#!/bin/bash
# ========================================
# Blinko 选择性数据导入执行脚本
# 创建时间: 2025-07-16
# 使用方法: ./import_blinko_data.sh
# ========================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-blinko}
DB_USER=${DB_USER:-postgres}
BACKUP_FILE="backup_20250318.sql"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE} Blinko 选择性数据导入工具${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查备份文件是否存在
if [ ! -f "$BACKUP_FILE" ]; then
    echo -e "${RED}❌ 错误: 找不到备份文件 $BACKUP_FILE${NC}"
    exit 1
fi

# 检查数据库连接
echo -e "${YELLOW}🔍 检查数据库连接...${NC}"
if ! pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME; then
    echo -e "${RED}❌ 错误: 无法连接到数据库${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 数据库连接正常${NC}"

# 函数：执行SQL命令
execute_sql() {
    local description="$1"
    local sql="$2"
    
    echo -e "${YELLOW}📝 $description...${NC}"
    if psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "$sql" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $description 完成${NC}"
    else
        echo -e "${RED}❌ $description 失败${NC}"
        return 1
    fi
}

# 函数：提取并导入数据
import_data_section() {
    local table_name="$1"
    local start_pattern="$2"
    local end_pattern="$3"
    local transform_cmd="$4"
    
    echo -e "${YELLOW}📥 导入 $table_name 数据...${NC}"
    
    # 提取数据段
    sed -n "/$start_pattern/,/$end_pattern/p" "$BACKUP_FILE" | \
    head -n -1 | \
    tail -n +2 | \
    $transform_cmd | \
    psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $table_name 数据导入完成${NC}"
    else
        echo -e "${RED}❌ $table_name 数据导入失败${NC}"
        return 1
    fi
}

# 阶段1: 备份现有配置
echo -e "\n${BLUE}=== 阶段1: 备份现有配置 ===${NC}"
execute_sql "备份现有配置" "
CREATE TABLE IF NOT EXISTS config_backup_$(date +%Y%m%d) AS 
SELECT * FROM config WHERE key IN (
    'NEXTAUTH_SECRET', 'aiApiKey', 'aiApiEndpoint', 'aiModel', 'aiModelProvider',
    'localCustomPath', 'objectStorage', 'embeddingApiKey', 'embeddingApiEndpoint'
);"

# 阶段2: 获取当前最大ID
echo -e "\n${BLUE}=== 阶段2: 分析当前数据状态 ===${NC}"
MAX_TAG_ID=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COALESCE(MAX(id), 0) FROM tag;" | tr -d ' ')
MAX_NOTE_ID=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COALESCE(MAX(id), 0) FROM notes;" | tr -d ' ')
MAX_ATTACHMENT_ID=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COALESCE(MAX(id), 0) FROM attachments;" | tr -d ' ')

echo -e "${GREEN}📊 当前数据状态:${NC}"
echo -e "   标签最大ID: $MAX_TAG_ID"
echo -e "   笔记最大ID: $MAX_NOTE_ID"
echo -e "   附件最大ID: $MAX_ATTACHMENT_ID"

# 阶段3: 导入标签数据
echo -e "\n${BLUE}=== 阶段3: 导入标签数据 ===${NC}"

# 创建临时文件包含所有标签数据
cat > /tmp/import_tags.sql << EOF
-- 导入标签数据，调整ID避免冲突
INSERT INTO tag (name, icon, parent, "createdAt", "updatedAt", "accountId", "sortOrder")
VALUES
-- 顶级标签 (parent=0)
('Welcome', '🎉', 0, '2025-02-27 23:52:53.341+08', '2025-02-27 23:52:53.341+08', 1, 0),
('p', '', 0, '2025-02-27 23:56:07.527+08', '2025-02-27 23:56:07.527+08', 1, 0),
('i', '', 0, '2025-03-09 00:04:35.552+08', '2025-03-09 00:04:35.552+08', 1, 0),
('P', '', 0, '2025-03-09 17:17:14.428+08', '2025-03-09 17:17:14.428+08', 1, 0),
('A', '', 0, '2025-03-10 10:19:43.673+08', '2025-03-10 10:19:43.673+08', 1, 0),
('知识管理', '', 0, '2025-03-10 23:07:27.867+08', '2025-03-10 23:07:27.867+08', 1, 0),
('稍后读', '', 0, '2025-03-11 17:21:49.038+08', '2025-03-11 17:21:49.038+08', 1, 0),
('api', '', 0, '2025-03-11 17:37:18.014+08', '2025-03-11 17:37:18.014+08', 1, 0),
('剪辑', '', 0, '2025-03-12 21:33:15.502+08', '2025-03-12 21:33:15.502+08', 1, 0),
('I', '', 0, '2025-03-12 21:35:50.835+08', '2025-03-12 21:35:50.835+08', 1, 0),
('待办', '', 0, '2025-03-13 10:28:46.008+08', '2025-03-13 10:28:46.008+08', 1, 0),
('R', '', 0, '2025-03-13 19:47:30.125+08', '2025-03-13 19:47:30.125+08', 1, 0),
('闪念', '', 0, '2025-03-13 23:14:32.084+08', '2025-03-13 23:14:32.084+08', 1, 0),
('翻译', '', 0, '2025-03-16 18:19:08.426+08', '2025-03-16 18:19:08.426+08', 1, 0),
('经验', '', 0, '2025-03-17 09:16:01.861+08', '2025-03-17 09:16:01.861+08', 1, 0),
('小火箭', '', 0, '2025-03-18 21:03:01.434+08', '2025-03-18 21:03:01.434+08', 1, 0)
ON CONFLICT (name) DO NOTHING;
EOF

psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f /tmp/import_tags.sql

# 阶段4: 导入重要笔记数据
echo -e "\n${BLUE}=== 阶段4: 导入笔记数据 ===${NC}"

# 提取并修改笔记数据，只导入重要的笔记
grep -A 1000 "COPY public.notes" "$BACKUP_FILE" | \
grep -B 1000 "^\\\\.$" | \
head -n -1 | \
tail -n +2 | \
# 过滤掉已删除的笔记 (isRecycle=t)
grep -v "	t	" | \
# 修改accountId为1
sed 's/\t1\t/\t1\t/g' | \
while IFS=$'\t' read -r id type content isArchived isRecycle isShare isTop sharePassword metadata createdAt updatedAt isReviewed accountId rest; do
    echo "INSERT INTO notes (type, content, \"isArchived\", \"isRecycle\", \"isShare\", \"isTop\", \"sharePassword\", metadata, \"createdAt\", \"updatedAt\", \"isReviewed\", \"accountId\", \"shareEncryptedUrl\", \"shareExpiryDate\", \"shareMaxView\", \"shareViewCount\") VALUES ($type, '$content', $isArchived, $isRecycle, $isShare, $isTop, '$sharePassword', '$metadata', '$createdAt', '$updatedAt', $isReviewed, 1, '', NULL, 0, 0);"
done > /tmp/import_notes.sql

psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f /tmp/import_notes.sql

# 阶段5: 重置序列
echo -e "\n${BLUE}=== 阶段5: 重置序列计数器 ===${NC}"
execute_sql "重置标签序列" "SELECT setval('tag_id_seq', (SELECT MAX(id) FROM tag));"
execute_sql "重置笔记序列" "SELECT setval('notes_id_seq', (SELECT MAX(id) FROM notes));"
execute_sql "重置附件序列" "SELECT setval('attachments_id_seq', (SELECT COALESCE(MAX(id), 1) FROM attachments));"

# 阶段6: 验证导入结果
echo -e "\n${BLUE}=== 阶段6: 验证导入结果 ===${NC}"
echo -e "${GREEN}📊 导入统计:${NC}"

TAGS_COUNT=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM tag;" | tr -d ' ')
NOTES_COUNT=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM notes;" | tr -d ' ')
ACTIVE_NOTES=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM notes WHERE \"isRecycle\" = false;" | tr -d ' ')

echo -e "   总标签数: $TAGS_COUNT"
echo -e "   总笔记数: $NOTES_COUNT"
echo -e "   有效笔记数: $ACTIVE_NOTES"

# 清理临时文件
rm -f /tmp/import_tags.sql /tmp/import_notes.sql

echo -e "\n${GREEN}🎉 数据导入完成！${NC}"
echo -e "${YELLOW}⚠️  请注意:${NC}"
echo -e "   1. 附件文件需要手动复制到正确位置"
echo -e "   2. 请测试标签和笔记功能是否正常"
echo -e "   3. 建议备份当前数据库状态"
echo -e "\n${BLUE}========================================${NC}"
