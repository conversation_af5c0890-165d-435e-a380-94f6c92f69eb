# docker-compose-1.0.5.yml
version: '3'

networks:
  blinko-network:
    driver: bridge

volumes:
  postgres-data:
    external: true
    name: blinko_blinko-restored-data

services:
  blinko-website:
    container_name: blinko-website
    image: blinkospace/blinko:1.0.5
    environment:
      NODE_ENV: production
      NEXTAUTH_URL: http://localhost:1111
      NEXT_PUBLIC_BASE_URL: http://localhost:1111
      NEXTAUTH_SECRET: my_ultra_secure_nextauth_secret
      DATABASE_URL: ****************************************************/postgres
    depends_on:
      postgres:
        condition: service_healthy
    restart: always
    ports:
      - 1111:1111
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://blinko-website:1111/"]
      interval: 30s 
      timeout: 10s  
      retries: 5
      start_period: 30s
    networks:
      - blinko-network

  postgres:
    image: postgres:14
    container_name: blinko-postgres
    restart: always
    ports:
      - 5432:5432
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: mysecretpassword
    healthcheck:
      test:
        ["CMD", "pg_isready", "-U", "postgres", "-d", "postgres"]
      interval: 5s
      timeout: 10s
      retries: 5
    networks:
      - blinko-network
    volumes:
      - postgres-data:/var/lib/postgresql/data
