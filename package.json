{"name": "blinko-monorepo", "version": "1.1.2", "private": true, "packageManager": "bun@1.2.8", "workspaces": ["app", "server"], "scripts": {"dev": "cd app && bun run tauri dev", "build:web": "turbo run build:web", "test": "turbo run test", "start": "cd server && bun run start", "clean": "turbo run clean && rm -rf node_modules", "dev:backend": "dotenv turbo run dev --filter=@blinko/backend", "dev:frontend": "dotenv turbo run dev --filter=@blinko/frontend", "postinstall": "turbo run prisma:generate --filter=@blinko/backend", "setup": "bun install", "prisma:generate": "cd prisma && prisma generate", "prisma:migrate:dev": "cd prisma && prisma migrate dev", "prisma:migrate:deploy": "cd prisma && prisma migrate deploy", "prisma:studio": "cd prisma && prisma studio", "build:seed": "tsup prisma/seed.ts --outDir dist", "seed": "bun dist/seed.js", "build:blinko:types": "tsc -p tsconfig.blinko.json"}, "devDependencies": {"@types/lru-cache": "^7.10.10", "concurrently": "^9.1.2", "eslint": "^8.56.0", "prettier": "^3.2.5", "turbo": "latest", "typescript": "^5.1.6", "vite-plugin-pwa": "^1.0.0", "workbox-window": "^7.3.0"}, "engines": {"bun": ">=1.0.0", "node": ">=20.0.0"}, "dependencies": {"@tauri-apps/plugin-process": "^2.2.1", "@tauri-apps/plugin-updater": "^2.7.1", "dotenv": "^16.5.0", "lru-cache": "^11.1.0", "react-i18next": "^15.4.1", "react-simple-pull-to-refresh": "^1.3.3", "wait-on": "^8.0.3"}}