#!/bin/bash

echo "🚀 开始 Notion 同步功能测试"
echo "================================"

# 检查依赖
echo "📦 检查依赖..."
if ! command -v bun &> /dev/null; then
    echo "❌ 错误：未找到 bun，请先安装 bun"
    exit 1
fi

# 构建项目
echo "🔨 构建项目..."
if ! bun run build:web; then
    echo "❌ 构建失败，请检查错误信息"
    exit 1
fi

echo "✅ 构建成功！"

# 提示用户
echo ""
echo "🧪 准备启动测试服务..."
echo "⚠️  注意：测试服务将使用端口 1111"
echo "⚠️  这可能与您的 Docker 服务冲突"
echo ""
echo "建议操作："
echo "1. 先停止 Docker 服务：docker-compose down"
echo "2. 启动测试：bun start"
echo "3. 访问：http://ccnu.me:1111"
echo "4. 测试完成后：Ctrl+C 停止"
echo "5. 恢复 Docker：docker-compose up -d"
echo ""

read -p "是否继续启动测试服务？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 启动测试服务..."
    echo "📝 访问 http://ccnu.me:1111 进行测试"
    echo "🔧 进入 设置 → 任务管理 → Notion 同步设置"
    echo "🔑 输入您的 API Token 并测试连接"
    echo ""
    echo "按 Ctrl+C 停止服务"
    echo "================================"
    
    # 启动服务
    bun start
else
    echo "❌ 测试取消"
    echo ""
    echo "💡 您也可以直接构建 Docker 镜像："
    echo "   docker build -t blinko-with-notion:latest ."
    echo "   # 然后修改 docker-compose.yml 中的镜像名"
    echo "   # image: blinko-with-notion:latest"
    echo "   docker-compose up -d"
fi
