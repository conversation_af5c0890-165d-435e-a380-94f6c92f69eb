generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x", "linux-arm64-openssl-3.0.x", "linux-musl-arm64-openssl-3.0.x"]
}

// generator zod {
//   provider = "zod-prisma-types"
// }

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

model accounts {
  id            Int             @id @default(autoincrement())
  name          String          @default("") @db.VarChar
  nickname      String          @default("") @db.VarChar
  password      String          @default("") @db.VarChar
  image         String          @default("") @db.VarChar
  apiToken      String          @default("") @db.VarChar
  description   String          @default("") @db.Text
  note          Int             @default(0)
  role          String          @default("") @db.VarChar
  loginType     String          @default("") @db.VarChar
  linkAccountId Int?
  createdAt     DateTime        @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime        @updatedAt @db.Timestamptz(6)
  notes         notes[]
  configs       config[]
  tags          tag[]
  comments      comments[]
  attachments   attachments[]
  follows       follows[]
  notifications notifications[]
  conversations conversation[]
  sharedNotes   noteInternalShare[]
}

model attachments {
  id            Int      @id @default(autoincrement())
  isShare       Boolean  @default(false)
  sharePassword String   @default("") @db.VarChar
  name          String   @default("") @db.VarChar
  path          String   @default("") @db.VarChar
  size          Decimal  @default(0) @db.Decimal
  type          String   @default("") @db.VarChar
  noteId        Int?
  accountId     Int?
  sortOrder     Int      @default(0)
  createdAt     DateTime @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime @updatedAt @db.Timestamptz(6)
  perfixPath    String?  @default("") @db.VarChar // folder1,folder2,folder3
  depth         Int?

  note    notes?    @relation(fields: [noteId], references: [id])
  account accounts? @relation(fields: [accountId], references: [id])
}

model config {
  id     Int       @id @default(autoincrement())
  key    String    @default("") @db.VarChar
  config Json?     @db.Json
  userId Int?
  user   accounts? @relation(fields: [userId], references: [id])
}

model notes {
  id                Int             @id @default(autoincrement())
  type              Int             @default(0)
  content           String          @default("") @db.VarChar
  isArchived        Boolean         @default(false)
  isRecycle         Boolean         @default(false)
  isShare           Boolean         @default(false)
  isTop             Boolean         @default(false)
  isReviewed        Boolean         @default(false)
  sharePassword     String          @default("") @db.VarChar
  shareEncryptedUrl String?         @db.VarChar
  shareExpiryDate   DateTime?       @db.Timestamptz(6)
  shareMaxView      Int?            @default(0)
  shareViewCount    Int?            @default(0)
  metadata          Json?           @db.Json
  accountId         Int?
  createdAt         DateTime        @default(now()) @db.Timestamptz(6)
  updatedAt         DateTime        @updatedAt @db.Timestamptz(6)
  attachments       attachments[]
  tags              tagsToNote[]
  account           accounts?       @relation(fields: [accountId], references: [id])
  referencedBy      noteReference[] @relation("ReferencedNote")
  references        noteReference[] @relation("ReferencingNote")
  comments          comments[]      @relation("NoteComments")
  histories         noteHistory[]
  internalShares    noteInternalShare[]
}

model comments {
  id        Int      @id @default(autoincrement())
  content   String   @db.Text
  accountId Int?
  guestName String?  @db.VarChar
  guestIP   String?  @db.VarChar
  guestUA   String?  @db.VarChar
  noteId    Int
  parentId  Int?
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)

  note    notes      @relation("NoteComments", fields: [noteId], references: [id])
  account accounts?  @relation(fields: [accountId], references: [id])
  parent  comments?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies comments[] @relation("CommentReplies")
}

model tag {
  id         Int          @id @default(autoincrement())
  name       String       @default("") @db.VarChar
  icon       String       @default("") @db.VarChar
  parent     Int          @default(0)
  accountId  Int?
  createdAt  DateTime     @default(now()) @db.Timestamptz(6)
  updatedAt  DateTime     @updatedAt @db.Timestamptz(6)
  tagsToNote tagsToNote[]
  sortOrder  Int          @default(0)
  account    accounts?    @relation(fields: [accountId], references: [id])
}

model tagsToNote {
  id     Int @default(autoincrement())
  noteId Int @default(0)
  tagId  Int @default(0)

  note notes @relation(fields: [noteId], references: [id])
  tag  tag   @relation(fields: [tagId], references: [id])

  @@id([noteId, tagId])
}

model scheduledTask {
  name      String   @id
  schedule  String
  lastRun   DateTime @default(now())
  isSuccess Boolean  @default(true)
  isRunning Boolean  @default(false)
  output    Json?    @db.Json
}

model noteReference {
  id         Int      @id @default(autoincrement())
  fromNoteId Int
  toNoteId   Int
  createdAt  DateTime @default(now()) @db.Timestamptz(6)

  fromNote notes @relation("ReferencingNote", fields: [fromNoteId], references: [id])
  toNote   notes @relation("ReferencedNote", fields: [toNoteId], references: [id])

  @@unique([fromNoteId, toNoteId])
}

model follows {
  id          Int     @id @default(autoincrement())
  siteName    String? @db.VarChar
  siteUrl     String  @db.VarChar
  siteAvatar  String? @db.VarChar
  description String? @db.Text

  createdAt  DateTime @default(now()) @db.Timestamptz(6)
  updatedAt  DateTime @updatedAt @db.Timestamptz(6)
  followType String   @default("following") @db.VarChar //  "following" or "follower"

  accountId Int
  account   accounts @relation(fields: [accountId], references: [id])
}

model notifications {
  id        Int      @id @default(autoincrement())
  type      String   @db.VarChar // follow, comment, mention 等
  title     String   @db.VarChar
  content   String   @db.Text
  metadata  Json?    @db.Json
  isRead    Boolean  @default(false)
  accountId Int
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)

  account accounts @relation(fields: [accountId], references: [id])
}

model cache {
  id        Int      @id @default(autoincrement())
  key       String   @unique @db.VarChar
  value     Json     @db.Json
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)
}

model plugin {
  id        Int      @id @default(autoincrement())
  metadata  Json     @db.Json
  path      String   @db.VarChar
  isUse     Boolean  @default(true)
  isDev     Boolean  @default(false)
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)
}

model conversation {
  id        Int       @id @default(autoincrement())
  title     String    @default("") @db.VarChar(255)
  isShare   Boolean   @default(false)
  accountId Int
  createdAt DateTime  @default(now()) @db.Timestamptz(6)
  updatedAt DateTime  @updatedAt @db.Timestamptz(6)
  messages  message[]

  account accounts @relation(fields: [accountId], references: [id])

  @@index([accountId])
}

model message {
  id             Int      @id @default(autoincrement())
  content        String   @db.Text
  role           String   @db.VarChar(50) // user or assistant
  conversationId Int
  createdAt      DateTime @default(now()) @db.Timestamptz(6)
  updatedAt      DateTime @updatedAt @db.Timestamptz(6)
  metadata       Json?    @db.Json

  conversation conversation @relation(fields: [conversationId], references: [id])

  @@index([conversationId])
}

model noteHistory {
  id        Int      @id @default(autoincrement())
  noteId    Int
  content   String   @db.Text
  metadata  Json?    @db.Json
  version   Int
  accountId Int?
  createdAt DateTime @default(now()) @db.Timestamptz(6)

  note notes @relation(fields: [noteId], references: [id], onDelete: Cascade)

  @@index([noteId])
  @@index([accountId])
}

model noteInternalShare {
  id        Int      @id @default(autoincrement())
  noteId    Int
  accountId Int
  canEdit   Boolean  @default(true)
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)

  note    notes    @relation(fields: [noteId], references: [id], onDelete: Cascade)
  account accounts @relation(fields: [accountId], references: [id])

  @@unique([noteId, accountId])
  @@index([noteId])
  @@index([accountId])
}

model session {
  id        String   @id
  sid       String   @unique
  data      String   @db.Text
  expiresAt DateTime
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)

  @@index([expiresAt])
}
