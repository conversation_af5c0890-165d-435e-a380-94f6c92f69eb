-- ========================================
-- Blinko 选择性数据导入计划 (ccnu.me环境)
-- 创建时间: 2025-07-16
-- 说明: 从backup_20250318.sql中选择性导入数据到ccnu.me服务
-- 执行方式: docker exec blinko-postgres psql -U postgres -d blinko -f thisfile.sql
-- ========================================

-- 阶段1: 备份当前重要配置
-- ========================================
CREATE TABLE IF NOT EXISTS config_backup AS 
SELECT * FROM config WHERE key IN (
    'NEXTAUTH_SECRET', 'aiApiKey', 'aiApiEndpoint', 'aiModel', 'aiModelProvider',
    'localCustomPath', 'objectStorage', 'embeddingApiKey', 'embeddingApiEndpoint'
);

-- 阶段2: 用户账户导入（可选）
-- ========================================
-- 注意: 如果需要导入用户账户，请取消注释以下语句
-- 检查是否已存在Cotton用户，如果不存在则导入
/*
INSERT INTO accounts (id, name, nickname, password, image, "apiToken", note, role, "createdAt", "updatedAt", "loginType", "linkAccountId", description)
SELECT 1, 'Cotton', 'Cotton', 'pbkdf2:531683872cc50d0c077610277b383b13:2a3597f0423fc9c4740f513c7043329d5d62c03cc9521376e2b18a0fc2733880175eebcd03dfd5f21415881e14cd3091aaf4117dcbd7d1dfc48e0dc54b943b6e', '', 'eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..A3SABkHSW5nHBN7J.9Xj93PzoiEie8V9PwM7dnj-5fMFy7M8SwJf-tT25VxUJjtA_KXo-Nh2et8Eblt8WJWROsy0F4y_Q6spIP99tXn9dempStMPDlNXynezCDQAFubhLhQ9owqeLiz3gMI_gUpfQmIoWlPKw7jLYJH_Tr153rDT0dHwEwILOOtFN.qpS-8mlotMNaAQxI9lxPRg', '0', 'superadmin', '2025-02-27 23:52:53.307+08', '2025-02-27 23:52:53.32+08', '', NULL, ''
WHERE NOT EXISTS (SELECT 1 FROM accounts WHERE name = 'Cotton');
*/

-- 阶段3: 标签数据导入
-- ========================================
-- 获取当前最大tag ID
DO $$
DECLARE
    max_tag_id INTEGER;
    tag_offset INTEGER;
BEGIN
    SELECT COALESCE(MAX(id), 0) INTO max_tag_id FROM tag;
    tag_offset := max_tag_id;
    
    -- 临时表存储ID映射
    DROP TABLE IF EXISTS tag_id_mapping;
    CREATE TEMP TABLE tag_id_mapping (
        old_id INTEGER,
        new_id INTEGER
    );
    
    -- 插入标签数据（按parent层级顺序）
    -- 第一层：parent=0的标签
    INSERT INTO tag (name, icon, parent, "createdAt", "updatedAt", "accountId", "sortOrder")
    SELECT name, icon, 0, "createdAt", "updatedAt", 1, "sortOrder"
    FROM (VALUES 
        ('Welcome', '🎉', '2025-02-27 23:52:53.341+08', '2025-02-27 23:52:53.341+08', 0),
        ('p', '', '2025-02-27 23:56:07.527+08', '2025-02-27 23:56:07.527+08', 0),
        ('i', '', '2025-03-09 00:04:35.552+08', '2025-03-09 00:04:35.552+08', 0),
        ('P', '', '2025-03-09 17:17:14.428+08', '2025-03-09 17:17:14.428+08', 0),
        ('A', '', '2025-03-10 10:19:43.673+08', '2025-03-10 10:19:43.673+08', 0),
        ('知识管理', '', '2025-03-10 23:07:27.867+08', '2025-03-10 23:07:27.867+08', 0),
        ('稍后读', '', '2025-03-11 17:21:49.038+08', '2025-03-11 17:21:49.038+08', 0),
        ('api', '', '2025-03-11 17:37:18.014+08', '2025-03-11 17:37:18.014+08', 0),
        ('剪辑', '', '2025-03-12 21:33:15.502+08', '2025-03-12 21:33:15.502+08', 0),
        ('I', '', '2025-03-12 21:35:50.835+08', '2025-03-12 21:35:50.835+08', 0),
        ('R', '', '2025-03-13 19:47:30.125+08', '2025-03-13 19:47:30.125+08', 0),
        ('闪念', '', '2025-03-13 23:14:32.084+08', '2025-03-13 23:14:32.084+08', 0),
        ('翻译', '', '2025-03-16 18:19:08.426+08', '2025-03-16 18:19:08.426+08', 0),
        ('经验', '', '2025-03-17 09:16:01.861+08', '2025-03-17 09:16:01.861+08', 0),
        ('小火箭', '', '2025-03-18 21:03:01.434+08', '2025-03-18 21:03:01.434+08', 0)
    ) AS t(name, icon, "createdAt", "updatedAt", "sortOrder")
    WHERE NOT EXISTS (SELECT 1 FROM tag WHERE tag.name = t.name);
    
END $$;

-- 阶段4: 笔记数据导入
-- ========================================
-- 获取当前最大note ID
DO $$
DECLARE
    max_note_id INTEGER;
    note_offset INTEGER;
    target_account_id INTEGER := 1; -- 假设导入到ID为1的账户
BEGIN
    SELECT COALESCE(MAX(id), 0) INTO max_note_id FROM notes;
    note_offset := max_note_id;
    
    -- 插入有效笔记（排除已删除的）
    INSERT INTO notes (type, content, "isArchived", "isRecycle", "isShare", "isTop", "sharePassword", metadata, "createdAt", "updatedAt", "isReviewed", "accountId", "shareEncryptedUrl", "shareExpiryDate", "shareMaxView", "shareViewCount")
    SELECT 
        type, content, "isArchived", false, "isShare", "isTop", "sharePassword", 
        metadata, "createdAt", "updatedAt", "isReviewed", target_account_id,
        "shareEncryptedUrl", "shareExpiryDate", "shareMaxView", "shareViewCount"
    FROM (VALUES
        -- 只导入重要的、未删除的笔记
        (0, '#Welcome

Welcome to Blinko!

Whether you''re capturing ideas, taking meeting notes, or planning your schedule, Blinko provides an easy and efficient way to manage it all. Here, you can create, edit, and share notes anytime, anywhere, ensuring you never lose a valuable thought.', false, false, false, false, '', '{"isIndexed":true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, '', NULL, 0, 0),
        (0, '#Welcome/To-Do

* Create a blinko
* Create a note
* Upload file', false, false, false, false, '', '{"isIndexed":true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, '', NULL, 0, 0),
        (0, 'https://github.com/blinko-space/blinko/', false, false, false, false, '', '{"isIndexed":true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, '', NULL, 0, 0)
        -- 可以继续添加更多重要笔记...
    ) AS n(type, content, "isArchived", "isRecycle", "isShare", "isTop", "sharePassword", metadata, "createdAt", "updatedAt", "isReviewed", "shareEncryptedUrl", "shareExpiryDate", "shareMaxView", "shareViewCount");
    
END $$;

-- 阶段5: 序列重置
-- ========================================
-- 重置所有相关序列到正确值
SELECT setval('tag_id_seq', (SELECT MAX(id) FROM tag));
SELECT setval('notes_id_seq', (SELECT MAX(id) FROM notes));
SELECT setval('attachments_id_seq', (SELECT COALESCE(MAX(id), 1) FROM attachments));
SELECT setval('"tagsToNote_id_seq"', (SELECT COALESCE(MAX(id), 1) FROM "tagsToNote"));

-- 阶段6: 数据完整性验证
-- ========================================
-- 验证脚本
SELECT 
    'tags' as table_name, 
    COUNT(*) as imported_count,
    MIN(id) as min_id,
    MAX(id) as max_id
FROM tag
UNION ALL
SELECT 
    'notes' as table_name, 
    COUNT(*) as imported_count,
    MIN(id) as min_id,
    MAX(id) as max_id
FROM notes
UNION ALL
SELECT 
    'config_backup' as table_name, 
    COUNT(*) as imported_count,
    MIN(id) as min_id,
    MAX(id) as max_id
FROM config_backup;

-- 清理临时表
DROP TABLE IF EXISTS config_backup;

-- ========================================
-- 导入完成提示
-- ========================================
SELECT 'Blinko数据导入完成！请访问 https://ccnu.me 查看结果并测试功能。' as status;
