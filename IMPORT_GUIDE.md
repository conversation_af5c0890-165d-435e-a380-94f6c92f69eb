# 🚀 Blinko 选择性数据导入完整方案

## 📋 方案概述

本方案提供了一个安全、可控的方式来从 `backup_20250318.sql` 中选择性导入数据到现有的 Blinko 系统，确保不破坏现有配置和数据。

## 🎯 导入策略

### 核心原则
- ✅ **保护现有配置** - 不覆盖系统设置和用户配置
- ✅ **选择性导入** - 只导入有价值的数据
- ✅ **ID冲突处理** - 自动处理主键冲突
- ✅ **数据完整性** - 确保外键关系正确
- ✅ **可回滚** - 提供备份和恢复机制

### 数据分析结果
从备份文件分析得出：
- **用户账户**: 1个（Cotton用户）
- **笔记数据**: 34条（其中多条已删除）
- **标签数据**: 43个标签（多级结构）
- **附件数据**: 15个附件文件
- **配置数据**: 23项系统配置

## 📝 实施步骤

### 方式一：自动化脚本导入（推荐）

```bash
# 1. 确保在blinko目录下
cd /root/blinko

# 2. 设置数据库连接信息（如果需要）
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=blinko
export DB_USER=postgres

# 3. 执行导入脚本
./import_blinko_data.sh
```

### 方式二：手动SQL导入

#### 步骤1: 连接数据库并备份配置
```sql
-- 备份现有重要配置
CREATE TABLE config_backup_20250716 AS 
SELECT * FROM config WHERE key IN (
    'NEXTAUTH_SECRET', 'aiApiKey', 'aiApiEndpoint', 'aiModel', 'aiModelProvider',
    'localCustomPath', 'objectStorage', 'embeddingApiKey', 'embeddingApiEndpoint'
);
```

#### 步骤2: 导入标签数据
```sql
-- 导入主要标签（避免重复）
INSERT INTO tag (name, icon, parent, "createdAt", "updatedAt", "accountId", "sortOrder")
VALUES
('Welcome', '🎉', 0, NOW(), NOW(), 1, 0),
('知识管理', '', 0, NOW(), NOW(), 1, 0),
('稍后读', '', 0, NOW(), NOW(), 1, 0),
('翻译', '', 0, NOW(), NOW(), 1, 0),
('经验', '', 0, NOW(), NOW(), 1, 0)
ON CONFLICT (name) DO NOTHING;
```

#### 步骤3: 导入重要笔记
```sql
-- 导入示例笔记
INSERT INTO notes (type, content, "isArchived", "isRecycle", "isShare", "isTop", metadata, "createdAt", "updatedAt", "accountId")
VALUES
(0, '#Welcome

Welcome to Blinko!

Whether you''re capturing ideas, taking meeting notes, or planning your schedule, Blinko provides an easy and efficient way to manage it all.', 
false, false, false, false, '{"isIndexed":true}', NOW(), NOW(), 1),

(0, '#知识管理
重要的事情做备份、分摊影响（把鸡蛋放在几个篮子里）、多方案思维（尽可能做几个方案，即使一个方案不成，还有额外的方案做弥补）', 
false, false, false, false, '{"isIndexed":true}', NOW(), NOW(), 1);
```

#### 步骤4: 重置序列
```sql
-- 重置序列到正确值
SELECT setval('tag_id_seq', (SELECT MAX(id) FROM tag));
SELECT setval('notes_id_seq', (SELECT MAX(id) FROM notes));
SELECT setval('attachments_id_seq', (SELECT COALESCE(MAX(id), 1) FROM attachments));
```

## 🔍 完整数据列表

### 主要标签结构
```
顶级标签:
├── Welcome (🎉)
├── p (项目相关)
├── A (摄影、大模型等)
├── 知识管理
├── 稍后读
├── api
├── 翻译
├── 经验
├── 闪念
└── 小火箭

子标签示例:
├── p/
│   ├── 项目
│   └── 阅读/
│       └── ai时代思考法
├── A/
│   ├── 摄影
│   ├── 剪辑
│   └── 大模型/
│       ├── 知识库/
│       │   └── 嵌入模型
│       └── 数据漂移容忍度
└── R/
    ├── 无损音乐下载
    ├── 电子书下载
    └── TTS模型汇总
```

### 重要笔记内容
1. **Welcome系列** - Blinko介绍和功能说明
2. **知识管理** - 个人知识管理方法
3. **AI相关** - 大模型、嵌入模型技术文档
4. **工具资源** - API密钥、下载资源等
5. **经验总结** - 学习和工作经验

## ⚠️ 注意事项

### 数据保护
- 现有的 `config` 表数据将被保护，不会被覆盖
- 现有的用户账户和权限设置保持不变
- 现有的 `scheduledTask` 定时任务配置保持不变

### 附件处理
```bash
# 附件文件需要手动处理
# 原路径: E:\blinko-data\
# 目标路径: 根据系统配置的localCustomPath

# 示例附件列表:
# - pic01.png ~ pic06.png (图片文件)
# - story.txt (文本文件)
# - 46efdeba9c1b62fc5a0964a2101e8f3_1741832915263.jpg
# - image_*.png (截图文件)
```

### ID映射处理
脚本会自动处理ID冲突：
- 标签ID: 从当前最大ID+1开始
- 笔记ID: 从当前最大ID+1开始
- 关联关系: 自动重新建立

## 🔧 故障排除

### 常见问题
1. **权限问题**: 确保数据库用户有足够权限
2. **连接问题**: 检查数据库连接参数
3. **编码问题**: 确保使用UTF-8编码
4. **空间问题**: 检查数据库存储空间

### 验证命令
```sql
-- 检查导入结果
SELECT 'tags' as table_name, COUNT(*) as count FROM tag
UNION ALL
SELECT 'notes' as table_name, COUNT(*) as count FROM notes
UNION ALL
SELECT 'active_notes' as table_name, COUNT(*) as count FROM notes WHERE "isRecycle" = false;

-- 检查标签关联
SELECT t.name, COUNT(ttn."noteId") as note_count
FROM tag t
LEFT JOIN "tagsToNote" ttn ON t.id = ttn."tagId"
GROUP BY t.id, t.name
ORDER BY note_count DESC;
```

## 🚀 后续操作

### 数据清理
```sql
-- 删除临时备份表（确认无误后）
DROP TABLE IF EXISTS config_backup_20250716;
```

### 功能测试
1. 测试标签创建和编辑
2. 测试笔记创建和搜索
3. 测试标签与笔记的关联
4. 测试附件上传功能

### 性能优化
```sql
-- 重建索引（如果需要）
REINDEX TABLE tag;
REINDEX TABLE notes;
REINDEX TABLE "tagsToNote";
```

## 📞 支持信息

如果遇到问题，请检查：
1. 数据库日志
2. 应用程序日志
3. 网络连接状态
4. 存储空间

---

**⚡ 提示**: 建议在生产环境操作前，先在测试环境验证整个导入流程。
