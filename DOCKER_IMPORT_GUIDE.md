# 🐳 Blinko Docker环境数据导入指南

## 🎯 当前环境状态

您的Docker环境已正常运行：
- **PostgreSQL容器**: `blinko-postgres` ✅
- **Blinko应用容器**: `blinko-website` ✅  
- **当前数据**: 41个标签，27条有效笔记

## 🚀 三种导入方式

### 方式一：简化快速导入（推荐）
```bash
# 一键导入精选的标签和笔记
./simple_docker_import.sh
```
**特点**: 最简单，导入预设的优质内容

### 方式二：完整Docker导入
```bash
# 功能完整的Docker导入脚本
./docker_import.sh
```
**特点**: 功能完整，支持文件导入和详细配置

### 方式三：直接SQL命令
```bash
# 手动执行SQL命令
docker exec blinko-postgres psql -U postgres -d blinko -c "你的SQL语句"
```
**特点**: 最灵活，适合高级用户

## 📋 导入内容预览

### 标签体系（12个精选标签）
```
🎉 Welcome      - 欢迎和介绍内容
📚 知识管理      - 知识管理方法论
📖 稍后读       - 待阅读内容
🌐 翻译         - 翻译相关资源
💡 经验         - 个人经验总结
🔑 api          - API密钥和接口
⚡ 闪念         - 快速想法记录
📁 项目         - 项目管理内容
🤖 AI           - AI相关技术
📦 资源         - 各类资源收集
🔧 工具         - 开发和效率工具
🎓 学习         - 学习方法和心得
```

### 笔记内容（5条精选笔记）
```
1. Welcome - Blinko功能介绍
2. 知识管理 - 核心原则和实践方法
3. 经验 - 学习与工作的思考
4. AI - 现代开发技术栈
5. 工具 - 开发和效率工具推荐
```

## 🛡️ 安全保障

- ✅ **不删除现有数据** - 只添加新内容
- ✅ **冲突处理** - 自动避免重复标签
- ✅ **容器隔离** - 在Docker环境中安全执行
- ✅ **可回滚** - 可以通过备份恢复

## 🔧 使用步骤

### 1. 检查环境
```bash
# 确认容器运行状态
docker ps | grep blinko
```

### 2. 执行导入
```bash
# 推荐使用简化导入
./simple_docker_import.sh
```

### 3. 验证结果
```bash
# 访问Web界面
https://ccnu.me

# 或查看数据库统计
docker exec blinko-postgres psql -U postgres -d blinko -c "
SELECT 'Tags: ' || COUNT(*) FROM tag
UNION ALL
SELECT 'Notes: ' || COUNT(*) FROM notes WHERE \"isRecycle\" = false;
"
```

## 🔍 故障排除

### 常见问题

**1. 容器未运行**
```bash
# 启动服务
docker-compose up -d
```

**2. 数据库连接失败**
```bash
# 检查容器日志
docker logs blinko-postgres
```

**3. 权限问题**
```bash
# 给脚本添加执行权限
chmod +x *.sh
```

**4. 端口冲突**
```bash
# 检查端口占用
docker ps --format "table {{.Names}}\t{{.Ports}}"
```

### 手动验证命令

```bash
# 检查标签数量
docker exec blinko-postgres psql -U postgres -d blinko -c "SELECT COUNT(*) FROM tag;"

# 检查笔记数量  
docker exec blinko-postgres psql -U postgres -d blinko -c "SELECT COUNT(*) FROM notes WHERE \"isRecycle\" = false;"

# 查看最新导入的标签
docker exec blinko-postgres psql -U postgres -d blinko -c "SELECT name, icon FROM tag ORDER BY \"createdAt\" DESC LIMIT 10;"
```

## 📱 导入后操作

### 1. 功能测试
- [ ] 打开 https://ccnu.me
- [ ] 检查标签是否显示正常
- [ ] 测试创建新笔记功能
- [ ] 验证搜索功能
- [ ] 测试标签筛选

### 2. 自定义调整
- [ ] 根据需要修改标签图标
- [ ] 添加个人常用标签
- [ ] 调整笔记分类
- [ ] 设置个性化配置

### 3. 数据备份
```bash
# 导入成功后建议备份
docker exec blinko-postgres pg_dump -U postgres blinko > backup_after_import.sql
```

## 💡 提示

- 导入过程中应用会自动重启以刷新缓存
- 如果界面没有立即更新，请刷新浏览器
- 标签和笔记都支持后续编辑和删除
- 可以随时添加更多个性化内容

---

**🎉 准备好了吗？运行 `./simple_docker_import.sh` 开始导入！**
