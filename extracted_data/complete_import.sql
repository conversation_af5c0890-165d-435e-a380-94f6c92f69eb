-- Blinko 完整数据导入脚本
-- 执行顺序: 1.备份 -> 2.标签 -> 3.笔记 -> 4.序列重置

-- 步骤1: 备份现有配置
CREATE TABLE IF NOT EXISTS config_backup AS 
SELECT * FROM config WHERE key IN (
    'NEXTAUTH_SECRET', 'aiApiKey', 'aiApiEndpoint', 'aiModel', 'aiModelProvider',
    'localCustomPath', 'objectStorage', 'embeddingApiKey', 'embeddingApiEndpoint'
);

-- 步骤2: 导入标签数据
\i extracted_data/tags_data.sql

-- 步骤3: 导入笔记数据  
\i extracted_data/notes_data.sql

-- 步骤4: 重置序列
SELECT setval('tag_id_seq', (SELECT MAX(id) FROM tag));
SELECT setval('notes_id_seq', (SELECT MAX(id) FROM notes));

-- 步骤5: 验证导入结果
SELECT 'Tags imported: ' || COUNT(*) FROM tag
UNION ALL
SELECT 'Notes imported: ' || COUNT(*) FROM notes
UNION ALL  
SELECT 'Active notes: ' || COUNT(*) FROM notes WHERE "isRecycle" = false;

-- 完成提示
SELECT '✅ Blinko数据导入完成！' as status;
