-- 重要笔记数据提取结果
-- 已过滤删除的笔记，只包含有价值内容

INSERT INTO notes (type, content, "isArchived", "isRecycle", "isShare", "isTop", "sharePassword", metadata, "createdAt", "updatedAt", "isReviewed", "accountId") VALUES

-- Welcome 系列笔记
(0, '#Welcome

Welcome to Blinko!

Whether you''re capturing ideas, taking meeting notes, or planning your schedule, Blinko provides an easy and efficient way to manage it all. Here, you can create, edit, and share notes anytime, anywhere, ensuring you never lose a valuable thought.', 
false, false, false, false, '', '{"isIndexed":true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1),

(0, '#Welcome/To-Do

* Create a blinko
* Create a note
* Upload file', 
false, false, false, false, '', '{"isIndexed":true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1),

-- 项目链接
(0, 'https://github.com/blinko-space/blinko/', 
false, false, false, false, '', '{"isIndexed":true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1),

-- 知识管理经验
(0, '#知识管理
重要的事情做备份、分摊影响（把鸡蛋放在几个篮子里）、多方案思维（尽可能做几个方案，即使一个方案不成，还有额外的方案做弥补）', 
false, false, false, false, '', '{"isIndexed":true}', '2025-03-10 23:07:27.859+08', '2025-03-10 23:07:27.859+08', false, 1),

-- AI安全相关
(1, '#P/AI/安全

https://gandalf.lakera.ai/baseline

根据提供的网页内容，这个网页属于一家名为 **Lakera** 的公司，专注于 **人工智能（AI）安全** 相关的产品和服务。以下是针对该网页的主要用途和内容的总结：

1. **Gandalf 游戏与安全挑战**：该网页介绍了一个名为 **Gandalf** 的"游戏"。玩家的目标是努力尝试让 Gandalf（一个 AI 系统）透露每个关卡的秘密密码。

2. **Lakera 的产品与服务**：**Lakera Guard**：这是该公司的一款产品，专注于保护生成式 AI（GenAI）的安全，以防止数据泄露、滥用或潜在的安全威胁。

简而言之，这个网页的用途：主要是展示 Lakera 公司在 AI 安全领域中的产品与服务，同时通过一个互动游戏（Gandalf）来吸引用户关注 AI 安全风险及其防护措施。', 
false, false, false, false, '', '{"isIndexed":true}', '2025-03-10 23:08:15.143+08', '2025-03-10 23:08:15.143+08', false, 1),

-- 思考方法论
(0, '#I/思考 

如果你想要做笔记，最好把三个要素都记下来
1. 产品出处（原文标题和链接)
2. 摘要内容（启发你灵感的内容是什么）
3. 你的思考（看到内容后给你的启发和灵感）
就和复盘的时候要把当时的背景和计划目标和实际完成情况记录一样，能够溯源', 
false, false, false, false, '', '{"isIndexed":true}', '2025-03-12 21:35:50.828+08', '2025-03-12 21:35:50.828+08', false, 1),

-- 技术栈笔记
(0, '#闪念 

ts + nextjs (t3 stack)+ react 就是大模型最擅长的框架（有必要了解一下这几个玩意）', 
false, false, false, false, '', '{"isIndexed":true}', '2025-03-13 23:14:32.077+08', '2025-03-13 23:14:32.077+08', false, 1),

-- 经验总结
(0, '#经验 "工作的时候也是需要学习大量的工作相关的知识和技能"以及"大学阶段比较有价值的是打基础"。我有很多技能并不是上课学来的，都是在实习项目中学到的，包括很多软实力软技能', 
false, false, false, false, '', '{"isIndexed":true}', '2025-03-17 09:16:01.847+08', '2025-03-17 09:16:01.847+08', false, 1);

-- 执行后记得重置序列:
-- SELECT setval('notes_id_seq', (SELECT MAX(id) FROM notes));
