-- 配置备份脚本
-- 在导入前执行，保护现有配置

CREATE TABLE IF NOT EXISTS config_backup_$(date +%Y%m%d_%H%M%S) AS 
SELECT * FROM config WHERE key IN (
    'NEXTAUTH_SECRET', 
    'aiApiKey', 
    'aiApiEndpoint', 
    'aiModel', 
    'aiModelProvider',
    'localCustomPath', 
    'objectStorage', 
    'embeddingApiKey', 
    'embeddingApiEndpoint',
    'theme',
    'themeColor',
    'themeForegroundColor'
);

-- 验证备份
SELECT 'Backup created with ' || COUNT(*) || ' config items' as backup_status 
FROM config_backup_$(date +%Y%m%d_%H%M%S);
