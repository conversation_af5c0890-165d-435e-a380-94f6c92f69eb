-- 标签数据提取结果
-- 包含43个标签的完整层级结构

INSERT INTO tag (name, icon, parent, "createdAt", "updatedAt", "accountId", "sortOrder") VALUES
-- 顶级标签 (parent=0)
('Welcome', '🎉', 0, '2025-02-27 23:52:53.341+08', '2025-02-27 23:52:53.341+08', 1, 0),
('p', '', 0, '2025-02-27 23:56:07.527+08', '2025-02-27 23:56:07.527+08', 1, 0),
('i', '', 0, '2025-03-09 00:04:35.552+08', '2025-03-09 00:04:35.552+08', 1, 0),
('P', '', 0, '2025-03-09 17:17:14.428+08', '2025-03-09 17:17:14.428+08', 1, 0),
('A', '', 0, '2025-03-10 10:19:43.673+08', '2025-03-10 10:19:43.673+08', 1, 0),
('知识管理', '', 0, '2025-03-10 23:07:27.867+08', '2025-03-10 23:07:27.867+08', 1, 0),
('稍后读', '', 0, '2025-03-11 17:21:49.038+08', '2025-03-11 17:21:49.038+08', 1, 0),
('api', '', 0, '2025-03-11 17:37:18.014+08', '2025-03-11 17:37:18.014+08', 1, 0),
('剪辑', '', 0, '2025-03-12 21:33:15.502+08', '2025-03-12 21:33:15.502+08', 1, 0),
('I', '', 0, '2025-03-12 21:35:50.835+08', '2025-03-12 21:35:50.835+08', 1, 0),
('待办', '', 0, '2025-03-13 10:28:46.008+08', '2025-03-13 10:28:46.008+08', 1, 0),
('R', '', 0, '2025-03-13 19:47:30.125+08', '2025-03-13 19:47:30.125+08', 1, 0),
('闪念', '', 0, '2025-03-13 23:14:32.084+08', '2025-03-13 23:14:32.084+08', 1, 0),
('翻译', '', 0, '2025-03-16 18:19:08.426+08', '2025-03-16 18:19:08.426+08', 1, 0),
('经验', '', 0, '2025-03-17 09:16:01.861+08', '2025-03-17 09:16:01.861+08', 1, 0),
('小火箭', '', 0, '2025-03-18 21:03:01.434+08', '2025-03-18 21:03:01.434+08', 1, 0);

-- 执行后记得重置序列:
-- SELECT setval('tag_id_seq', (SELECT MAX(id) FROM tag));
