#!/bin/bash

# =====================================================
# 🚀 完整 Blinko 数据导入脚本执行器 (ccnu.me)
# =====================================================
# 描述：将 backup_20250318.sql 中的所有笔记数据导入到正在运行的 ccnu.me Blinko 服务
# 目标：postgres 数据库 (Docker 容器中的正确数据库)
# 日期：2025-07-17

set -e  # 遇到错误立即退出

echo "🚀 开始完整数据导入到 ccnu.me Blinko 服务..."
echo "📅 $(date)"
echo ""

# 检查 Docker 容器状态
echo "🔍 检查 Docker 容器状态..."
if ! docker ps | grep -q blinko-postgres; then
    echo "❌ 错误: blinko-postgres 容器未运行!"
    echo "请先启动 Docker 容器："
    echo "docker-compose up -d"
    exit 1
fi

if ! docker ps | grep -q blinko-website; then
    echo "❌ 错误: blinko-website 容器未运行!"
    echo "请先启动 Docker 容器："
    echo "docker-compose up -d"
    exit 1
fi

echo "✅ Docker 容器运行正常"
echo ""

# 导入前备份
echo "💾 创建导入前备份..."
docker exec blinko-postgres pg_dump -U postgres postgres > "backup_before_import_$(date +%Y%m%d_%H%M%S).sql"
echo "✅ 备份完成"
echo ""

# 执行数据导入
echo "📊 正在导入完整数据..."
echo "目标数据库: postgres"
echo "包含内容: 标签、笔记、附件、关联关系"
echo ""

# 执行导入脚本
docker exec -i blinko-postgres psql -U postgres -d postgres < complete_data_import.sql

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 数据导入成功完成!"
    echo ""
    
    # 验证导入结果
    echo "📈 验证导入结果..."
    
    echo "📋 标签统计:"
    docker exec blinko-postgres psql -U postgres -d postgres -c "SELECT COUNT(*) as \"标签总数\" FROM tag;" -t
    
    echo "📝 笔记统计:"
    docker exec blinko-postgres psql -U postgres -d postgres -c "SELECT COUNT(*) as \"笔记总数\" FROM notes;" -t
    
    echo "🔗 标签关联统计:"
    docker exec blinko-postgres psql -U postgres -d postgres -c "SELECT COUNT(*) as \"关联总数\" FROM \"tagsToNote\";" -t
    
    echo "📎 附件统计:"
    docker exec blinko-postgres psql -U postgres -d postgres -c "SELECT COUNT(*) as \"附件总数\" FROM attachments;" -t
    
    echo ""
    echo "🏷️ 新导入的标签预览:"
    docker exec blinko-postgres psql -U postgres -d postgres -c "SELECT id, name, icon FROM tag WHERE id >= 101 ORDER BY id LIMIT 5;" -t
    
    echo ""
    echo "📄 新导入的笔记预览:"
    docker exec blinko-postgres psql -U postgres -d postgres -c "SELECT id, LEFT(content, 40) || '...' as \"笔记摘要\" FROM notes WHERE id >= 101 ORDER BY id LIMIT 5;" -t
    
    echo ""
    echo "🔄 重启 Blinko 应用以刷新缓存..."
    docker restart blinko-website
    
    echo ""
    echo "✨ 等待应用启动..."
    sleep 10
    
    echo ""
    echo "🌟 ==================================="
    echo "🎉 全部数据导入成功完成！"
    echo "🌟 ==================================="
    echo ""
    echo "📍 服务地址: https://ccnu.me"
    echo "📊 导入内容:"
    echo "   • ✅ Welcome 系列笔记"
    echo "   • ✅ 项目管理笔记"  
    echo "   • ✅ 硅基API笔记"
    echo "   • ✅ 多层级标签体系"
    echo "   • ✅ 附件文件"
    echo "   • ✅ 标签关联关系"
    echo ""
    echo "🎯 下一步操作:"
    echo "   1. 访问 https://ccnu.me"
    echo "   2. 登录您的账户"
    echo "   3. 查看新导入的笔记和标签"
    echo "   4. 测试搜索和创建功能"
    echo ""
    echo "🔧 如有问题，请查看 Docker 日志:"
    echo "   docker logs blinko-website"
    echo "   docker logs blinko-postgres"
    echo ""
    
else
    echo ""
    echo "❌ 数据导入失败!"
    echo "请检查错误信息并重试"
    echo ""
    echo "🔧 调试命令:"
    echo "docker exec blinko-postgres psql -U postgres -d postgres -c \"SELECT 1;\""
    echo "docker logs blinko-postgres"
    exit 1
fi
