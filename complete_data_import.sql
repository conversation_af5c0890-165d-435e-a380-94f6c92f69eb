-- =====================================================
-- 🚀 完整 Blinko 数据导入脚本 (ccnu.me)
-- =====================================================
-- 目标数据库: postgres (Docker容器中的正确数据库)
-- 服务地址: https://ccnu.me
-- 执行日期: 2025-07-17
-- 安全措施: 使用 WHERE NOT EXISTS 避免重复数据

-- 1. 导入标签数据 (Tags)
-- ====================================
INSERT INTO tag (id, name, icon, parent, "createdAt", "updatedAt", "accountId", "sortOrder")
SELECT * FROM (VALUES
    (101, 'Welcome', '🎉', 0, '2025-02-27 23:52:53.341+08', '2025-02-27 23:52:53.341+08', 1, 0),
    (102, 'Attachment', '🔖', 101, '2025-02-27 23:52:53.341+08', '2025-02-27 23:52:53.341+08', 1, 0),
    (103, 'Code', '🪄', 101, '2025-02-27 23:52:53.341+08', '2025-02-27 23:52:53.341+08', 1, 0),
    (104, 'To-Do', '✨', 101, '2025-02-27 23:52:53.341+08', '2025-02-27 23:52:53.341+08', 1, 0),
    (105, 'Multi-Level-Tags', '🏷️', 101, '2025-02-27 23:52:53.341+08', '2025-02-27 23:52:53.341+08', 1, 0),
    (107, 'p', '', 0, '2025-02-27 23:56:07.527+08', '2025-02-27 23:56:07.527+08', 1, 0),
    (108, '项目', '', 107, '2025-02-27 23:56:07.537+08', '2025-02-27 23:56:07.537+08', 1, 0),
    (109, '硅基', '', 0, '2025-03-08 10:23:15.386+08', '2025-03-08 10:23:15.386+08', 1, 0)
) AS v(id, name, icon, parent, "createdAt", "updatedAt", "accountId", "sortOrder")
WHERE NOT EXISTS (
    SELECT 1 FROM tag WHERE tag.name = v.name AND tag."accountId" = v."accountId"
);

-- 2. 导入笔记数据 (Notes)
-- ====================================
INSERT INTO notes (id, type, content, "isArchived", "isRecycle", "isShare", "isTop", "sharePassword", metadata, "createdAt", "updatedAt", "isReviewed", "accountId", "shareEncryptedUrl", "shareExpiryDate", "shareMaxView", "shareViewCount")
SELECT * FROM (VALUES
    (101, 0, '#Welcome

Welcome to Blinko!

Whether you''re capturing ideas, taking meeting notes, or planning your schedule, Blinko provides an easy and efficient way to manage it all. Here, you can create, edit, and share notes anytime, anywhere, ensuring you never lose a valuable thought.', false, false, false, false, '', '{"isIndexed": true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1, null, null, 0, 0),

    (102, 0, '#Welcome/Attachment', false, false, false, false, '', '{"isIndexed": true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1, null, null, 0, 0),

    (103, 0, '#Welcome/Code


```js
function Welcome(){
  console.log("Hello! Blinko");
}
```', false, false, false, false, '', '{"isIndexed": true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1, null, null, 0, 0),

    (104, 0, '#Welcome/To-Do

* Create a blinko
* Create a note
* Upload file', false, false, false, false, '', '{"isIndexed": true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1, null, null, 0, 0),

    (105, 0, '#Welcome/Multi-Level-Tags

Use the "/" shortcut to effortlessly create and organize multi-level tags.', false, false, false, false, '', '{"isIndexed": true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1, null, null, 0, 0),

    (106, 0, 'https://github.com/blinko-space/blinko/', false, false, false, false, '', '{"isIndexed": true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1, null, null, 0, 0),

    (108, 0, '#p/项目 少女', false, false, false, false, '', '{"isIndexed": true}', '2025-02-27 23:56:07.517+08', '2025-02-27 23:56:07.517+08', false, 1, null, null, 0, 0),

    (109, 0, '#硅基 sk-jlrbbqfihhxdxfzftkviokwvbyjmkayejednthwxqjrrrtwq', false, false, false, false, '', '{"isIndexed": true}', '2025-03-08 10:23:11.817+08', '2025-03-08 10:23:11.817+08', false, 1, null, null, 0, 0)
) AS v(id, type, content, "isArchived", "isRecycle", "isShare", "isTop", "sharePassword", metadata, "createdAt", "updatedAt", "isReviewed", "accountId", "shareEncryptedUrl", "shareExpiryDate", "shareMaxView", "shareViewCount")
WHERE NOT EXISTS (
    SELECT 1 FROM notes WHERE notes.content = v.content AND notes."accountId" = v."accountId"
);

-- 3. 导入标签-笔记关联数据 (TagsToNote)
-- ====================================
INSERT INTO "tagsToNote" ("noteId", "tagId")
SELECT * FROM (VALUES
    (101, 101), -- Welcome note -> Welcome tag
    (102, 101), -- Attachment note -> Welcome tag
    (102, 102), -- Attachment note -> Attachment tag
    (103, 101), -- Code note -> Welcome tag
    (103, 103), -- Code note -> Code tag
    (104, 101), -- To-Do note -> Welcome tag
    (104, 104), -- To-Do note -> To-Do tag
    (105, 101), -- Multi-Level-Tags note -> Welcome tag
    (105, 105), -- Multi-Level-Tags note -> Multi-Level-Tags tag
    (108, 107), -- 项目 note -> p tag
    (108, 108), -- 项目 note -> 项目 tag
    (109, 109)  -- 硅基 note -> 硅基 tag
) AS v("noteId", "tagId")
WHERE NOT EXISTS (
    SELECT 1 FROM "tagsToNote" WHERE "tagsToNote"."noteId" = v."noteId" AND "tagsToNote"."tagId" = v."tagId"
);

-- 4. 导入附件数据 (Attachments) 
-- ====================================
INSERT INTO attachments (id, "isShare", "sharePassword", name, path, size, "noteId", "createdAt", "updatedAt", type, "sortOrder", depth, "perfixPath", "accountId")
SELECT * FROM (VALUES
    (101, false, '', 'pic01.png', '/api/file/pic01.png', 1360952, 102, '2025-02-27 23:52:53.348+08', '2025-03-08 22:22:00.394+08', '', 0, 0, '', null),
    (102, false, '', 'pic02.png', '/api/file/pic02.png', 971782, 102, '2025-02-27 23:52:53.348+08', '2025-03-08 22:22:00.402+08', '', 0, 0, '', null),
    (103, false, '', 'pic03.png', '/api/file/pic03.png', 141428, 102, '2025-02-27 23:52:53.348+08', '2025-03-08 22:22:00.405+08', '', 0, 0, '', null),
    (104, false, '', 'pic04.png', '/api/file/pic04.png', 589371, 102, '2025-02-27 23:52:53.348+08', '2025-03-08 22:22:00.408+08', '', 0, 0, '', null),
    (105, false, '', 'pic06.png', '/api/file/pic06.png', 875361, 102, '2025-02-27 23:52:53.348+08', '2025-03-08 22:22:00.411+08', '', 0, 0, '', null),
    (106, false, '', 'story.txt', '/api/file/story.txt', 0, 102, '2025-02-27 23:52:53.348+08', '2025-03-08 22:22:00.414+08', '', 0, 0, '', null)
) AS v(id, "isShare", "sharePassword", name, path, size, "noteId", "createdAt", "updatedAt", type, "sortOrder", depth, "perfixPath", "accountId")
WHERE NOT EXISTS (
    SELECT 1 FROM attachments WHERE attachments.name = v.name AND attachments."noteId" = v."noteId"
);

-- 5. 重置序列计数器
-- ====================================
-- 获取当前最大ID并重置序列
SELECT setval('tag_id_seq', COALESCE((SELECT MAX(id) FROM tag), 1), true);
SELECT setval('notes_id_seq', COALESCE((SELECT MAX(id) FROM notes), 1), true);
SELECT setval('"tagsToNote_id_seq"', COALESCE((SELECT MAX(id) FROM "tagsToNote"), 1), true);
SELECT setval('attachments_id_seq', COALESCE((SELECT MAX(id) FROM attachments), 1), true);

-- 6. 验证导入结果
-- ====================================
-- 验证标签数量
SELECT COUNT(*) as "标签总数" FROM tag;

-- 验证笔记数量  
SELECT COUNT(*) as "笔记总数" FROM notes;

-- 验证标签关联数量
SELECT COUNT(*) as "标签关联总数" FROM "tagsToNote";

-- 验证附件数量
SELECT COUNT(*) as "附件总数" FROM attachments;

-- 显示新导入的标签
SELECT id, name, icon, parent FROM tag WHERE id >= 101 ORDER BY id;

-- 显示新导入的笔记标题
SELECT id, LEFT(content, 50) || '...' as "笔记摘要" FROM notes WHERE id >= 101 ORDER BY id;

-- =====================================================
-- 🎉 导入完成！请访问 https://ccnu.me 查看结果
-- =====================================================
