SET session_replication_role = 'replica';
--
-- PostgreSQL database cluster dump
--

SET default_transaction_read_only = off;

SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;

--
-- Roles
--







--
-- Databases
--

--
-- Database "template1" dump
--

\connect template1

--
-- PostgreSQL database dump
--

-- Dumped from database version 14.17 (Debian 14.17-1.pgdg120+1)
-- Dumped by pg_dump version 14.17 (Debian 14.17-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- PostgreSQL database dump complete
--

--
-- Database "blinko" dump
--

--
-- PostgreSQL database dump
--

-- Dumped from database version 14.17 (Debian 14.17-1.pgdg120+1)
-- Dumped by pg_dump version 14.17 (Debian 14.17-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: blinko; Type: DATABASE; Schema: -; Owner: postgres
--

CREATE DATABASE blinko WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE = 'en_US.utf8';


ALTER DATABASE blinko OWNER TO postgres;

\connect blinko

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO postgres;

--
-- Name: accounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accounts (
    id integer NOT NULL,
    name character varying DEFAULT ''::character varying NOT NULL,
    nickname character varying DEFAULT ''::character varying NOT NULL,
    password character varying DEFAULT ''::character varying NOT NULL,
    image character varying DEFAULT ''::character varying NOT NULL,
    "apiToken" character varying DEFAULT ''::character varying NOT NULL,
    note integer DEFAULT 0 NOT NULL,
    role character varying DEFAULT ''::character varying NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "loginType" character varying DEFAULT ''::character varying NOT NULL,
    "linkAccountId" integer,
    description text DEFAULT ''::text NOT NULL
);


ALTER TABLE public.accounts OWNER TO postgres;

--
-- Name: accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accounts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.accounts_id_seq OWNER TO postgres;

--
-- Name: accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accounts_id_seq OWNED BY public.accounts.id;


--
-- Name: attachments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.attachments (
    id integer NOT NULL,
    "isShare" boolean DEFAULT false NOT NULL,
    "sharePassword" character varying DEFAULT ''::character varying NOT NULL,
    name character varying DEFAULT ''::character varying NOT NULL,
    path character varying DEFAULT ''::character varying NOT NULL,
    size numeric DEFAULT 0 NOT NULL,
    "noteId" integer,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    type character varying DEFAULT ''::character varying NOT NULL,
    "sortOrder" integer DEFAULT 0 NOT NULL,
    depth integer,
    "perfixPath" character varying DEFAULT ''::character varying,
    "accountId" integer
);


ALTER TABLE public.attachments OWNER TO postgres;

--
-- Name: attachments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.attachments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.attachments_id_seq OWNER TO postgres;

--
-- Name: attachments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.attachments_id_seq OWNED BY public.attachments.id;


--
-- Name: cache; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cache (
    id integer NOT NULL,
    key character varying NOT NULL,
    value json NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.cache OWNER TO postgres;

--
-- Name: cache_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cache_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.cache_id_seq OWNER TO postgres;

--
-- Name: cache_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cache_id_seq OWNED BY public.cache.id;


--
-- Name: comments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.comments (
    id integer NOT NULL,
    content text NOT NULL,
    "accountId" integer,
    "guestName" character varying,
    "guestIP" character varying,
    "guestUA" character varying,
    "noteId" integer NOT NULL,
    "parentId" integer,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.comments OWNER TO postgres;

--
-- Name: comments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.comments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.comments_id_seq OWNER TO postgres;

--
-- Name: comments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.comments_id_seq OWNED BY public.comments.id;


--
-- Name: config; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.config (
    id integer NOT NULL,
    key character varying DEFAULT ''::character varying NOT NULL,
    config json,
    "userId" integer
);


ALTER TABLE public.config OWNER TO postgres;

--
-- Name: config_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.config_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.config_id_seq OWNER TO postgres;

--
-- Name: config_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.config_id_seq OWNED BY public.config.id;


--
-- Name: conversation; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.conversation (
    id integer NOT NULL,
    title character varying(255) DEFAULT ''::character varying NOT NULL,
    "accountId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.conversation OWNER TO postgres;

--
-- Name: conversation_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.conversation_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.conversation_id_seq OWNER TO postgres;

--
-- Name: conversation_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.conversation_id_seq OWNED BY public.conversation.id;


--
-- Name: follows; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.follows (
    id integer NOT NULL,
    "siteName" character varying,
    "siteUrl" character varying NOT NULL,
    "siteAvatar" character varying,
    description text,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "followType" character varying DEFAULT 'following'::character varying NOT NULL,
    "accountId" integer NOT NULL
);


ALTER TABLE public.follows OWNER TO postgres;

--
-- Name: follows_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.follows_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.follows_id_seq OWNER TO postgres;

--
-- Name: follows_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.follows_id_seq OWNED BY public.follows.id;


--
-- Name: message; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.message (
    id integer NOT NULL,
    content text NOT NULL,
    role character varying(50) NOT NULL,
    "conversationId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    metadata json
);


ALTER TABLE public.message OWNER TO postgres;

--
-- Name: message_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.message_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.message_id_seq OWNER TO postgres;

--
-- Name: message_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.message_id_seq OWNED BY public.message.id;


--
-- Name: noteHistory; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."noteHistory" (
    id integer NOT NULL,
    "noteId" integer NOT NULL,
    content text NOT NULL,
    metadata json,
    version integer NOT NULL,
    "accountId" integer,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."noteHistory" OWNER TO postgres;

--
-- Name: noteHistory_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."noteHistory_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."noteHistory_id_seq" OWNER TO postgres;

--
-- Name: noteHistory_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."noteHistory_id_seq" OWNED BY public."noteHistory".id;


--
-- Name: noteReference; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."noteReference" (
    id integer NOT NULL,
    "fromNoteId" integer NOT NULL,
    "toNoteId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."noteReference" OWNER TO postgres;

--
-- Name: noteReference_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."noteReference_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."noteReference_id_seq" OWNER TO postgres;

--
-- Name: noteReference_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."noteReference_id_seq" OWNED BY public."noteReference".id;


--
-- Name: notes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notes (
    id integer NOT NULL,
    type integer DEFAULT 0 NOT NULL,
    content character varying DEFAULT ''::character varying NOT NULL,
    "isArchived" boolean DEFAULT false NOT NULL,
    "isRecycle" boolean DEFAULT false NOT NULL,
    "isShare" boolean DEFAULT false NOT NULL,
    "isTop" boolean DEFAULT false NOT NULL,
    "sharePassword" character varying DEFAULT ''::character varying NOT NULL,
    metadata json,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "isReviewed" boolean DEFAULT false NOT NULL,
    "accountId" integer,
    "shareEncryptedUrl" character varying,
    "shareExpiryDate" timestamp(6) with time zone,
    "shareMaxView" integer DEFAULT 0,
    "shareViewCount" integer DEFAULT 0
);


ALTER TABLE public.notes OWNER TO postgres;

--
-- Name: notes_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.notes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.notes_id_seq OWNER TO postgres;

--
-- Name: notes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.notes_id_seq OWNED BY public.notes.id;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notifications (
    id integer NOT NULL,
    type character varying NOT NULL,
    title character varying NOT NULL,
    content text NOT NULL,
    metadata json,
    "isRead" boolean DEFAULT false NOT NULL,
    "accountId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.notifications OWNER TO postgres;

--
-- Name: notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.notifications_id_seq OWNER TO postgres;

--
-- Name: notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.notifications_id_seq OWNED BY public.notifications.id;


--
-- Name: plugin; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.plugin (
    id integer NOT NULL,
    metadata json NOT NULL,
    path character varying NOT NULL,
    "isUse" boolean DEFAULT true NOT NULL,
    "isDev" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.plugin OWNER TO postgres;

--
-- Name: plugin_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.plugin_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.plugin_id_seq OWNER TO postgres;

--
-- Name: plugin_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.plugin_id_seq OWNED BY public.plugin.id;


--
-- Name: scheduledTask; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."scheduledTask" (
    name text NOT NULL,
    schedule text NOT NULL,
    "lastRun" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "isSuccess" boolean DEFAULT true NOT NULL,
    "isRunning" boolean DEFAULT false NOT NULL,
    output json
);


ALTER TABLE public."scheduledTask" OWNER TO postgres;

--
-- Name: tag; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tag (
    id integer NOT NULL,
    name character varying DEFAULT ''::character varying NOT NULL,
    icon character varying DEFAULT ''::character varying NOT NULL,
    parent integer DEFAULT 0 NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "accountId" integer,
    "sortOrder" integer DEFAULT 0 NOT NULL
);


ALTER TABLE public.tag OWNER TO postgres;

--
-- Name: tag_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tag_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.tag_id_seq OWNER TO postgres;

--
-- Name: tag_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.tag_id_seq OWNED BY public.tag.id;


--
-- Name: tagsToNote; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."tagsToNote" (
    id integer NOT NULL,
    "noteId" integer DEFAULT 0 NOT NULL,
    "tagId" integer DEFAULT 0 NOT NULL
);


ALTER TABLE public."tagsToNote" OWNER TO postgres;

--
-- Name: tagsToNote_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."tagsToNote_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."tagsToNote_id_seq" OWNER TO postgres;

--
-- Name: tagsToNote_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."tagsToNote_id_seq" OWNED BY public."tagsToNote".id;


--
-- Name: accounts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts ALTER COLUMN id SET DEFAULT nextval('public.accounts_id_seq'::regclass);


--
-- Name: attachments id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments ALTER COLUMN id SET DEFAULT nextval('public.attachments_id_seq'::regclass);


--
-- Name: cache id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cache ALTER COLUMN id SET DEFAULT nextval('public.cache_id_seq'::regclass);


--
-- Name: comments id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments ALTER COLUMN id SET DEFAULT nextval('public.comments_id_seq'::regclass);


--
-- Name: config id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.config ALTER COLUMN id SET DEFAULT nextval('public.config_id_seq'::regclass);


--
-- Name: conversation id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversation ALTER COLUMN id SET DEFAULT nextval('public.conversation_id_seq'::regclass);


--
-- Name: follows id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows ALTER COLUMN id SET DEFAULT nextval('public.follows_id_seq'::regclass);


--
-- Name: message id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message ALTER COLUMN id SET DEFAULT nextval('public.message_id_seq'::regclass);


--
-- Name: noteHistory id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteHistory" ALTER COLUMN id SET DEFAULT nextval('public."noteHistory_id_seq"'::regclass);


--
-- Name: noteReference id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference" ALTER COLUMN id SET DEFAULT nextval('public."noteReference_id_seq"'::regclass);


--
-- Name: notes id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notes ALTER COLUMN id SET DEFAULT nextval('public.notes_id_seq'::regclass);


--
-- Name: notifications id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications ALTER COLUMN id SET DEFAULT nextval('public.notifications_id_seq'::regclass);


--
-- Name: plugin id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.plugin ALTER COLUMN id SET DEFAULT nextval('public.plugin_id_seq'::regclass);


--
-- Name: tag id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag ALTER COLUMN id SET DEFAULT nextval('public.tag_id_seq'::regclass);


--
-- Name: tagsToNote id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote" ALTER COLUMN id SET DEFAULT nextval('public."tagsToNote_id_seq"'::regclass);


--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
b46ce3a6-9800-4b76-b7ed-378e951042bb	9fa97d3b49d43eb600745ee4c4845358be7d3e1139f98bed17df6c8e8afe8e46	2025-02-27 23:31:11.800519+08	20241026033301_0_0_1	\N	\N	2025-02-27 23:31:11.727358+08	1
93686754-967c-48df-8e44-33ef900735fd	2ce1697d65421c8f9e35edd5c2c53763bdebe368434bfc6b24e30fdb42f2dff4	2025-02-27 23:31:11.943045+08	20250110022717_0_34_15	\N	\N	2025-02-27 23:31:11.935775+08	1
1622c434-2cdd-452a-bcab-6fd80ae8bfd2	bca4800423e0a93798bd3bd4495222c5f1a290ee6a013be2b2250eae49fd9b5f	2025-02-27 23:31:11.809355+08	20241027072348_0_0_3	\N	\N	2025-02-27 23:31:11.803237+08	1
4497093b-c2d0-4322-ac46-5864340b2bd1	f66411c9c48f9b8d624d3d0a84183ea59fa005f34143a2348cbec15213d2c829	2025-02-27 23:31:11.818425+08	20241112084821_0_2_9	\N	\N	2025-02-27 23:31:11.811808+08	1
d284dd9e-fce2-49ce-9bc6-d998a6c75863	570c238793a05d26ea62ac05fb2c4ecf55e704e5a648b2d346e92ce727cd9dc2	2025-02-27 23:31:11.82783+08	20241121082228_0_6_0	\N	\N	2025-02-27 23:31:11.82196+08	1
0264d961-f599-4f90-9c2e-fb4f916991a6	7a72662f97fe7d3ff25f48ba8b0b4d775a7786fb382f9ffa74d1568d11041996	2025-02-27 23:31:11.960911+08	20250110105226_0_36_0	\N	\N	2025-02-27 23:31:11.944946+08	1
01d08f66-4a63-4739-9265-3a656f3048f2	08cb30f3ae17306833068ee37027f90243ec9fe0ad1ccf317097d408d8f08778	2025-02-27 23:31:11.835802+08	20241202103221_0_12_11	\N	\N	2025-02-27 23:31:11.82985+08	1
5d7a7dd4-e908-4502-8a1f-f2ae6b19a531	d7ba53a4d86751d7d3b05477676eace96e0907eee7ca35e5d77d32559631c323	2025-02-27 23:31:11.852223+08	20241205035247_18_0_1	\N	\N	2025-02-27 23:31:11.837755+08	1
28d1100e-68ad-4a91-8137-592b617699be	cb212e993eeb326e7092e5cea14f069bde6601c29863602b3f998b3847afbcb7	2025-02-27 23:31:11.861016+08	20241212033352_0_23_3	\N	\N	2025-02-27 23:31:11.854958+08	1
c6ef5b0c-88c3-4dc8-91d4-35c85f592202	bb7b9ee3fe8d49311ead5709ef71579ca099b4f0e05011a07229fc49e4273cd5	2025-02-27 23:31:11.982831+08	20250115050059_0_37_0	\N	\N	2025-02-27 23:31:11.963633+08	1
72643890-4893-437c-ae3a-ea86c99e7143	60e2f4991f43b9abed2c0982c0cda50be307e7f6673d9aaf099a6f4b6edb90d6	2025-02-27 23:31:11.869354+08	20241218023101_0_27_0	\N	\N	2025-02-27 23:31:11.863365+08	1
050d30fd-8893-486a-9c9b-2e9fc577932c	5f13a60e92bfbe447840075a94ac3e2f37159c611c08f75e78c79e9b3ac1fcff	2025-02-27 23:31:11.876685+08	20241219062514_0_27_7	\N	\N	2025-02-27 23:31:11.871131+08	1
834ffe0c-018e-455c-be48-c42169d8390b	471626763957618b7b81e0a266dc4179c63a8d62b254a88c7499734bd163ed04	2025-02-27 23:31:11.885907+08	20241226141834_0_30_7	\N	\N	2025-02-27 23:31:11.878537+08	1
030d7b96-35ef-4ac3-a853-6e6a588506d2	73c54b7c727c21868a10b47577b678fbd15b2e720410773ccf0087a67c371e0d	2025-02-27 23:31:12.00228+08	20250116052731_0_37_4	\N	\N	2025-02-27 23:31:11.984727+08	1
2d0891da-4134-46a5-89e8-bb0ca9a954bd	a626e584989cc0e1712e9a1b16e4cbbae3a2c0ab2214d137bb748fd115687821	2025-02-27 23:31:11.89455+08	20241231014246_0_32_0	\N	\N	2025-02-27 23:31:11.888658+08	1
feb61e73-def0-49ca-b26c-438d52d1d3d4	89a28b674edab6177c0b56968bb4920ef98b144ec4e8bb490290e3cd8eb081e1	2025-02-27 23:31:11.902694+08	20250101032000_0_32_4	\N	\N	2025-02-27 23:31:11.897162+08	1
dec5e363-f0ad-4266-a256-919ea67e06b1	b577841cbcd0b79b95b59569c546caced160b42ae811c42cbdc7d952ffe736c9	2025-02-27 23:31:11.923314+08	20250108081202_0_34_0	\N	\N	2025-02-27 23:31:11.904603+08	1
53244c5e-8526-45f1-ae4d-77ffa33ab162	5ff61439418b8e46dbf00dcddd25ff1fe09adbf8c191b4285fbe9f65b67e4a07	2025-02-27 23:31:12.019893+08	20250219083523_0_39_0	\N	\N	2025-02-27 23:31:12.004065+08	1
bb8e9b9b-fae2-475f-8a56-ecea286eca95	df97e584d79952e8a5a5463ec9f46bd3aaae1776d38aacbb5320adb7576e518f	2025-02-27 23:31:11.93327+08	20250110011520_0_34_13	\N	\N	2025-02-27 23:31:11.925354+08	1
63ba26f8-0121-4a5d-b587-87653320e8b8	ba9d281c54e8e733318de494b4b8e37d4977f9611dea63bc38b96751d10b9973	2025-02-27 23:31:12.049134+08	20250222125610_0_41_0	\N	\N	2025-02-27 23:31:12.022231+08	1
094fc0b8-f97b-4215-9f89-6c5133f7d6f0	d1cd034aa7e81c8e6ed452103a686644c9a6c5a8dff5aeaf4c5063ec87b229ae	2025-03-08 23:30:11.98739+08	20250306102203_0_42_4	\N	\N	2025-03-08 23:30:11.949898+08	1
\.


--
-- Data for Name: accounts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.accounts (id, name, nickname, password, image, "apiToken", note, role, "createdAt", "updatedAt", "loginType", "linkAccountId", description) FROM stdin;
1	Cotton	Cotton	pbkdf2:531683872cc50d0c077610277b383b13:2a3597f0423fc9c4740f513c7043329d5d62c03cc9521376e2b18a0fc2733880175eebcd03dfd5f21415881e14cd3091aaf4117dcbd7d1dfc48e0dc54b943b6e		eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..A3SABkHSW5nHBN7J.9Xj93PzoiEie8V9PwM7dnj-5fMFy7M8SwJf-tT25VxUJjtA_KXo-Nh2et8Eblt8WJWROsy0F4y_Q6spIP99tXn9dempStMPDlNXynezCDQAFubhLhQ9owqeLiz3gMI_gUpfQmIoWlPKw7jLYJH_Tr153rDT0dHwEwILOOtFN.qpS-8mlotMNaAQxI9lxPRg	0	superadmin	2025-02-27 23:52:53.307+08	2025-02-27 23:52:53.32+08		\N	
\.


--
-- Data for Name: attachments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.attachments (id, "isShare", "sharePassword", name, path, size, "noteId", "createdAt", "updatedAt", type, "sortOrder", depth, "perfixPath", "accountId") FROM stdin;
1	f		pic01.png	/api/file/pic01.png	1360952	2	2025-02-27 23:52:53.348+08	2025-03-08 23:30:12.398+08		0	0		\N
2	f		pic02.png	/api/file/pic02.png	971782	2	2025-02-27 23:52:53.348+08	2025-03-08 23:30:12.405+08		0	0		\N
3	f		pic03.png	/api/file/pic03.png	141428	2	2025-02-27 23:52:53.348+08	2025-03-08 23:30:12.408+08		0	0		\N
4	f		pic04.png	/api/file/pic04.png	589371	2	2025-02-27 23:52:53.348+08	2025-03-08 23:30:12.411+08		0	0		\N
5	f		pic06.png	/api/file/pic06.png	875361	2	2025-02-27 23:52:53.348+08	2025-03-08 23:30:12.413+08		0	0		\N
6	f		story.txt	/api/file/story.txt	0	2	2025-02-27 23:52:53.348+08	2025-03-08 23:30:12.415+08		0	0		\N
8	f		46efdeba9c1b62fc5a0964a2101e8f3_1741832915263.jpg	/api/file/46efdeba9c1b62fc5a0964a2101e8f3_1741832915263.jpg	133775	23	2025-03-13 10:28:35.268+08	2025-03-13 10:28:46.017+08	image/jpeg	0	0		1
9	f		image_1742113484795.png	/api/file/image_1742113484795.png	149705	32	2025-03-16 16:24:44.799+08	2025-03-16 18:19:08.44+08	image/png	0	0		1
10	f		image_1742113510403.png	/api/file/image_1742113510403.png	488765	32	2025-03-16 16:25:10.406+08	2025-03-16 18:19:08.44+08	image/png	0	0		1
11	f		data/f0c4652d151aeaa7dda6363745f1ca004a6eb583_2_465x500.png	/api/file/E:\\blinko-data/f0c4652d151aeaa7dda6363745f1ca004a6eb583_2_465x500.png	21385	\N	2025-03-18 20:25:02.617+08	2025-03-18 20:25:02.617+08	image/png	0	1	E:\\blinko-data	1
12	f		data/f0c4652d151aeaa7dda6363745f1ca004a6eb583.png	/api/file/E:\\blinko-data/f0c4652d151aeaa7dda6363745f1ca004a6eb583.png	36642	19	2025-03-18 20:25:50.374+08	2025-03-18 20:25:55.02+08	image/png	0	1	E:\\blinko-data	1
13	f		image_1742302890840.png	/api/file/E:\\blinko-data/image_1742302890840.png	28827	\N	2025-03-18 21:01:30.843+08	2025-03-18 21:01:30.843+08	image/png	0	1	E:\\blinko-data	1
15	f		image_1742302956298.png	/api/file/E:\\blinko-data/image_1742302956298.png	28827	34	2025-03-18 21:02:36.3+08	2025-03-18 21:03:01.458+08	image/png	0	1	E:\\blinko-data	1
\.


--
-- Data for Name: cache; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.cache (id, key, value, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: comments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.comments (id, content, "accountId", "guestName", "guestIP", "guestUA", "noteId", "parentId", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: config; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.config (id, key, config, "userId") FROM stdin;
1	NEXTAUTH_SECRET	{"value":"CSVoNVGcIBxOUXLoyX/JsCKWAr6ShsTNJUEVByswtMc="}	\N
2	theme	{"value":"system"}	1
3	isUseAI	{"type":"boolean","value":true}	\N
11	tavilyApiKey	{"type":"string","value":"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"}	\N
13	embeddingApiEndpoint	{"type":"string","value":"https://api.siliconflow.com/v1"}	\N
15	spotifyConsumerKey	{"type":"string","value":"Cotton"}	\N
16	spotifyConsumerSecret	{"type":"string","value":"TCW147896"}	\N
8	aiModelProvider	{"type":"string","value":"OpenAI"}	\N
5	aiModel	{"type":"string","value":"gpt-4o"}	\N
9	aiApiKey	{"type":"string","value":"sk-FN0OmEVkXsA442zJ2U05uZ6CEsu5ckpTh6ryBiYY"}	\N
10	aiApiEndpoint	{"type":"string","value":"https://free.yunwu.ai/v1"}	\N
17	themeColor	{"type":"string","value":"#16a34a"}	1
18	themeForegroundColor	{"type":"string","value":"#ffffff"}	1
19	customBackgroundUrl	{"type":"string","value":""}	\N
20	smallDeviceCardColumns	{"type":"string","value":"1"}	1
21	largeDeviceCardColumns	{"type":"string","value":"1"}	1
6	localCustomPath	{"type":"string","value":"E:\\\\blinko-data"}	\N
12	embeddingApiKey	{"type":"string","value":"sk-bkietjtzisygzbspeukkzkqbdzliglhduewkwkkkpldiemlp"}	\N
22	embeddingDimensions	{"type":"number","value":1024}	\N
4	embeddingModel	{"type":"string","value":"Pro/BAAI/bge-m3"}	\N
14	embeddingTopK	{"type":"number","value":4}	\N
23	embeddingScore	{"type":"number","value":0.65}	\N
7	objectStorage	{"type":"string","value":"local"}	\N
\.


--
-- Data for Name: conversation; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.conversation (id, title, "accountId", "createdAt", "updatedAt") FROM stdin;
1		1	2025-03-08 23:42:00.584+08	2025-03-08 23:42:00.584+08
2		1	2025-03-08 23:44:01.294+08	2025-03-08 23:44:01.294+08
3	生日快乐文章撰写	1	2025-03-09 00:00:33.549+08	2025-03-09 00:00:58.731+08
\.


--
-- Data for Name: follows; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.follows (id, "siteName", "siteUrl", "siteAvatar", description, "createdAt", "updatedAt", "followType", "accountId") FROM stdin;
\.


--
-- Data for Name: message; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.message (id, content, role, "conversationId", "createdAt", "updatedAt", metadata) FROM stdin;
1	你好	user	1	2025-03-08 23:42:00.946+08	2025-03-08 23:42:00.946+08	\N
2	撰写一篇200字的文章并将其保存在你的笔记中。	user	2	2025-03-08 23:44:01.475+08	2025-03-08 23:44:01.475+08	\N
3	你好	user	2	2025-03-08 23:46:41.984+08	2025-03-08 23:46:41.984+08	\N
4	你好	user	2	2025-03-08 23:51:16.59+08	2025-03-08 23:51:16.59+08	\N
5	你好	user	2	2025-03-08 23:52:59.055+08	2025-03-08 23:52:59.055+08	\N
6		assistant	2	2025-03-08 23:53:00.761+08	2025-03-08 23:53:00.761+08	{"notes":[{"id":2,"type":0,"content":"#Welcome/Attachment","isArchived":false,"isRecycle":false,"isShare":false,"isTop":false,"isReviewed":false,"sharePassword":"","shareEncryptedUrl":null,"shareExpiryDate":null,"shareMaxView":0,"shareViewCount":0,"metadata":{"isIndexed":true,"isAttachmentsIndexed":true},"accountId":1,"createdAt":"2025-02-27T15:52:53.337Z","updatedAt":"2025-02-27T15:52:53.337Z","tags":[{"id":2,"noteId":2,"tagId":1,"tag":{"id":1,"name":"Welcome","icon":"🎉","parent":0,"accountId":1,"createdAt":"2025-02-27T15:52:53.341Z","updatedAt":"2025-02-27T15:52:53.341Z","sortOrder":0}},{"id":3,"noteId":2,"tagId":2,"tag":{"id":2,"name":"Attachment","icon":"🔖","parent":1,"accountId":1,"createdAt":"2025-02-27T15:52:53.341Z","updatedAt":"2025-02-27T15:52:53.341Z","sortOrder":0}}],"attachments":[{"id":1,"isShare":false,"sharePassword":"","name":"pic01.png","path":"/api/file/pic01.png","size":"1360952","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.398Z","perfixPath":"","depth":0},{"id":2,"isShare":false,"sharePassword":"","name":"pic02.png","path":"/api/file/pic02.png","size":"971782","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.405Z","perfixPath":"","depth":0},{"id":3,"isShare":false,"sharePassword":"","name":"pic03.png","path":"/api/file/pic03.png","size":"141428","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.408Z","perfixPath":"","depth":0},{"id":4,"isShare":false,"sharePassword":"","name":"pic04.png","path":"/api/file/pic04.png","size":"589371","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.411Z","perfixPath":"","depth":0},{"id":5,"isShare":false,"sharePassword":"","name":"pic06.png","path":"/api/file/pic06.png","size":"875361","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.413Z","perfixPath":"","depth":0},{"id":6,"isShare":false,"sharePassword":"","name":"story.txt","path":"/api/file/story.txt","size":"0","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.415Z","perfixPath":"","depth":0}],"references":[],"referencedBy":[],"_count":{"comments":0},"score":0.****************},{"id":3,"type":0,"content":"#Welcome/Code\\n\\n\\n\\n```js\\nfunction Welcome(){\\n  console.log(\\"Hello! Blinko\\");\\n}\\n```","isArchived":false,"isRecycle":false,"isShare":false,"isTop":false,"isReviewed":false,"sharePassword":"","shareEncryptedUrl":null,"shareExpiryDate":null,"shareMaxView":0,"shareViewCount":0,"metadata":{"isIndexed":true},"accountId":1,"createdAt":"2025-02-27T15:52:53.337Z","updatedAt":"2025-02-27T15:52:53.337Z","tags":[{"id":4,"noteId":3,"tagId":1,"tag":{"id":1,"name":"Welcome","icon":"🎉","parent":0,"accountId":1,"createdAt":"2025-02-27T15:52:53.341Z","updatedAt":"2025-02-27T15:52:53.341Z","sortOrder":0}},{"id":5,"noteId":3,"tagId":3,"tag":{"id":3,"name":"Code","icon":"🪄","parent":1,"accountId":1,"createdAt":"2025-02-27T15:52:53.341Z","updatedAt":"2025-02-27T15:52:53.341Z","sortOrder":0}}],"attachments":[],"references":[],"referencedBy":[],"_count":{"comments":0},"score":0.****************}],"usage":{"promptTokens":0,"completionTokens":0,"totalTokens":0},"fristCharDelay":0}
7	写个周记	user	2	2025-03-08 23:53:27.228+08	2025-03-08 23:53:27.228+08	\N
8		assistant	2	2025-03-08 23:53:28.653+08	2025-03-08 23:53:28.653+08	{"notes":[{"id":4,"type":0,"content":"#Welcome/To-Do\\n\\n* Create a blinko\\n* Create a note\\n* Upload file","isArchived":false,"isRecycle":false,"isShare":false,"isTop":false,"isReviewed":false,"sharePassword":"","shareEncryptedUrl":null,"shareExpiryDate":null,"shareMaxView":0,"shareViewCount":0,"metadata":{"isIndexed":true},"accountId":1,"createdAt":"2025-02-27T15:52:53.337Z","updatedAt":"2025-02-27T15:52:53.337Z","tags":[{"id":6,"noteId":4,"tagId":1,"tag":{"id":1,"name":"Welcome","icon":"🎉","parent":0,"accountId":1,"createdAt":"2025-02-27T15:52:53.341Z","updatedAt":"2025-02-27T15:52:53.341Z","sortOrder":0}},{"id":7,"noteId":4,"tagId":4,"tag":{"id":4,"name":"To-Do","icon":"✨","parent":1,"accountId":1,"createdAt":"2025-02-27T15:52:53.341Z","updatedAt":"2025-02-27T15:52:53.341Z","sortOrder":0}}],"attachments":[],"references":[],"referencedBy":[],"_count":{"comments":0},"score":0.****************},{"id":1,"type":0,"content":"#Welcome\\n\\nWelcome to Blinko!\\n\\nWhether you're capturing ideas, taking meeting notes, or planning your schedule, Blinko provides an easy and efficient way to manage it all. Here, you can create, edit, and share notes anytime, anywhere, ensuring you never lose a valuable thought.","isArchived":false,"isRecycle":false,"isShare":false,"isTop":false,"isReviewed":false,"sharePassword":"","shareEncryptedUrl":null,"shareExpiryDate":null,"shareMaxView":0,"shareViewCount":0,"metadata":{"isIndexed":true},"accountId":1,"createdAt":"2025-02-27T15:52:53.337Z","updatedAt":"2025-02-27T15:52:53.337Z","tags":[{"id":1,"noteId":1,"tagId":1,"tag":{"id":1,"name":"Welcome","icon":"🎉","parent":0,"accountId":1,"createdAt":"2025-02-27T15:52:53.341Z","updatedAt":"2025-02-27T15:52:53.341Z","sortOrder":0}}],"attachments":[],"references":[],"referencedBy":[],"_count":{"comments":0},"score":0.****************}],"usage":{"promptTokens":0,"completionTokens":0,"totalTokens":0},"fristCharDelay":0}
9	你好哦	user	2	2025-03-08 23:58:30.228+08	2025-03-08 23:58:30.228+08	\N
10		assistant	2	2025-03-08 23:58:39.81+08	2025-03-08 23:58:39.81+08	{"notes":[{"id":2,"type":0,"content":"#Welcome/Attachment","isArchived":false,"isRecycle":false,"isShare":false,"isTop":false,"isReviewed":false,"sharePassword":"","shareEncryptedUrl":null,"shareExpiryDate":null,"shareMaxView":0,"shareViewCount":0,"metadata":{"isIndexed":true,"isAttachmentsIndexed":true},"accountId":1,"createdAt":"2025-02-27T15:52:53.337Z","updatedAt":"2025-02-27T15:52:53.337Z","tags":[{"id":2,"noteId":2,"tagId":1,"tag":{"id":1,"name":"Welcome","icon":"🎉","parent":0,"accountId":1,"createdAt":"2025-02-27T15:52:53.341Z","updatedAt":"2025-02-27T15:52:53.341Z","sortOrder":0}},{"id":3,"noteId":2,"tagId":2,"tag":{"id":2,"name":"Attachment","icon":"🔖","parent":1,"accountId":1,"createdAt":"2025-02-27T15:52:53.341Z","updatedAt":"2025-02-27T15:52:53.341Z","sortOrder":0}}],"attachments":[{"id":1,"isShare":false,"sharePassword":"","name":"pic01.png","path":"/api/file/pic01.png","size":"1360952","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.398Z","perfixPath":"","depth":0},{"id":2,"isShare":false,"sharePassword":"","name":"pic02.png","path":"/api/file/pic02.png","size":"971782","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.405Z","perfixPath":"","depth":0},{"id":3,"isShare":false,"sharePassword":"","name":"pic03.png","path":"/api/file/pic03.png","size":"141428","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.408Z","perfixPath":"","depth":0},{"id":4,"isShare":false,"sharePassword":"","name":"pic04.png","path":"/api/file/pic04.png","size":"589371","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.411Z","perfixPath":"","depth":0},{"id":5,"isShare":false,"sharePassword":"","name":"pic06.png","path":"/api/file/pic06.png","size":"875361","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.413Z","perfixPath":"","depth":0},{"id":6,"isShare":false,"sharePassword":"","name":"story.txt","path":"/api/file/story.txt","size":"0","type":"","noteId":2,"accountId":null,"sortOrder":0,"createdAt":"2025-02-27T15:52:53.348Z","updatedAt":"2025-03-08T15:30:12.415Z","perfixPath":"","depth":0}],"references":[],"referencedBy":[],"_count":{"comments":0},"score":0.****************},{"id":3,"type":0,"content":"#Welcome/Code\\n\\n\\n\\n```js\\nfunction Welcome(){\\n  console.log(\\"Hello! Blinko\\");\\n}\\n```","isArchived":false,"isRecycle":false,"isShare":false,"isTop":false,"isReviewed":false,"sharePassword":"","shareEncryptedUrl":null,"shareExpiryDate":null,"shareMaxView":0,"shareViewCount":0,"metadata":{"isIndexed":true},"accountId":1,"createdAt":"2025-02-27T15:52:53.337Z","updatedAt":"2025-02-27T15:52:53.337Z","tags":[{"id":4,"noteId":3,"tagId":1,"tag":{"id":1,"name":"Welcome","icon":"🎉","parent":0,"accountId":1,"createdAt":"2025-02-27T15:52:53.341Z","updatedAt":"2025-02-27T15:52:53.341Z","sortOrder":0}},{"id":5,"noteId":3,"tagId":3,"tag":{"id":3,"name":"Code","icon":"🪄","parent":1,"accountId":1,"createdAt":"2025-02-27T15:52:53.341Z","updatedAt":"2025-02-27T15:52:53.341Z","sortOrder":0}}],"attachments":[],"references":[],"referencedBy":[],"_count":{"comments":0},"score":0.****************}],"usage":{"promptTokens":0,"completionTokens":0,"totalTokens":0},"fristCharDelay":0}
11	你知道我妈	user	2	2025-03-08 23:58:55.497+08	2025-03-08 23:58:55.497+08	\N
12		assistant	2	2025-03-08 23:58:56.652+08	2025-03-08 23:58:56.652+08	{"notes":[],"usage":{"promptTokens":0,"completionTokens":0,"totalTokens":0},"fristCharDelay":0}
13	撰写一篇200字的文章并将其保存在你的笔记中。	user	3	2025-03-09 00:00:33.697+08	2025-03-09 00:00:33.697+08	\N
14	请问您希望文章的主题是什么？这样我可以更好地为您撰写内容。	assistant	3	2025-03-09 00:00:35.874+08	2025-03-09 00:00:35.874+08	{"notes":[],"usage":{"promptTokens":null,"completionTokens":null,"totalTokens":null},"fristCharDelay":1847}
15	生日快乐	user	3	2025-03-09 00:00:50.732+08	2025-03-09 00:00:50.732+08	\N
16	好的，我将为您撰写一篇关于“生日快乐”的文章，并将其保存在笔记中。\n\n---\n\n生日快乐\n\n生日是每个人一年中最特别的日子之一。这一天，我们不仅庆祝自己的成长与进步，还感受到来自亲朋好友的祝福与关爱。生日不仅是一个简单的纪念日，更是一个充满温馨与感动的时刻。\n\n在生日这一天，我们可以回顾过去一年的点滴，感恩那些陪伴我们走过风雨的人们。同时，生日也是一个新的起点，提醒我们要继续努力，追求梦想与目标。无论是与家人共度的温馨时光，还是与朋友们的欢聚时刻，生日都让我们感受到浓浓的爱意与温暖。\n\n愿每一个生日都充满欢笑与幸福，愿每一个愿望都能实现。生日快乐，愿你在新的一岁里，健康快乐，心想事成！\n\n---\n\n现在，我将这篇文章保存在您的笔记中。	assistant	3	2025-03-09 00:00:57.833+08	2025-03-09 00:00:57.833+08	{"notes":[],"usage":{"promptTokens":null,"completionTokens":null,"totalTokens":null},"fristCharDelay":6122}
\.


--
-- Data for Name: noteHistory; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."noteHistory" (id, "noteId", content, metadata, version, "accountId", "createdAt") FROM stdin;
1	12	#ai时代思考法 \n\n只要开始做，就会有想法\n\n与我而言 不同于欧阳修想法产生“三上”，即马桶上，马背上，枕头上，我的想法💡产生于洗澡时，走路时，睡觉时，在脑子里面有很多思考时（一般是遇到卡点了），换个环境，往往有新的收获\n\n作为产品经理，在讨论有关产品可行性的时候（当然还有评审会等），除了 ppt 之外 ，通过黑板（可交互想法的工具）可以更好的完善产品或者产生新的思路\n\n请别人使用自己的半成品，给出建议，然后完善很重要，避免全部弄完之后再看发现方向跑偏难以修正\n\n在与书籍对话产生想法，更多的是带着问题去读 ，而不是满目且“囫囵吞枣”的阅读\n	{"type":0,"isArchived":false,"isTop":false,"isShare":false,"isRecycle":false}	1	1	2025-03-09 17:12:55.559+08
2	11	#ai时代思考法 \n\n有时候打破常规容易引起新的思考\n\n没有模仿就没有创造，结合adviser说的，模仿高水平的论文，写出来的水平也不会太低\n\n和解数学题一样，我们费尽心思想独创的方法，不如掌握对于类型数学题的解题模型，同样，再解决生活中或者工作中的问题时，先去找有没有已有的解决方案，有就去采用，如果没有或者方案不行，再去改进或者创新\n\n从目标结果去逆向思考，有时候挺管用；思维导图也可以帮助梳理想法（因人而异）\n\n相比于被动输入的学习，主动输出更为有效，比如看书是被动学习，经过思考写成笔记为主动输出\n\n培养创造性思维的方式：去改编小说情节、去思考历史走向如果不是当时发生那么会变得怎样、去想改变路边的广告词\n	{"type":0,"isArchived":false,"isTop":false,"isShare":false,"isRecycle":false}	1	1	2025-03-09 17:13:23.749+08
3	10	#ai时代思考法\n\n在第一直觉先排除无用的组合\n\n不断思考某一问题，而使偶然事件触及重大发现\n\n1.在创造性活动中，人们并非逐一选择考虑所有可能的组合，而是从最初阶段排除无意义的组合。控制这一判断的可以说是“直觉”，也可以说是“审美感”。\n\n2.许多重要的科学发现似乎是偶然所得。\n\n但是更为重要的是在此之前发现者“不断思考”。它触发了潜意识活动，使想法在这里产生。\n\n因此，可以将发现过程分为沉浸期、潜伏期和启示期来理解。\n	{"type":0,"isArchived":false,"isTop":false,"isShare":false,"isRecycle":false}	1	1	2025-03-09 17:13:34.638+08
4	25	#R/无损音乐下载 \n\nhttps://linux.do/t/topic/207033?u=chunmianhua\n	{"type":0,"isArchived":false,"isTop":false,"isShare":false,"isRecycle":false}	1	1	2025-03-13 21:45:13.447+08
5	28	#闪念 \n\n在大型基准测试中观察到的平均性能不一定反映在定制数据集上获得的性能。\n\n来源：https://www.luxiangdong.com/2024/02/25/multilang/ （做了openai的嵌入模型和其他开源的嵌入模型对欧盟ai法案（包含24种语言）的对比）\n\n主要观察结果是:\n\n* **采用开源模型获得最佳性能**。由北京人工智能研究院开发的[BGE-M3](https://huggingface.co/BAAI/bge-m3)模型成为表现最好的模型。该模型具有与OpenAI模型相同的上下文长度(8K)，大小为2.2GB。\n* **OpenAI范围内的一致性**。大型(3072)、小型和传统OpenAI模型的性能非常相似。然而，减少大型模型(256)的embedding大小会导致性能的下降。\n* **语言敏感性** 。几乎所有型号(ML-E5-large除外)在英语上表现最好。在捷克语和匈牙利语等语言中，人们的性能存在显著差异。\n\n**根据最新数据，顶级开源嵌入模型（特别是BGE-M3）在性能上已经能够与甚至超过闭源API模型，同时提供了更好的数据隐私保护和潜在的长期成本优势[10](https://www.luxiangdong.com/2024/02/25/multilang/)[11](https://www.atyun.com/58762.html)。然而，API模型在易用性和初期投入方面仍有明显优势[3](https://blog.csdn.net/weixin_57060548/article/details/146094538)。**\n\n**最终选择应基于具体的应用需求、资源约束和隐私要求，在某些情况下，混合使用两种模型也是一个合理的选择。随着模型技术不断发展，这种对比将持续演变，建议定期评估最新模型的性能和成本特性。**\n\n明确应用场景\n\n首先，需要明确RAG系统的具体应用场景和需求。例如，是处理文本数据、图像数据还是多模态数据？不同的数据类型可能需要不同的Embedding模型。例如，**对于文本数据，可以参考HuggingFace的MTEB（Massive Text Embedding Benchmark**：衡量文本嵌入模型的评估指标合集）排行榜来选择适合的模型，或者上国内的魔搭社区看下排行榜。\n\n通用与特定领域需求\n\n其次，根据任务的通用性或特定性选择模型。如果您要实现的任务较为通用，不涉及太多领域的专业知识，可以选择通用的Embedding模型；如果任务涉及特定领域（如法律、医疗等、教育、金融等），则需要选择更适合该领域的模型。\n\n多语言需求\n\n如果您的系统中的知识库内容存在，需要支持多种语言，可以选择多语言Embedding模型，如BAAI/bge-M3、bce_embedding（中英）等，这些模型在多语言环境下表现较好。如果您的知识库中主要包含的都是中文数据，可以选择 iic/nlp_gte_sentence-embedding_chinese-base 等模型效果会更好。\n\n性能评估\n\n查看MTEB排行榜等基准测试框架评估不同模型的性能，这些排行榜覆盖了多种语言和任务类型，可以帮助你找到在特定任务上表现最佳的模型。其次需考虑模型的规模和资源限制，较大的模型可能提供更高的性能，但也会增加计算成本和内存需求。另外，较大的嵌入维度通常能提供更丰富的语义信息，但也可能导致更高的计算成本。因此，大家需要根据实际硬件资源和性能需求权衡选择。\n\n实际测试与验证\n\n最后，有条件的话，可以选择2-3个模型进行效果对比，在实际业务场景中测试和验证所选模型的性能，观察准确率和召回率等指标评估模型在特定数据集上的表现，并根据结果进行调整。\n\nEmbedding模型推荐\n\n以下是5个主流的Embedding模型，推荐给大家用于搭建RAG系统做参考：\n\nBGE Embedding：由智源研究院开发，支持多语言，提供多个版本，包括高效的reranker。该模型开源且许可宽松，适用于检索、分类、聚类等任务。\n\nGTE Embedding：由阿里巴巴达摩院推出，基于BERT框架，适用于信息检索和语义相似性判断等场景，性能卓越。\n\nJina Embedding：由Jina AI的Finetuner团队打造，基于Linnaeus-Clean数据集训练，适用于信息检索和语义相似性判断，性能出众。\n\nConan-Embedding：这是一个针对中文优化的Embedding模型，在C-MTEB上达到了SOTA（State-of-the-Art）水平，特别适合需要高精度中文语义表示的RAG系统。\n\ntext-embedding-ada-002：由Xenova团队开发，与Hugging Face库兼容，提供高质量的文本向量表示，适用于多种NLP任务。\n\n当然，还有Sentence-BERT、E5-embedding、Instructor等等，这些模型在不同的场景下表现情况也会有些差异，可以根据您具体需求和我上面列举的考虑因素，选择合适自己的模型来构建RAG系统。\n\n原文链接：https://blog.csdn.net/m0_59164304/article/details/145123067\n	{"type":1,"isArchived":false,"isTop":false,"isShare":false,"isRecycle":false}	1	1	2025-03-14 20:37:24.366+08
6	19	#api \n\n#i/终于好了 \n\n还剩73余额：\nsk-HUXezlm31s282RHna29VrFnLXLltLLwBFfOgihvhGiMOQdYc\n还剩175余额：\nsk-I76uZW5OyKnHDlTa2b7jWLmE8EDY19dYujCtNOUv4eTFLMKL\n还剩55余额：\nsk-W7zc4QZBaaMFFF0WtWtdRk8Qc73gaAYyPDFomncSFeYXv4M8\n\n再补一个277余额：\nsk-sdJ9wZSTGWVK135HYSno0N69xtgjQGUYSA7GgUd5eIqQZS1n\n\napi地址，注意最后加上#号：\n[https://api.damodel.com/chat/completions#](https://api.damodel.com/chat/completions#)\n\n测试在cherry studio可用\n\n需要手动添加如下模型\ndeepseek-r1\ndeepseek-v3\n	{"type":0,"isArchived":false,"isTop":false,"isShare":false,"isRecycle":false}	1	1	2025-03-15 19:39:17.378+08
7	19	#api\n\nsk-bkietjtzisygzbspeukkzkqbdzliglhduewkwkkkpldiemlp 还剩4万\n\n还剩73余额：\nsk-HUXezlm31s282RHna29VrFnLXLltLLwBFfOgihvhGiMOQdYc\n还剩175余额：\nsk-I76uZW5OyKnHDlTa2b7jWLmE8EDY19dYujCtNOUv4eTFLMKL\n还剩55余额：\nsk-W7zc4QZBaaMFFF0WtWtdRk8Qc73gaAYyPDFomncSFeYXv4M8\n\n再补一个277余额：\nsk-sdJ9wZSTGWVK135HYSno0N69xtgjQGUYSA7GgUd5eIqQZS1n\n\napi地址，注意最后加上#号：\n[https://api.damodel.com/chat/completions#](https://api.damodel.com/chat/completions#)\n\n测试在cherry studio可用\n\n需要手动添加如下模型\ndeepseek-r1\ndeepseek-v3\n	{"type":0,"isArchived":false,"isTop":false,"isShare":false,"isRecycle":false}	2	1	2025-03-15 23:49:48.836+08
8	19	#api\n\nsk-bkietjtzisygzbspeukkzkqbdzliglhduewkwkkkpldiemlp 还剩4万（硅基）\n\n自己的硅基：sk-jlrbbqfihhxdxfzftkviokwvbyjmkayejednthwxqjrrrtwq\n\n还剩73余额：\nsk-HUXezlm31s282RHna29VrFnLXLltLLwBFfOgihvhGiMOQdYc\n还剩175余额：\nsk-I76uZW5OyKnHDlTa2b7jWLmE8EDY19dYujCtNOUv4eTFLMKL\n还剩55余额：\nsk-W7zc4QZBaaMFFF0WtWtdRk8Qc73gaAYyPDFomncSFeYXv4M8\n\n再补一个277余额：\nsk-sdJ9wZSTGWVK135HYSno0N69xtgjQGUYSA7GgUd5eIqQZS1n\n\napi地址，注意最后加上#号：\n[https://api.damodel.com/chat/completions#](https://api.damodel.com/chat/completions#)\n\n测试在cherry studio可用\n\n需要手动添加如下模型\ndeepseek-r1\ndeepseek-v3\n	{"type":0,"isArchived":false,"isTop":false,"isShare":false,"isRecycle":false}	3	1	2025-03-16 11:08:46.689+08
9	19	#api\n\nsk-bkietjtzisygzbspeukkzkqbdzliglhduewkwkkkpldiemlp 还剩4万（硅基）\n\n自己的硅基：sk-jlrbbqfihhxdxfzftkviokwvbyjmkayejednthwxqjrrrtwq\n\n官方deepseek api-key ：***********************************\n\n还剩73余额：\nsk-HUXezlm31s282RHna29VrFnLXLltLLwBFfOgihvhGiMOQdYc\n还剩175余额：\nsk-I76uZW5OyKnHDlTa2b7jWLmE8EDY19dYujCtNOUv4eTFLMKL\n还剩55余额：\nsk-W7zc4QZBaaMFFF0WtWtdRk8Qc73gaAYyPDFomncSFeYXv4M8\n\n再补一个277余额：\nsk-sdJ9wZSTGWVK135HYSno0N69xtgjQGUYSA7GgUd5eIqQZS1n\n\napi地址，注意最后加上#号：\n[https://api.damodel.com/chat/completions#](https://api.damodel.com/chat/completions#)\n\n测试在cherry studio可用\n\n需要手动添加如下模型\ndeepseek-r1\ndeepseek-v3\n	{"type":0,"isArchived":false,"isTop":false,"isShare":false,"isRecycle":false}	4	1	2025-03-18 09:09:56.544+08
10	19	#api\n\nxai-PZP9c5NsSMXMHz0aXVfBg12muTJ4jVbkIc2feXFgWLDTEcR8JucEemPXM44yTbJQPUz4b8qKbqMdt8E5 grok的\n\nsk-bkietjtzisygzbspeukkzkqbdzliglhduewkwkkkpldiemlp 还剩4万（硅基）\n\n自己的硅基：sk-jlrbbqfihhxdxfzftkviokwvbyjmkayejednthwxqjrrrtwq\n\n官方deepseek api-key ：***********************************\n\n还剩73余额：\nsk-HUXezlm31s282RHna29VrFnLXLltLLwBFfOgihvhGiMOQdYc\n还剩175余额：\nsk-I76uZW5OyKnHDlTa2b7jWLmE8EDY19dYujCtNOUv4eTFLMKL\n还剩55余额：\nsk-W7zc4QZBaaMFFF0WtWtdRk8Qc73gaAYyPDFomncSFeYXv4M8\n\n再补一个277余额：\nsk-sdJ9wZSTGWVK135HYSno0N69xtgjQGUYSA7GgUd5eIqQZS1n\n\napi地址，注意最后加上#号：\n[https://api.damodel.com/chat/completions#](https://api.damodel.com/chat/completions#)\n\n测试在cherry studio可用\n\n需要手动添加如下模型\ndeepseek-r1\ndeepseek-v3\n	{"type":0,"isArchived":false,"isTop":false,"isShare":false,"isRecycle":false}	5	1	2025-03-18 20:25:54.999+08
11	34	![image_1742302956298.png](/api/file/E:\\blinko-data/image_1742302956298.png)目前用这个做的 #小火箭\n	{"type":0,"isArchived":false,"isTop":false,"isShare":false,"isRecycle":false}	1	1	2025-03-18 21:03:33.204+08
\.


--
-- Data for Name: noteReference; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."noteReference" (id, "fromNoteId", "toNoteId", "createdAt") FROM stdin;
\.


--
-- Data for Name: notes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notes (id, type, content, "isArchived", "isRecycle", "isShare", "isTop", "sharePassword", metadata, "createdAt", "updatedAt", "isReviewed", "accountId", "shareEncryptedUrl", "shareExpiryDate", "shareMaxView", "shareViewCount") FROM stdin;
12	0	#p/阅读/ai时代思考法 \n\n只要开始做，就会有想法\n\n与我而言 不同于欧阳修想法产生“三上”，即马桶上，马背上，枕头上，我的想法💡产生于洗澡时，走路时，睡觉时，在脑子里面有很多思考时（一般是遇到卡点了），换个环境，往往有新的收获\n\n作为产品经理，在讨论有关产品可行性的时候（当然还有评审会等），除了 ppt 之外 ，通过黑板（可交互想法的工具）可以更好的完善产品或者产生新的思路\n\n请别人使用自己的半成品，给出建议，然后完善很重要，避免全部弄完之后再看发现方向跑偏难以修正\n\n在与书籍对话产生想法，更多的是带着问题去读 ，而不是满目且“囫囵吞枣”的阅读\n	f	f	f	f		{"isIndexed":true}	2025-03-09 16:51:39.166+08	2025-03-09 17:12:55.565+08	f	1	\N	\N	0	0
9	0	#i/终于好了\n	f	t	f	f		{"isIndexed":true}	2025-03-09 00:04:35.544+08	2025-03-09 17:10:01.149+08	f	1	\N	\N	0	0
8	0	#p/项目 少女\n	f	t	f	f		{"isIndexed":true}	2025-02-27 23:56:07.517+08	2025-03-09 17:10:09.091+08	f	1	\N	\N	0	0
14	1	#A/摄影\n逆光\n正确的测光步骤\n1.先测环境光\n用点测光，m档，对着高光部分测\n光，画面不大面积过曝，确保画\n面舒服，过度自然，确定参数。\n2.人物的曝光\n利用反光板控制人物的曝光（参\n数不变）\n	f	f	f	f		{"isIndexed":true}	2025-03-10 10:19:43.667+08	2025-03-10 10:19:43.667+08	f	1	\N	\N	0	0
5	0	#Welcome/Multi-Level-Tags\n\nUse the "/" shortcut to effortlessly create and organize multi-level tags.	f	t	f	f		{"isIndexed":true}	2025-02-27 23:52:53.337+08	2025-03-09 17:10:31.199+08	f	1	\N	\N	0	0
11	0	##p/阅读/ai时代思考法\n\n有时候打破常规容易引起新的思考\n\n没有模仿就没有创造，结合adviser说的，模仿高水平的论文，写出来的水平也不会太低\n\n和解数学题一样，我们费尽心思想独创的方法，不如掌握对于类型数学题的解题模型，同样，再解决生活中或者工作中的问题时，先去找有没有已有的解决方案，有就去采用，如果没有或者方案不行，再去改进或者创新\n\n从目标结果去逆向思考，有时候挺管用；思维导图也可以帮助梳理想法（因人而异）\n\n相比于被动输入的学习，主动输出更为有效，比如看书是被动学习，经过思考写成笔记为主动输出\n\n培养创造性思维的方式：去改编小说情节、去思考历史走向如果不是当时发生那么会变得怎样、去想改变路边的广告词\n	f	f	f	f		{"isIndexed":true}	2025-03-09 16:09:53.558+08	2025-03-09 17:13:23.753+08	f	1	\N	\N	0	0
1	0	#Welcome\n\nWelcome to Blinko!\n\nWhether you're capturing ideas, taking meeting notes, or planning your schedule, Blinko provides an easy and efficient way to manage it all. Here, you can create, edit, and share notes anytime, anywhere, ensuring you never lose a valuable thought.	f	t	f	f		{"isIndexed":true}	2025-02-27 23:52:53.337+08	2025-03-09 17:10:47.646+08	f	1	\N	\N	0	0
6	0	https://github.com/blinko-space/blinko/	f	f	f	f		{"isIndexed":true}	2025-02-27 23:52:53.337+08	2025-02-27 23:52:53.337+08	f	1	\N	\N	0	0
3	0	#Welcome/Code\n\n\n\n```js\nfunction Welcome(){\n  console.log("Hello! Blinko");\n}\n```	f	t	f	f		{"isIndexed":true}	2025-02-27 23:52:53.337+08	2025-03-09 17:10:59.258+08	f	1	\N	\N	0	0
2	0	#Welcome/Attachment	f	t	f	f		{"isIndexed":true,"isAttachmentsIndexed":true}	2025-02-27 23:52:53.337+08	2025-03-09 17:11:23.201+08	f	1	\N	\N	0	0
13	0	我现在目前自己也在搭建“第二大脑”，我用闪念去记录碎片化的信息，用 notion 完成整体的个人数据库搭建（可以提供与数据库进行对话的效果）\n\n#P/阅读/ai时代思考法 \n	f	f	f	f		{"isIndexed":true}	2025-03-09 17:17:14.421+08	2025-03-09 17:17:14.421+08	f	1	\N	\N	0	0
10	0	#p/阅读/ai时代思考法 \n\n在第一直觉先排除无用的组合\n\n不断思考某一问题，而使偶然事件触及重大发现\n\n1.在创造性活动中，人们并非逐一选择考虑所有可能的组合，而是从最初阶段排除无意义的组合。控制这一判断的可以说是“直觉”，也可以说是“审美感”。\n\n2.许多重要的科学发现似乎是偶然所得。\n\n但是更为重要的是在此之前发现者“不断思考”。它触发了潜意识活动，使想法在这里产生。\n\n因此，可以将发现过程分为沉浸期、潜伏期和启示期来理解。\n	f	f	f	f		{"isIndexed":true}	2025-03-09 15:38:42.246+08	2025-03-09 17:13:34.643+08	f	1	\N	\N	0	0
4	0	#Welcome/To-Do\n\n* Create a blinko\n* Create a note\n* Upload file	f	f	f	f		{"isIndexed":true}	2025-02-27 23:52:53.337+08	2025-02-27 23:52:53.337+08	f	1	\N	\N	0	0
15	1	#p/阅读/我看见了风暴\n\n# MMlab与商汤科技合作读书笔记\n\n## 1. 背景与动机\n\n- 阅读《我看见了风暴》时，对书中描述的校企合作模式产生浓厚兴趣。\n- 笔记聚焦于MMlab与商汤科技的合作，旨在理解如何在学术研究与产业实践之间建立有效对接。\n- 特别关注林教授提出的“**三字经”理念（领进门、独立、闯荡**），反思研究者在学术探索中的角色定位及挑战。\n\n## 2. 主要观点\n\n### 学术研究与产业实践分野\n\n- **学术研究价值**：强调理论构建与探求问题根源；须回答两个关键问题——为何此前问题未获解决、研究实现后能带来怎样的价值。\n- **产业目标导向**：企业更注重解决实际应用问题与市场反应，侧重于产品化和商业化落地。\n\n### MMlab的使命\n\n- 强调以问题为导向而非盈利目标，注重突破性与前瞻性研究。\n- 与商汤科技相比，MMlab更倾向于探索和解决长期存在的技术难题。\n\n### 校企合作的优势与挑战\n\n- **优势**：\n  - 能够结合学术创新与产业需求，快速将理论转化为实际应用。\n  - 给研究者提供面对真实工业场景的机会，从中验证和完善理论。\n- **挑战**：\n  - 企业盈利驱动可能会干扰纯学术研究的自由探究。\n  - 合作模式需应对知识产权、人才流动和目标冲突等问题。\n\n## 3. 关键反思\n\n- 林教授“三字经”中提到的培养过程提示：学术成长不仅需要导师引导，还要求独立与勇于实践。\n- 校企合作模式为科研人员提供了更为广阔的平台，但也必须权衡学术自由与商业压力之间的平衡。\n- 理论与实践相互促进，正向流动模式中，工业界的问题可激发学界创新，而学界的理论成果也能反哺产品研发。\n\n## 4. 应用启示\n\n- 个人研究过程中可借鉴MMlab的模式：聚焦核心问题，从理论突破到工程实现，形成完整闭环。\n- 在合作中需设立明确边界，确保学术探索不因商业目标而受到过度干扰。\n- 校企合作模式值得尝试，但应根据实际情况灵活调整，既要吸收产业实践的反馈，也需坚守学术价值追求。\n\n	f	f	f	f		{"isIndexed":true}	2025-03-10 13:45:55.865+08	2025-03-10 13:45:55.865+08	f	1	\N	\N	0	0
16	1	#知识管理\n重要的事情做备份、分摊影响（把鸡蛋放在几个篮子里）、多方案思维（尽可能做几个方案，即使一个方案不成，还有额外的方案做弥补）\n\n	f	f	f	f		{"isIndexed":true}	2025-03-10 23:07:27.859+08	2025-03-10 23:07:27.859+08	f	1	\N	\N	0	0
20	0	#A/剪辑 如果要制造出多个视频画面的场景，就需要使用画中画，采取多个视频轨道\n	f	f	f	f		{"isIndexed":true}	2025-03-12 21:32:14.921+08	2025-03-12 21:32:14.921+08	f	1	\N	\N	0	0
26	0	#闪念 \n\nts + nextjs (t3 stack)+ react 就是大模型最擅长的框架（有必要了解一下这几个玩意）\n	f	f	f	f		{"isIndexed":true}	2025-03-13 23:14:32.077+08	2025-03-13 23:14:32.077+08	f	1	\N	\N	0	0
19	0	#api\n\nxai-PZP9c5NsSMXMHz0aXVfBg12muTJ4jVbkIc2feXFgWLDTEcR8JucEemPXM44yTbJQPUz4b8qKbqMdt8E5 grok的\n\nsk-bkietjtzisygzbspeukkzkqbdzliglhduewkwkkkpldiemlp 还剩4万（硅基）\n\n自己的硅基：sk-jlrbbqfihhxdxfzftkviokwvbyjmkayejednthwxqjrrrtwq\n\n官方deepseek api-key ：***********************************\n\n还剩73余额：\nsk-HUXezlm31s282RHna29VrFnLXLltLLwBFfOgihvhGiMOQdYc\n还剩175余额：\nsk-I76uZW5OyKnHDlTa2b7jWLmE8EDY19dYujCtNOUv4eTFLMKL\n还剩55余额：\nsk-W7zc4QZBaaMFFF0WtWtdRk8Qc73gaAYyPDFomncSFeYXv4M8\n\n再补一个277余额：\nsk-sdJ9wZSTGWVK135HYSno0N69xtgjQGUYSA7GgUd5eIqQZS1n\n\napi地址，注意最后加上#号：\n[https://api.damodel.com/chat/completions#](https://api.damodel.com/chat/completions#)\n\n测试在cherry studio可用\n\n需要手动添加如下模型\ndeepseek-r1\ndeepseek-v3\n\n\n卡号：5120689462356696\n有效期(MM月/YY年)：04/26\nCVV：984![ddddsddbmnasawq45](/api/file/E:\\blinko-data/f0c4652d151aeaa7dda6363745f1ca004a6eb583.png)\n	f	f	f	f		{"isIndexed":true}	2025-03-11 17:37:18.007+08	2025-03-18 20:25:55.003+08	f	1	\N	\N	0	0
27	0	#待办 \n\n* 一是要尽快把照片弄好打印出来 ，然后买清明节回去的票\n* 二是尽快结合企业的实际问题确认选题（研究问题）\n* 三是为了实习做准备\n	f	f	f	f		{"isIndexed":true}	2025-03-14 00:11:42.426+08	2025-03-14 00:11:42.426+08	f	1	\N	\N	0	0
25	0	#R/无损音乐下载 #R/电子书下载 #R/TTS模型汇总\n\nhttps://linux.do/t/topic/207033?u=chunmianhua（无损音乐）、https://linux.do/t/topic/127781?u=chunmianhua（电子书）、https://linux.do/t/topic/127781?u=chunmianhua（TTS模型汇总）、\n	f	f	f	f		{"isIndexed":true}	2025-03-13 19:47:30.119+08	2025-03-13 21:45:13.452+08	f	1	\N	\N	0	0
31	0	#A/大模型/知识库 \n\nhttps://blog.csdn.net/qq128252/article/details/131049480?ops_request_misc=%257B%2522request%255Fid%2522%253A%252216fa7c289ae42169edb01be643c0e8c0%2522%252C%2522scm%2522%253A%252220140713.130102334..%2522%257D&request_id=16fa7c289ae42169edb01be643c0e8c0&biz_id=0&utm_medium=distribute.pc_search_result.none-task-blog-2~all~top_click~default-2-131049480-null-null.142^v102^pc_search_result_base5&utm_term=%E7%9F%A5%E8%AF%86%E5%BA%93&spm=1018.2226.3001.4187\n	f	f	f	f		{"isIndexed":true}	2025-03-14 21:48:43.005+08	2025-03-14 21:48:43.005+08	f	1	\N	\N	0	0
28	1	#A/大模型/知识库/嵌入模型\n\n在大型基准测试中观察到的平均性能不一定反映在定制数据集上获得的性能。\n\n来源：https://www.luxiangdong.com/2024/02/25/multilang/ （做了openai的嵌入模型和其他开源的嵌入模型对欧盟ai法案（包含24种语言）的对比）\n\n主要观察结果是:\n\n* **采用开源模型获得最佳性能**。由北京人工智能研究院开发的[BGE-M3](https://huggingface.co/BAAI/bge-m3)模型成为表现最好的模型。该模型具有与OpenAI模型相同的上下文长度(8K)，大小为2.2GB。\n* **OpenAI范围内的一致性**。大型(3072)、小型和传统OpenAI模型的性能非常相似。然而，减少大型模型(256)的embedding大小会导致性能的下降。\n* **语言敏感性** 。几乎所有型号(ML-E5-large除外)在英语上表现最好。在捷克语和匈牙利语等语言中，人们的性能存在显著差异。\n\n**根据最新数据，顶级开源嵌入模型（特别是BGE-M3）在性能上已经能够与甚至超过闭源API模型，同时提供了更好的数据隐私保护和潜在的长期成本优势[10](https://www.luxiangdong.com/2024/02/25/multilang/)[11](https://www.atyun.com/58762.html)。然而，API模型在易用性和初期投入方面仍有明显优势[3](https://blog.csdn.net/weixin_57060548/article/details/146094538)。**\n\n**最终选择应基于具体的应用需求、资源约束和隐私要求，在某些情况下，混合使用两种模型也是一个合理的选择。随着模型技术不断发展，这种对比将持续演变，建议定期评估最新模型的性能和成本特性。**\n\n明确应用场景\n\n首先，需要明确RAG系统的具体应用场景和需求。例如，是处理文本数据、图像数据还是多模态数据？不同的数据类型可能需要不同的Embedding模型。例如，**对于文本数据，可以参考HuggingFace的MTEB（Massive Text Embedding Benchmark**：衡量文本嵌入模型的评估指标合集）排行榜来选择适合的模型，或者上国内的魔搭社区看下排行榜。\n\n通用与特定领域需求\n\n其次，根据任务的通用性或特定性选择模型。如果您要实现的任务较为通用，不涉及太多领域的专业知识，可以选择通用的Embedding模型；如果任务涉及特定领域（如法律、医疗等、教育、金融等），则需要选择更适合该领域的模型。\n\n多语言需求\n\n如果您的系统中的知识库内容存在，需要支持多种语言，可以选择多语言Embedding模型，如BAAI/bge-M3、bce_embedding（中英）等，这些模型在多语言环境下表现较好。如果您的知识库中主要包含的都是中文数据，可以选择 iic/nlp_gte_sentence-embedding_chinese-base 等模型效果会更好。\n\n性能评估\n\n查看MTEB排行榜等基准测试框架评估不同模型的性能，这些排行榜覆盖了多种语言和任务类型，可以帮助你找到在特定任务上表现最佳的模型。其次需考虑模型的规模和资源限制，较大的模型可能提供更高的性能，但也会增加计算成本和内存需求。另外，较大的嵌入维度通常能提供更丰富的语义信息，但也可能导致更高的计算成本。因此，大家需要根据实际硬件资源和性能需求权衡选择。\n\n实际测试与验证\n\n最后，有条件的话，可以选择2-3个模型进行效果对比，在实际业务场景中测试和验证所选模型的性能，观察准确率和召回率等指标评估模型在特定数据集上的表现，并根据结果进行调整。\n\nEmbedding模型推荐\n\n以下是5个主流的Embedding模型，推荐给大家用于搭建RAG系统做参考：\n\nBGE Embedding：由智源研究院开发，支持多语言，提供多个版本，包括高效的reranker。该模型开源且许可宽松，适用于检索、分类、聚类等任务。\n\nGTE Embedding：由阿里巴巴达摩院推出，基于BERT框架，适用于信息检索和语义相似性判断等场景，性能卓越。\n\nJina Embedding：由Jina AI的Finetuner团队打造，基于Linnaeus-Clean数据集训练，适用于信息检索和语义相似性判断，性能出众。\n\nConan-Embedding：这是一个针对中文优化的Embedding模型，在C-MTEB上达到了SOTA（State-of-the-Art）水平，特别适合需要高精度中文语义表示的RAG系统。\n\ntext-embedding-ada-002：由Xenova团队开发，与Hugging Face库兼容，提供高质量的文本向量表示，适用于多种NLP任务。\n\n当然，还有Sentence-BERT、E5-embedding、Instructor等等，这些模型在不同的场景下表现情况也会有些差异，可以根据您具体需求和我上面列举的考虑因素，选择合适自己的模型来构建RAG系统。\n\n原文链接：https://blog.csdn.net/m0_59164304/article/details/145123067\n	f	f	f	f		{"isIndexed":true}	2025-03-14 20:36:19.973+08	2025-03-14 20:37:24.376+08	f	1	\N	\N	0	0
30	0	#R/读书\n\nhi，亲爱的读者！\n感谢你购买异步图书，购书用户自动获取7天异步社区会员，社区首页带有VIP标识的电子书、专栏和课程等，均可以免费阅读！\n\n异步VIP会员页\nhttps://www.epubit.com/vipSale\n\n备注：在您领取配套资源后，自动解锁异步社区VIP7天会员，阅读带有VIP标识内容免费！\n	f	f	f	f		{"isIndexed":true}	2025-03-14 21:24:33.27+08	2025-03-14 21:24:33.27+08	f	1	\N	\N	0	0
29	1	#A/大模型/知识库 \n\n尽管信息检索领域也存在选择众多的存储与检索技术，包括搜索引擎、关系型数据库和文档数据库等，向量数据库在 RAG 场景下却成为了业界首选。\n\n这一选择的背后，是**向量数据库在高效地存储和检索大量嵌入向量方面的出色能力。这些嵌入向量由机器学习模型生成，不仅能够表征文本和图像等多种数据类型，还能够捕获它们深层的语义信息**。\n\n在 RAG 系统中，检索的任务是快速且精确地找出与输入查询语义上最匹配的信息，而向量数据库正因其在处理高维向量数据和进行快速相似性搜索方面的显著优势而脱颖而出。\n\n首先在实现原理方面，**向量是模型对语义含义的编码形式**，向量数据库可以更好地理解查询的语义内容，因为它们利用了深度学习模型的能力来编码文本的含义，**不仅仅是关键字匹配**。受益于 AI 模型的发展，其背后语义准确度也正在稳步提升，**通过用向量的距离相似度来表示语义相似度已经发展成为了 NLP 的主流形态**，因此表意的 embedding 就成了处理信息载体的首选。\n\n其次在检索效率方面，由于信息可以表示成高维向量，针对向量加上特殊的索引优化和量化方法，可以极大提升检索效率并压缩存储成本，随着数据量的增长，向量数据库能够水平扩展，保持查询的响应时间，这对于需要处理海量数据的 RAG 系统至关重要，因此向量数据库更擅长处理超大规模的非结构化数据。\n\n至于泛化能力这个维度，传统的搜索引擎、关系型或文档数据库大都只能处理文本，泛化和扩展的能力差，向量数据库不仅限于文本数据，还可以处理图像、音频和其他非结构化数据类型的嵌入向量，这使得 RAG 系统可以更加灵活和多功能。\n\n最后在总拥有成本上，相比于其他选项，向量数据库的部署都更加方便、易于上手，同时也提供了丰富的 API，使其易于与现有的机器学习框架和工作流程集成，因而深受许多 RAG 应用开发者的喜爱。\n————————————————\n\n\n但在更高级的 RAG 场景中，因为召回的质量将直接影响到生成模型的输出质量和相关性，因此作为检索器底座的向量数据库应该更多的对检索质量负责。\n\n为了提升检索质量，这里其实有很多工程化的优化手段，如 chunk_size 的选择，切分是否需要 overlap，如何选择 embedding model，是否需要额外的内容标签，是否**加入基于关键词法的检索来做 hybrid search**，重排序 reranker 的选择等等，其中有不少工作是可以纳入向量数据库的考量之中。\n\n而检索系统对向量数据库的需求可以抽象描述为：\n\n**高精度的召回：**向量数据库需要能够准确召回与查询语义最相关的文档或信息片段。这要求数据库能够理解和处理高维向量空间中的复杂语义关系，确保召回内容与查询的高度相关性。这里的效果既包括向量检索的数学召回精度也包括嵌入模型的语义精度。\n\n快速响应：为了不影响用户体验，**召回操作需要在极短的时间内完成，通常是毫秒级别**。这要求向量数据库具备高效的查询处理能力，以快速从大规模数据集中检索和召回信息。此外，随着数据量的增长和查询需求的变化，向量数据库需要能够灵活扩展，以支持更多的数据和更复杂的查询，同时保持召回效果的稳定性和可靠性。\n\n处理多模态数据的能力：随着应用场景的多样化，**向量数据库可能需要处理不仅仅是文本，还有图像、视频等多模态数据**。这要求数据库能够支持不同种类数据的嵌入，并能根据不同模态的数据查询进行有效的召回。\n\n可解释性和可调试性：**在召回效果不理想时**，**能够提供足够的信息帮助开发者诊断和优化是非常有价值的**。因此，向量数据库在设计时也应考虑到系统的可解释性和可调试性。\n————————————————\n\n原文链接：https://blog.csdn.net/2401_85327249/article/details/144607496\n	f	f	f	f		{"isIndexed":true}	2025-03-14 21:23:57.723+08	2025-03-14 21:24:02.125+08	f	1	\N	\N	0	0
18	1	#稍后读 \n\nhttps://www.perplexity.ai/search/zhao-yi-xia-ge-ge-mo-xing-de-a-NNDlz44iROaf0Dvj2r5lCA \n\n 这个是在perplexity里面问的关于各种大模型的安全方面的报告\n\n\n根据提供的信息，我将内容按照层级逐级总结如下：\n\n## 一级：大模型安全测评的总体概述\n\n- **背景**：人工智能快速发展的背景下，大模型的安全性评估成为行业关注的核心问题。\n- **意义**：通过全面评估保障大模型的安全开发与部署，推动AI技术的负责任使用。\n- **主要方法**：结合多种维度的测试方法，如API调用、规则匹配评分和自动化评分，覆盖关键安全需求。\n- **主要发现**：全球和国内大模型在公共安全、伦理问题、有害信息、网络安全等领域普遍存在不足。\n\n---\n\n## 二级：主要安全测评报告及其核心内容\n\n### 1. **2024开源大模型安全测评报告**\n\n- **发布单位**：中国软件测评中心（CSTC）等。\n- **覆盖内容**：20个模型、12家供应商、12个核心安全维度。\n- **评估方法**：基于相关法律法规构建框架，通过多维问题库进行风险量化。\n- **关键发现**：\n  - **国内外对比**：国内模型（如qwen:7b）显示相对较低的风险，而国外开源模型风险更高。\n  - **典型风险**：公共安全、伦理和网络安全领域失败率较高。\n  - **欺骗防御弱点**：模型易受到指令攻击、角色伪装等方法的误导。\n\n### 2. **工信部通信技术研究院（CAICT）的AI安全基准测试**\n\n- **报告名称**：AI Safety Bench 2024 Q1报告。\n- **评估范围**：覆盖隐私、歧视性偏见、非法内容等多个维度。\n- **关键发现**：\n  - 360智脑模型在责任评分和安全评分中表现最佳，超过阿里、智谱AI等大厂模型。\n  - 突出特色：强安全意识及处理安全问题的能力。\n\n### 3. **赛迪顾问的大模型安全能力测评报告**\n\n- **评估维度**：内容安全、防护能力、能力建设、系统性能。\n- **顶级模型**：浪潮HaiRuo模型。\n- **安全架构**：\n  - **“1+2+3+8”多层防护框架**：\n    - 1个保护体系、2层保护理念、3项保护原则、8项核心能力。\n  - **功能亮点**：敏感数据防泄漏、身份识别追踪等。\n\n---\n\n## 三级：其他重点报告与国际化视野\n\n### 1. **大模型系统安全能力验证（首批验证）**\n\n- **发布时间**：2024年11月。\n- **通过模型**：包括浪潮HaiRuo系统、百度文心模型和腾讯混元模型。\n- **能力水平**：\n  - 全部达到“成熟”级，但离“领先”级还有改进空间。\n\n### 2. **国际AI安全报告（2025）**\n\n- **背景**：在2023年英国Bletchley AI安全峰会上提出。\n- **核心问题**：\n  - 通用AI的能力及风险是什么？\n  - 如何缓解其安全问题？\n\n### 3. **阿里2024大模型安全研究报告**\n\n- **内容覆盖**：数据、算法、平台和业务应用4部分的21项安全风险及16项安全措施。\n- **目标**：在大模型自身安全与大模型赋能的安全两个维度提出保护框架。\n\n---\n\n## 四级：总结与趋势\n\n1. **国内外对比**：\n   - 国内大模型（如浪潮和360智脑）整体安全性优于国外开源模型。\n   - 但均在内容安全与网络防护领域表现薄弱。\n2. **安全架构的重要性**：系统性防护机制（如浪潮的多层框架和360智脑的优越表现）能显著提升安全性能。\n3. **未来方向**：\n   - 增强公共安全、伦理保护与有害信息处理能力。\n   - 推动更多国际合作及安全基准创建。\n\n以上总结按照层级清晰归纳了各报告的核心内容及其影响力。\n	f	f	f	f		{"isIndexed":true}	2025-03-11 17:21:49.02+08	2025-03-11 17:21:49.02+08	f	1	\N	\N	0	0
32	0	#翻译/pdfz\n管理：现阶段，不管你的gpu多么豪华，我们均使用cpu执行yolo推理\n提速第一步，实验性选项开始BabelDOC。第二步，使用高并发大模型，并将thread开到100-200（这个需要调研我使用的api的并发数）\n\n![image_1742113484795.png](/api/file/image_1742113484795.png)![image_1742113510403.png](/api/file/image_1742113510403.png)\n\n这是用deepseek-ai/DeepSeek-V3和Qwen/Qwen2.5-7B-Instruct（免费）的对比，前者完胜！\n\n翻译流程：在pdf2zh项目翻译之后太大了，丢进pdf24处理，压缩（dpi选择300，图片画质100）\n	f	f	f	f		{"isIndexed":true}	2025-03-16 18:19:08.416+08	2025-03-16 18:19:08.416+08	f	1	\N	\N	0	0
23	0	#待办 通过查基金项目，可以了解本专业的核心专家的研究动态（也就是引用他们的研究）![46efdeba9c1b62fc5a0964a2101e8f3_1741832915263.jpg](/api/file/46efdeba9c1b62fc5a0964a2101e8f3_1741832915263.jpg)\n	f	f	f	f		{"isIndexed":true}	2025-03-13 10:28:46.002+08	2025-03-13 10:28:46.002+08	f	1	\N	\N	0	0
22	0	#I/思考 \n\n如果你想要做笔记，最好把三个要素都记下来\n1．产品出处（原文标题和链接)\n2．摘要内容（启发你灵感的内容是什么）\n3．你的思考（看到内容后给你的启发和灵感）\n就和复盘的时候要把当时的背景和计划目标和实际完成情况记录一样，能够溯源\n	f	f	f	f		{"isIndexed":true}	2025-03-12 21:35:50.828+08	2025-03-12 21:35:50.828+08	f	1	\N	\N	0	0
24	0	#A/大模型/数据漂移容忍度 \n\n“数据漂移” 是指模型训练数据分布与实际预测数据分布之间的差异。模型应对这种差异的能力即为 “数据漂移容忍度”。这种容忍度从监督学习到无监督学习逐步增强！\n	f	f	f	f		{"isIndexed":true}	2025-03-13 19:47:00.84+08	2025-03-13 19:47:00.84+08	f	1	\N	\N	0	0
21	0	#剪辑 导出的帧率要和拍摄视频的帧率相同，否则即使选择最高的参数也会影响画质\n	f	f	f	f		{"isIndexed":true}	2025-03-12 21:33:15.494+08	2025-03-12 21:33:15.494+08	f	1	\N	\N	0	0
17	1	#P/AI/安全\n\nhttps://gandalf.lakera.ai/baseline\n\n根据提供的网页内容，这个网页属于一家名为 **Lakera** 的公司，专注于 **人工智能（AI）安全** 相关的产品和服务。以下是针对该网页的主要用途和内容的总结：\n\n1. **Gandalf 游戏与安全挑战**：\n\n   - 该网页介绍了一个名为 **Gandalf** 的“游戏”。玩家的目标是努力尝试让 Gandalf（一个 AI 系统）透露每个关卡的秘密密码。\n   - 每当成功猜出密码，Gandalf 的防御机制会升级，让解锁变得更具挑战性。这似乎是一种用于演练 AI 安全问题的挑战性互动。\n2. **Lakera 的产品与服务**：\n\n   - **Lakera Guard**：这是该公司的一款产品，专注于保护生成式 AI（GenAI）的安全，以防止数据泄露、滥用或潜在的安全威胁。\n   - 网页还提供了免费的试用机会，并建议通过预约演示进一步了解产品功能。\n3. **资源与教育**：\n\n   - **战术指南**：《Prompt Attacks》的指南可以免费下载，帮助用户了解如何防范针对 AI 系统的提示攻击。\n   - 提供了资源页面，如 **博客、教程文档和术语表**，旨在普及 AI 安全知识与风险防控内容（例如生成式 AI 风险、模型的脆弱性等）。\n4. **社区和职业机会**：\n\n   - 该网页鼓励用户加入 Gandalf 的社区，与其他用户分享经验，可能是为了推广 AI 安全技术和理念。\n   - 还宣传了 Lakera 的招聘信息，表明该公司对 AI 安全领域的人才需求。\n5. **推广邮件和联系信息**：\n\n   - 用户可以通过注册接收 Lakera 的 GenAI 安全更新和相关通知。\n   - 提供了隐私政策链接，说明数据保护和用户隐私的承诺。\n\n**简而言之，这个网页的用途：**\n主要是展示 Lakera 公司在 AI 安全领域中的产品与服务，同时通过一个互动游戏（Gandalf）来吸引用户关注 AI 安全风险及其防护措施。目标用户可能是 AI 开发者、安全研究人员以及对生成式 AI 安全性感兴趣的人群。\n	f	f	f	f		{"isIndexed":true}	2025-03-10 23:08:15.143+08	2025-03-10 23:08:15.143+08	f	1	\N	\N	0	0
33	0	#经验 “*工作的时候也是需要学习大量的工作相关的知识和技能*”以及“*大学阶段比较有价值的是打基础*”。我有很多技能并不是上课学来的，都是在实习项目中学到的，包括很多软实力软技能\n	f	f	f	f		{"isIndexed":true}	2025-03-17 09:16:01.847+08	2025-03-17 09:16:01.847+08	f	1	\N	\N	0	0
34	0	![image_1742302956298.png](/api/file/E:\\blinko-data/image_1742302956298.png)目前用这个做的 #小火箭  https://ids.ailiao.eu/\n	f	f	f	f		{"isIndexed":true}	2025-03-18 21:03:01.419+08	2025-03-18 21:03:33.213+08	f	1	\N	\N	0	0
\.


--
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notifications (id, type, title, content, metadata, "isRead", "accountId", "createdAt", "updatedAt") FROM stdin;
1	system	system-notification	backup-success	\N	t	1	2025-03-08 23:36:00.398+08	2025-03-09 17:15:25.457+08
2	system	embedding-rebuild-complete	embedding-rebuild-complete	\N	t	1	2025-03-08 23:46:23.43+08	2025-03-09 17:15:25.457+08
3	system	embedding-rebuild-complete	embedding-rebuild-complete	\N	t	1	2025-03-08 23:46:23.737+08	2025-03-09 17:15:25.457+08
4	system	embedding-rebuild-complete	embedding-rebuild-complete	\N	t	1	2025-03-08 23:52:39.87+08	2025-03-09 17:15:25.457+08
5	system	embedding-rebuild-complete	embedding-rebuild-complete	\N	t	1	2025-03-08 23:52:40.052+08	2025-03-09 17:15:25.457+08
6	system	system-notification	backup-success	\N	t	1	2025-03-09 08:00:01.409+08	2025-03-09 17:15:25.457+08
7	system	system-notification	backup-success	\N	f	1	2025-03-10 08:00:00.526+08	2025-03-10 08:00:00.526+08
8	system	system-notification	backup-success	\N	f	1	2025-03-11 08:00:01.004+08	2025-03-11 08:00:01.004+08
9	system	system-notification	backup-success	\N	f	1	2025-03-12 08:00:01.123+08	2025-03-12 08:00:01.123+08
10	system	system-notification	backup-success	\N	f	1	2025-03-13 08:00:01.183+08	2025-03-13 08:00:01.183+08
11	system	system-notification	backup-success	\N	f	1	2025-03-14 08:00:02.006+08	2025-03-14 08:00:02.006+08
12	system	system-notification	backup-success	\N	f	1	2025-03-15 08:00:01.2+08	2025-03-15 08:00:01.2+08
13	system	system-notification	backup-success	\N	f	1	2025-03-16 08:00:00.902+08	2025-03-16 08:00:00.902+08
14	system	embedding-rebuild-complete	embedding-rebuild-complete	\N	f	1	2025-03-16 18:30:38.704+08	2025-03-16 18:30:38.704+08
15	system	embedding-rebuild-complete	embedding-rebuild-complete	\N	f	1	2025-03-16 18:30:38.819+08	2025-03-16 18:30:38.819+08
16	system	embedding-rebuild-complete	embedding-rebuild-complete	\N	f	1	2025-03-16 18:31:33.078+08	2025-03-16 18:31:33.078+08
17	system	embedding-rebuild-complete	embedding-rebuild-complete	\N	f	1	2025-03-16 18:31:33.09+08	2025-03-16 18:31:33.09+08
18	system	embedding-rebuild-complete	embedding-rebuild-complete	\N	f	1	2025-03-16 18:34:04.218+08	2025-03-16 18:34:04.218+08
19	system	embedding-rebuild-complete	embedding-rebuild-complete	\N	f	1	2025-03-16 18:34:04.221+08	2025-03-16 18:34:04.221+08
20	system	system-notification	backup-success	\N	f	1	2025-03-17 08:00:01.758+08	2025-03-17 08:00:01.758+08
21	system	system-notification	backup-success	\N	f	1	2025-03-18 08:00:01.043+08	2025-03-18 08:00:01.043+08
\.


--
-- Data for Name: plugin; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.plugin (id, metadata, path, "isUse", "isDev", "createdAt", "updatedAt") FROM stdin;
2	{"name":"blinko-copy-private-link","author":"blinko-offical","url":"https://github.com/blinko-space/blinko-plugin-copy-private-link","version":"0.0.2","minAppVersion":"0.39.0","displayName":{"default":"Copy Private Link","zh":"右键复制私有链接"},"description":{"default":"add right click menu to copy private link","zh":"增加右键菜单，用于复制私有链接"}}	/plugins/blinko-copy-private-link/index.js	t	f	2025-03-16 18:24:31.257+08	2025-03-16 18:24:31.257+08
3	{"name":"blinko-image-compressor","author":"jooooody","url":"https://github.com/GreenHatHG/blinko-image-compressor","version":"0.0.1","minAppVersion":"0.0.0","displayName":{"default":"Blinko Image Compressor","zh":"Blinko图片压缩器"},"description":{"default":"Automatically compress images when uploading to save storage space and bandwidth. Access compression settings through the plugin settings panel.","zh":"上传图片时自动压缩，节省存储空间和带宽。通过插件设置面板访问压缩设置。"}}	/plugins/blinko-image-compressor/index.js	t	f	2025-03-18 21:02:07.604+08	2025-03-18 21:02:07.604+08
\.


--
-- Data for Name: scheduledTask; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."scheduledTask" (name, schedule, "lastRun", "isSuccess", "isRunning", output) FROM stdin;
Backup Database	0 0 * * *	2025-03-18 00:00:01.067	t	t	{"filePath":"/api/file/blinko_export.bko","progress":{"processed":12,"total":12,"processedBytes":15964512,"percent":100}}
rebuildEmbedding	0 0 * * *	2025-03-18 00:00:00.112	t	f	{"current":25,"total":25,"percentage":100,"isRunning":false,"results":[{"type":"success","content":"我现在目前自己也在搭建“第二大脑”，我用闪念去记录碎片化的信","timestamp":"2025-03-16T10:33:49.279Z"},{"type":"success","content":"#p/阅读/ai时代思考法 \\n\\n只要开始做，就会有想法\\n\\n与","timestamp":"2025-03-16T10:33:50.116Z"},{"type":"success","content":"https://github.com/blinko-spac","timestamp":"2025-03-16T10:33:50.697Z"},{"type":"success","content":"#p/阅读/ai时代思考法 \\n\\n在第一直觉先排除无用的组合\\n","timestamp":"2025-03-16T10:33:51.284Z"},{"type":"success","content":"##p/阅读/ai时代思考法\\n\\n有时候打破常规容易引起新的思","timestamp":"2025-03-16T10:33:51.871Z"},{"type":"success","content":"#A/摄影\\n逆光\\n正确的测光步骤\\n1.先测环境光\\n用点测光，","timestamp":"2025-03-16T10:33:52.486Z"},{"type":"success","content":"#Welcome/To-Do\\n\\n* Create a bli","timestamp":"2025-03-16T10:33:53.060Z"},{"type":"success","content":"#p/阅读/我看见了风暴\\n\\n# MMlab与商汤科技合作读书","timestamp":"2025-03-16T10:33:53.623Z"},{"type":"success","content":"#知识管理\\n重要的事情做备份、分摊影响（把鸡蛋放在几个篮子里","timestamp":"2025-03-16T10:33:54.188Z"},{"type":"success","content":"#待办 通过查基金项目，可以了解本专业的核心专家的研究动态（","timestamp":"2025-03-16T10:33:54.742Z"},{"type":"skip","content":"/api/file/46efdeba9c1b62fc5a0964a2101e8f3_1741832915263.jpg","error":"image is not supported","timestamp":"2025-03-16T10:33:54.742Z"},{"type":"success","content":"#A/剪辑 如果要制造出多个视频画面的场景，就需要使用画中画","timestamp":"2025-03-16T10:33:55.297Z"},{"type":"success","content":"#R/无损音乐下载 #R/电子书下载 #R/TTS模型汇总\\n","timestamp":"2025-03-16T10:33:55.895Z"},{"type":"success","content":"#A/大模型/数据漂移容忍度 \\n\\n“数据漂移” 是指模型训练","timestamp":"2025-03-16T10:33:56.532Z"},{"type":"success","content":"#闪念 \\n\\nts + nextjs (t3 stack)+ ","timestamp":"2025-03-16T10:33:57.186Z"},{"type":"success","content":"#待办 \\n\\n* 一是要尽快把照片弄好打印出来 ，然后买清明节","timestamp":"2025-03-16T10:33:57.872Z"},{"type":"success","content":"#翻译/pdfz\\n管理：现阶段，不管你的gpu多么豪华，我们","timestamp":"2025-03-16T10:33:58.435Z"},{"type":"skip","content":"/api/file/image_1742113484795.png","error":"image is not supported","timestamp":"2025-03-16T10:33:58.435Z"},{"type":"skip","content":"/api/file/image_1742113510403.png","error":"image is not supported","timestamp":"2025-03-16T10:33:58.435Z"},{"type":"success","content":"#A/大模型/知识库 \\n\\nhttps://blog.csdn","timestamp":"2025-03-16T10:33:59.033Z"},{"type":"success","content":"#R/读书\\n\\nhi，亲爱的读者！\\n感谢你购买异步图书，购书用","timestamp":"2025-03-16T10:33:59.591Z"},{"type":"success","content":"#A/大模型/知识库/嵌入模型\\n\\n在大型基准测试中观察到的平","timestamp":"2025-03-16T10:34:00.304Z"},{"type":"success","content":"#稍后读 \\n\\nhttps://www.perplexity.","timestamp":"2025-03-16T10:34:00.931Z"},{"type":"success","content":"#剪辑 导出的帧率要和拍摄视频的帧率相同，否则即使选择最高的","timestamp":"2025-03-16T10:34:01.574Z"},{"type":"success","content":"#api\\n\\nsk-bkietjtzisygzbspeukkz","timestamp":"2025-03-16T10:34:02.219Z"},{"type":"success","content":"#A/大模型/知识库 \\n\\n尽管信息检索领域也存在选择众多的存","timestamp":"2025-03-16T10:34:02.847Z"},{"type":"success","content":"#I/思考 \\n\\n如果你想要做笔记，最好把三个要素都记下来\\n1","timestamp":"2025-03-16T10:34:03.465Z"},{"type":"success","content":"#P/AI/安全\\n\\nhttps://gandalf.lake","timestamp":"2025-03-16T10:34:04.209Z"}],"lastUpdate":"2025-03-16T10:34:04.213Z"}
\.


--
-- Data for Name: tag; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.tag (id, name, icon, parent, "createdAt", "updatedAt", "accountId", "sortOrder") FROM stdin;
1	Welcome	🎉	0	2025-02-27 23:52:53.341+08	2025-02-27 23:52:53.341+08	1	0
2	Attachment	🔖	1	2025-02-27 23:52:53.341+08	2025-02-27 23:52:53.341+08	1	0
3	Code	🪄	1	2025-02-27 23:52:53.341+08	2025-02-27 23:52:53.341+08	1	0
4	To-Do	✨	1	2025-02-27 23:52:53.341+08	2025-02-27 23:52:53.341+08	1	0
5	Multi-Level-Tags	🏷️	1	2025-02-27 23:52:53.341+08	2025-02-27 23:52:53.341+08	1	0
7	p		0	2025-02-27 23:56:07.527+08	2025-02-27 23:56:07.527+08	1	0
8	项目		7	2025-02-27 23:56:07.537+08	2025-02-27 23:56:07.537+08	1	0
9	i		0	2025-03-09 00:04:35.552+08	2025-03-09 00:04:35.552+08	1	0
10	终于好了		9	2025-03-09 00:04:35.561+08	2025-03-09 00:04:35.561+08	1	0
12	阅读		7	2025-03-09 17:12:55.592+08	2025-03-09 17:12:55.592+08	1	0
13	ai时代思考法		12	2025-03-09 17:12:55.608+08	2025-03-09 17:12:55.608+08	1	0
14	P		0	2025-03-09 17:17:14.428+08	2025-03-09 17:17:14.428+08	1	0
15	阅读		14	2025-03-09 17:17:14.438+08	2025-03-09 17:17:14.438+08	1	0
16	ai时代思考法		15	2025-03-09 17:17:14.445+08	2025-03-09 17:17:14.445+08	1	0
17	A		0	2025-03-10 10:19:43.673+08	2025-03-10 10:19:43.673+08	1	0
18	摄影		17	2025-03-10 10:19:43.679+08	2025-03-10 10:19:43.679+08	1	0
19	我看见了风暴		12	2025-03-10 13:45:55.886+08	2025-03-10 13:45:55.886+08	1	0
20	知识管理		0	2025-03-10 23:07:27.867+08	2025-03-10 23:07:27.867+08	1	0
21	AI		14	2025-03-10 23:08:15.16+08	2025-03-10 23:08:15.16+08	1	0
22	安全		21	2025-03-10 23:08:15.167+08	2025-03-10 23:08:15.167+08	1	0
23	稍后读		0	2025-03-11 17:21:49.038+08	2025-03-11 17:21:49.038+08	1	0
24	api		0	2025-03-11 17:37:18.014+08	2025-03-11 17:37:18.014+08	1	0
25	剪辑		17	2025-03-12 21:32:14.934+08	2025-03-12 21:32:14.934+08	1	0
26	剪辑		0	2025-03-12 21:33:15.502+08	2025-03-12 21:33:15.502+08	1	0
27	I		0	2025-03-12 21:35:50.835+08	2025-03-12 21:35:50.835+08	1	0
28	思考		27	2025-03-12 21:35:50.845+08	2025-03-12 21:35:50.845+08	1	0
29	待办		0	2025-03-13 10:28:46.008+08	2025-03-13 10:28:46.008+08	1	0
30	大模型		17	2025-03-13 19:47:00.871+08	2025-03-13 19:47:00.871+08	1	0
31	数据漂移容忍度		30	2025-03-13 19:47:00.882+08	2025-03-13 19:47:00.882+08	1	0
32	R		0	2025-03-13 19:47:30.125+08	2025-03-13 19:47:30.125+08	1	0
33	无损音乐下载		32	2025-03-13 19:47:30.138+08	2025-03-13 19:47:30.138+08	1	0
34	电子书下载		32	2025-03-13 21:45:13.462+08	2025-03-13 21:45:13.462+08	1	0
35	TTS模型汇总		32	2025-03-13 21:45:13.479+08	2025-03-13 21:45:13.479+08	1	0
36	闪念		0	2025-03-13 23:14:32.084+08	2025-03-13 23:14:32.084+08	1	0
37	知识库		30	2025-03-14 20:37:24.393+08	2025-03-14 20:37:24.393+08	1	0
38	嵌入模型		37	2025-03-14 20:37:24.4+08	2025-03-14 20:37:24.4+08	1	0
39	读书		32	2025-03-14 21:24:33.286+08	2025-03-14 21:24:33.286+08	1	0
40	翻译		0	2025-03-16 18:19:08.426+08	2025-03-16 18:19:08.426+08	1	0
41	pdfz		40	2025-03-16 18:19:08.434+08	2025-03-16 18:19:08.434+08	1	0
42	经验		0	2025-03-17 09:16:01.861+08	2025-03-17 09:16:01.861+08	1	0
43	小火箭		0	2025-03-18 21:03:01.434+08	2025-03-18 21:03:01.434+08	1	0
\.


--
-- Data for Name: tagsToNote; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."tagsToNote" (id, "noteId", "tagId") FROM stdin;
1	1	1
2	2	1
3	2	2
4	3	1
5	3	3
6	4	1
7	4	4
8	5	1
9	5	5
11	8	7
12	8	8
13	9	9
14	9	10
18	12	7
19	12	12
20	12	13
24	10	7
25	10	12
26	10	13
30	13	14
31	13	15
32	13	16
33	14	17
34	14	18
35	15	7
36	15	12
37	15	19
38	16	20
39	17	14
40	17	21
41	17	22
42	18	23
43	19	24
46	20	17
47	20	25
48	21	26
49	22	27
50	22	28
51	23	29
52	24	17
53	24	30
54	24	31
55	25	32
56	25	33
57	25	34
58	25	35
61	26	36
62	27	29
64	28	17
65	28	30
66	28	37
67	28	38
72	29	17
73	29	30
74	29	37
75	30	32
76	30	39
77	31	17
78	31	30
79	31	37
80	32	40
81	32	41
82	33	42
83	34	43
\.


--
-- Name: accounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.accounts_id_seq', 1, true);


--
-- Name: attachments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.attachments_id_seq', 15, true);


--
-- Name: cache_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.cache_id_seq', 1, false);


--
-- Name: comments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.comments_id_seq', 1, false);


--
-- Name: config_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.config_id_seq', 23, true);


--
-- Name: conversation_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.conversation_id_seq', 3, true);


--
-- Name: follows_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.follows_id_seq', 1, false);


--
-- Name: message_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.message_id_seq', 16, true);


--
-- Name: noteHistory_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."noteHistory_id_seq"', 11, true);


--
-- Name: noteReference_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."noteReference_id_seq"', 1, false);


--
-- Name: notes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.notes_id_seq', 34, true);


--
-- Name: notifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.notifications_id_seq', 21, true);


--
-- Name: plugin_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.plugin_id_seq', 3, true);


--
-- Name: tag_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.tag_id_seq', 43, true);


--
-- Name: tagsToNote_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."tagsToNote_id_seq"', 83, true);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: accounts accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (id);


--
-- Name: attachments attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments
    ADD CONSTRAINT attachments_pkey PRIMARY KEY (id);


--
-- Name: cache cache_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cache
    ADD CONSTRAINT cache_pkey PRIMARY KEY (id);


--
-- Name: comments comments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_pkey PRIMARY KEY (id);


--
-- Name: config config_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.config
    ADD CONSTRAINT config_pkey PRIMARY KEY (id);


--
-- Name: conversation conversation_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversation
    ADD CONSTRAINT conversation_pkey PRIMARY KEY (id);


--
-- Name: follows follows_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT follows_pkey PRIMARY KEY (id);


--
-- Name: message message_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message
    ADD CONSTRAINT message_pkey PRIMARY KEY (id);


--
-- Name: noteHistory noteHistory_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteHistory"
    ADD CONSTRAINT "noteHistory_pkey" PRIMARY KEY (id);


--
-- Name: noteReference noteReference_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference"
    ADD CONSTRAINT "noteReference_pkey" PRIMARY KEY (id);


--
-- Name: notes notes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notes
    ADD CONSTRAINT notes_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: plugin plugin_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.plugin
    ADD CONSTRAINT plugin_pkey PRIMARY KEY (id);


--
-- Name: scheduledTask scheduledTask_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."scheduledTask"
    ADD CONSTRAINT "scheduledTask_pkey" PRIMARY KEY (name);


--
-- Name: tag tag_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag
    ADD CONSTRAINT tag_pkey PRIMARY KEY (id);


--
-- Name: tagsToNote tagsToNote_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote"
    ADD CONSTRAINT "tagsToNote_pkey" PRIMARY KEY ("noteId", "tagId");


--
-- Name: cache_key_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cache_key_key ON public.cache USING btree (key);


--
-- Name: conversation_accountId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "conversation_accountId_idx" ON public.conversation USING btree ("accountId");


--
-- Name: message_conversationId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "message_conversationId_idx" ON public.message USING btree ("conversationId");


--
-- Name: noteHistory_accountId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "noteHistory_accountId_idx" ON public."noteHistory" USING btree ("accountId");


--
-- Name: noteHistory_noteId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "noteHistory_noteId_idx" ON public."noteHistory" USING btree ("noteId");


--
-- Name: noteReference_fromNoteId_toNoteId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "noteReference_fromNoteId_toNoteId_key" ON public."noteReference" USING btree ("fromNoteId", "toNoteId");


--
-- Name: attachments attachments_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments
    ADD CONSTRAINT "attachments_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: attachments attachments_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments
    ADD CONSTRAINT "attachments_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: comments comments_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT "comments_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: comments comments_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT "comments_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: comments comments_parentId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT "comments_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES public.comments(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: config config_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.config
    ADD CONSTRAINT "config_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: conversation conversation_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversation
    ADD CONSTRAINT "conversation_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: follows follows_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT "follows_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: message message_conversationId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message
    ADD CONSTRAINT "message_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES public.conversation(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: noteHistory noteHistory_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteHistory"
    ADD CONSTRAINT "noteHistory_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: noteReference noteReference_fromNoteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference"
    ADD CONSTRAINT "noteReference_fromNoteId_fkey" FOREIGN KEY ("fromNoteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: noteReference noteReference_toNoteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference"
    ADD CONSTRAINT "noteReference_toNoteId_fkey" FOREIGN KEY ("toNoteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: notes notes_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notes
    ADD CONSTRAINT "notes_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: notifications notifications_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "notifications_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: tag tag_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag
    ADD CONSTRAINT "tag_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: tagsToNote tagsToNote_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote"
    ADD CONSTRAINT "tagsToNote_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: tagsToNote tagsToNote_tagId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote"
    ADD CONSTRAINT "tagsToNote_tagId_fkey" FOREIGN KEY ("tagId") REFERENCES public.tag(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- PostgreSQL database dump complete
--

--
-- Database "postgres" dump
--

\connect postgres

--
-- PostgreSQL database dump
--

-- Dumped from database version 14.17 (Debian 14.17-1.pgdg120+1)
-- Dumped by pg_dump version 14.17 (Debian 14.17-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO postgres;

--
-- Name: accounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accounts (
    id integer NOT NULL,
    name character varying DEFAULT ''::character varying NOT NULL,
    nickname character varying DEFAULT ''::character varying NOT NULL,
    password character varying DEFAULT ''::character varying NOT NULL,
    image character varying DEFAULT ''::character varying NOT NULL,
    "apiToken" character varying DEFAULT ''::character varying NOT NULL,
    note integer DEFAULT 0 NOT NULL,
    role character varying DEFAULT ''::character varying NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "loginType" character varying DEFAULT ''::character varying NOT NULL,
    "linkAccountId" integer,
    description text DEFAULT ''::text NOT NULL
);


ALTER TABLE public.accounts OWNER TO postgres;

--
-- Name: accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accounts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.accounts_id_seq OWNER TO postgres;

--
-- Name: accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accounts_id_seq OWNED BY public.accounts.id;


--
-- Name: attachments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.attachments (
    id integer NOT NULL,
    "isShare" boolean DEFAULT false NOT NULL,
    "sharePassword" character varying DEFAULT ''::character varying NOT NULL,
    name character varying DEFAULT ''::character varying NOT NULL,
    path character varying DEFAULT ''::character varying NOT NULL,
    size numeric DEFAULT 0 NOT NULL,
    "noteId" integer,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    type character varying DEFAULT ''::character varying NOT NULL,
    "sortOrder" integer DEFAULT 0 NOT NULL,
    depth integer,
    "perfixPath" character varying DEFAULT ''::character varying,
    "accountId" integer
);


ALTER TABLE public.attachments OWNER TO postgres;

--
-- Name: attachments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.attachments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.attachments_id_seq OWNER TO postgres;

--
-- Name: attachments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.attachments_id_seq OWNED BY public.attachments.id;


--
-- Name: cache; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cache (
    id integer NOT NULL,
    key character varying NOT NULL,
    value json NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.cache OWNER TO postgres;

--
-- Name: cache_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cache_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.cache_id_seq OWNER TO postgres;

--
-- Name: cache_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cache_id_seq OWNED BY public.cache.id;


--
-- Name: comments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.comments (
    id integer NOT NULL,
    content text NOT NULL,
    "accountId" integer,
    "guestName" character varying,
    "guestIP" character varying,
    "guestUA" character varying,
    "noteId" integer NOT NULL,
    "parentId" integer,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.comments OWNER TO postgres;

--
-- Name: comments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.comments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.comments_id_seq OWNER TO postgres;

--
-- Name: comments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.comments_id_seq OWNED BY public.comments.id;


--
-- Name: config; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.config (
    id integer NOT NULL,
    key character varying DEFAULT ''::character varying NOT NULL,
    config json,
    "userId" integer
);


ALTER TABLE public.config OWNER TO postgres;

--
-- Name: config_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.config_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.config_id_seq OWNER TO postgres;

--
-- Name: config_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.config_id_seq OWNED BY public.config.id;


--
-- Name: conversation; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.conversation (
    id integer NOT NULL,
    title character varying(255) DEFAULT ''::character varying NOT NULL,
    "accountId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.conversation OWNER TO postgres;

--
-- Name: conversation_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.conversation_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.conversation_id_seq OWNER TO postgres;

--
-- Name: conversation_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.conversation_id_seq OWNED BY public.conversation.id;


--
-- Name: follows; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.follows (
    id integer NOT NULL,
    "siteName" character varying,
    "siteUrl" character varying NOT NULL,
    "siteAvatar" character varying,
    description text,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "followType" character varying DEFAULT 'following'::character varying NOT NULL,
    "accountId" integer NOT NULL
);


ALTER TABLE public.follows OWNER TO postgres;

--
-- Name: follows_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.follows_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.follows_id_seq OWNER TO postgres;

--
-- Name: follows_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.follows_id_seq OWNED BY public.follows.id;


--
-- Name: message; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.message (
    id integer NOT NULL,
    content text NOT NULL,
    role character varying(50) NOT NULL,
    "conversationId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    metadata json
);


ALTER TABLE public.message OWNER TO postgres;

--
-- Name: message_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.message_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.message_id_seq OWNER TO postgres;

--
-- Name: message_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.message_id_seq OWNED BY public.message.id;


--
-- Name: noteReference; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."noteReference" (
    id integer NOT NULL,
    "fromNoteId" integer NOT NULL,
    "toNoteId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."noteReference" OWNER TO postgres;

--
-- Name: noteReference_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."noteReference_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."noteReference_id_seq" OWNER TO postgres;

--
-- Name: noteReference_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."noteReference_id_seq" OWNED BY public."noteReference".id;


--
-- Name: notes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notes (
    id integer NOT NULL,
    type integer DEFAULT 0 NOT NULL,
    content character varying DEFAULT ''::character varying NOT NULL,
    "isArchived" boolean DEFAULT false NOT NULL,
    "isRecycle" boolean DEFAULT false NOT NULL,
    "isShare" boolean DEFAULT false NOT NULL,
    "isTop" boolean DEFAULT false NOT NULL,
    "sharePassword" character varying DEFAULT ''::character varying NOT NULL,
    metadata json,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "isReviewed" boolean DEFAULT false NOT NULL,
    "accountId" integer,
    "shareEncryptedUrl" character varying,
    "shareExpiryDate" timestamp(6) with time zone,
    "shareMaxView" integer DEFAULT 0,
    "shareViewCount" integer DEFAULT 0
);


ALTER TABLE public.notes OWNER TO postgres;

--
-- Name: notes_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.notes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.notes_id_seq OWNER TO postgres;

--
-- Name: notes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.notes_id_seq OWNED BY public.notes.id;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notifications (
    id integer NOT NULL,
    type character varying NOT NULL,
    title character varying NOT NULL,
    content text NOT NULL,
    metadata json,
    "isRead" boolean DEFAULT false NOT NULL,
    "accountId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.notifications OWNER TO postgres;

--
-- Name: notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.notifications_id_seq OWNER TO postgres;

--
-- Name: notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.notifications_id_seq OWNED BY public.notifications.id;


--
-- Name: plugin; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.plugin (
    id integer NOT NULL,
    metadata json NOT NULL,
    path character varying NOT NULL,
    "isUse" boolean DEFAULT true NOT NULL,
    "isDev" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.plugin OWNER TO postgres;

--
-- Name: plugin_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.plugin_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.plugin_id_seq OWNER TO postgres;

--
-- Name: plugin_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.plugin_id_seq OWNED BY public.plugin.id;


--
-- Name: scheduledTask; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."scheduledTask" (
    name text NOT NULL,
    schedule text NOT NULL,
    "lastRun" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "isSuccess" boolean DEFAULT true NOT NULL,
    "isRunning" boolean DEFAULT false NOT NULL,
    output json
);


ALTER TABLE public."scheduledTask" OWNER TO postgres;

--
-- Name: tag; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tag (
    id integer NOT NULL,
    name character varying DEFAULT ''::character varying NOT NULL,
    icon character varying DEFAULT ''::character varying NOT NULL,
    parent integer DEFAULT 0 NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "accountId" integer,
    "sortOrder" integer DEFAULT 0 NOT NULL
);


ALTER TABLE public.tag OWNER TO postgres;

--
-- Name: tag_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tag_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.tag_id_seq OWNER TO postgres;

--
-- Name: tag_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.tag_id_seq OWNED BY public.tag.id;


--
-- Name: tagsToNote; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."tagsToNote" (
    id integer NOT NULL,
    "noteId" integer DEFAULT 0 NOT NULL,
    "tagId" integer DEFAULT 0 NOT NULL
);


ALTER TABLE public."tagsToNote" OWNER TO postgres;

--
-- Name: tagsToNote_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."tagsToNote_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."tagsToNote_id_seq" OWNER TO postgres;

--
-- Name: tagsToNote_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."tagsToNote_id_seq" OWNED BY public."tagsToNote".id;


--
-- Name: accounts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts ALTER COLUMN id SET DEFAULT nextval('public.accounts_id_seq'::regclass);


--
-- Name: attachments id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments ALTER COLUMN id SET DEFAULT nextval('public.attachments_id_seq'::regclass);


--
-- Name: cache id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cache ALTER COLUMN id SET DEFAULT nextval('public.cache_id_seq'::regclass);


--
-- Name: comments id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments ALTER COLUMN id SET DEFAULT nextval('public.comments_id_seq'::regclass);


--
-- Name: config id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.config ALTER COLUMN id SET DEFAULT nextval('public.config_id_seq'::regclass);


--
-- Name: conversation id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversation ALTER COLUMN id SET DEFAULT nextval('public.conversation_id_seq'::regclass);


--
-- Name: follows id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows ALTER COLUMN id SET DEFAULT nextval('public.follows_id_seq'::regclass);


--
-- Name: message id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message ALTER COLUMN id SET DEFAULT nextval('public.message_id_seq'::regclass);


--
-- Name: noteReference id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference" ALTER COLUMN id SET DEFAULT nextval('public."noteReference_id_seq"'::regclass);


--
-- Name: notes id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notes ALTER COLUMN id SET DEFAULT nextval('public.notes_id_seq'::regclass);


--
-- Name: notifications id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications ALTER COLUMN id SET DEFAULT nextval('public.notifications_id_seq'::regclass);


--
-- Name: plugin id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.plugin ALTER COLUMN id SET DEFAULT nextval('public.plugin_id_seq'::regclass);


--
-- Name: tag id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag ALTER COLUMN id SET DEFAULT nextval('public.tag_id_seq'::regclass);


--
-- Name: tagsToNote id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote" ALTER COLUMN id SET DEFAULT nextval('public."tagsToNote_id_seq"'::regclass);


--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
b46ce3a6-9800-4b76-b7ed-378e951042bb	9fa97d3b49d43eb600745ee4c4845358be7d3e1139f98bed17df6c8e8afe8e46	2025-02-27 23:31:11.800519+08	20241026033301_0_0_1	\N	\N	2025-02-27 23:31:11.727358+08	1
93686754-967c-48df-8e44-33ef900735fd	2ce1697d65421c8f9e35edd5c2c53763bdebe368434bfc6b24e30fdb42f2dff4	2025-02-27 23:31:11.943045+08	20250110022717_0_34_15	\N	\N	2025-02-27 23:31:11.935775+08	1
1622c434-2cdd-452a-bcab-6fd80ae8bfd2	bca4800423e0a93798bd3bd4495222c5f1a290ee6a013be2b2250eae49fd9b5f	2025-02-27 23:31:11.809355+08	20241027072348_0_0_3	\N	\N	2025-02-27 23:31:11.803237+08	1
4497093b-c2d0-4322-ac46-5864340b2bd1	f66411c9c48f9b8d624d3d0a84183ea59fa005f34143a2348cbec15213d2c829	2025-02-27 23:31:11.818425+08	20241112084821_0_2_9	\N	\N	2025-02-27 23:31:11.811808+08	1
d284dd9e-fce2-49ce-9bc6-d998a6c75863	570c238793a05d26ea62ac05fb2c4ecf55e704e5a648b2d346e92ce727cd9dc2	2025-02-27 23:31:11.82783+08	20241121082228_0_6_0	\N	\N	2025-02-27 23:31:11.82196+08	1
0264d961-f599-4f90-9c2e-fb4f916991a6	7a72662f97fe7d3ff25f48ba8b0b4d775a7786fb382f9ffa74d1568d11041996	2025-02-27 23:31:11.960911+08	20250110105226_0_36_0	\N	\N	2025-02-27 23:31:11.944946+08	1
01d08f66-4a63-4739-9265-3a656f3048f2	08cb30f3ae17306833068ee37027f90243ec9fe0ad1ccf317097d408d8f08778	2025-02-27 23:31:11.835802+08	20241202103221_0_12_11	\N	\N	2025-02-27 23:31:11.82985+08	1
5d7a7dd4-e908-4502-8a1f-f2ae6b19a531	d7ba53a4d86751d7d3b05477676eace96e0907eee7ca35e5d77d32559631c323	2025-02-27 23:31:11.852223+08	20241205035247_18_0_1	\N	\N	2025-02-27 23:31:11.837755+08	1
28d1100e-68ad-4a91-8137-592b617699be	cb212e993eeb326e7092e5cea14f069bde6601c29863602b3f998b3847afbcb7	2025-02-27 23:31:11.861016+08	20241212033352_0_23_3	\N	\N	2025-02-27 23:31:11.854958+08	1
c6ef5b0c-88c3-4dc8-91d4-35c85f592202	bb7b9ee3fe8d49311ead5709ef71579ca099b4f0e05011a07229fc49e4273cd5	2025-02-27 23:31:11.982831+08	20250115050059_0_37_0	\N	\N	2025-02-27 23:31:11.963633+08	1
72643890-4893-437c-ae3a-ea86c99e7143	60e2f4991f43b9abed2c0982c0cda50be307e7f6673d9aaf099a6f4b6edb90d6	2025-02-27 23:31:11.869354+08	20241218023101_0_27_0	\N	\N	2025-02-27 23:31:11.863365+08	1
050d30fd-8893-486a-9c9b-2e9fc577932c	5f13a60e92bfbe447840075a94ac3e2f37159c611c08f75e78c79e9b3ac1fcff	2025-02-27 23:31:11.876685+08	20241219062514_0_27_7	\N	\N	2025-02-27 23:31:11.871131+08	1
834ffe0c-018e-455c-be48-c42169d8390b	471626763957618b7b81e0a266dc4179c63a8d62b254a88c7499734bd163ed04	2025-02-27 23:31:11.885907+08	20241226141834_0_30_7	\N	\N	2025-02-27 23:31:11.878537+08	1
030d7b96-35ef-4ac3-a853-6e6a588506d2	73c54b7c727c21868a10b47577b678fbd15b2e720410773ccf0087a67c371e0d	2025-02-27 23:31:12.00228+08	20250116052731_0_37_4	\N	\N	2025-02-27 23:31:11.984727+08	1
2d0891da-4134-46a5-89e8-bb0ca9a954bd	a626e584989cc0e1712e9a1b16e4cbbae3a2c0ab2214d137bb748fd115687821	2025-02-27 23:31:11.89455+08	20241231014246_0_32_0	\N	\N	2025-02-27 23:31:11.888658+08	1
feb61e73-def0-49ca-b26c-438d52d1d3d4	89a28b674edab6177c0b56968bb4920ef98b144ec4e8bb490290e3cd8eb081e1	2025-02-27 23:31:11.902694+08	20250101032000_0_32_4	\N	\N	2025-02-27 23:31:11.897162+08	1
dec5e363-f0ad-4266-a256-919ea67e06b1	b577841cbcd0b79b95b59569c546caced160b42ae811c42cbdc7d952ffe736c9	2025-02-27 23:31:11.923314+08	20250108081202_0_34_0	\N	\N	2025-02-27 23:31:11.904603+08	1
53244c5e-8526-45f1-ae4d-77ffa33ab162	5ff61439418b8e46dbf00dcddd25ff1fe09adbf8c191b4285fbe9f65b67e4a07	2025-02-27 23:31:12.019893+08	20250219083523_0_39_0	\N	\N	2025-02-27 23:31:12.004065+08	1
bb8e9b9b-fae2-475f-8a56-ecea286eca95	df97e584d79952e8a5a5463ec9f46bd3aaae1776d38aacbb5320adb7576e518f	2025-02-27 23:31:11.93327+08	20250110011520_0_34_13	\N	\N	2025-02-27 23:31:11.925354+08	1
63ba26f8-0121-4a5d-b587-87653320e8b8	ba9d281c54e8e733318de494b4b8e37d4977f9611dea63bc38b96751d10b9973	2025-02-27 23:31:12.049134+08	20250222125610_0_41_0	\N	\N	2025-02-27 23:31:12.022231+08	1
\.


--
-- Data for Name: accounts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.accounts (id, name, nickname, password, image, "apiToken", note, role, "createdAt", "updatedAt", "loginType", "linkAccountId", description) FROM stdin;
1	Cotton	Cotton	pbkdf2:531683872cc50d0c077610277b383b13:2a3597f0423fc9c4740f513c7043329d5d62c03cc9521376e2b18a0fc2733880175eebcd03dfd5f21415881e14cd3091aaf4117dcbd7d1dfc48e0dc54b943b6e		eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..A3SABkHSW5nHBN7J.9Xj93PzoiEie8V9PwM7dnj-5fMFy7M8SwJf-tT25VxUJjtA_KXo-Nh2et8Eblt8WJWROsy0F4y_Q6spIP99tXn9dempStMPDlNXynezCDQAFubhLhQ9owqeLiz3gMI_gUpfQmIoWlPKw7jLYJH_Tr153rDT0dHwEwILOOtFN.qpS-8mlotMNaAQxI9lxPRg	0	superadmin	2025-02-27 23:52:53.307+08	2025-02-27 23:52:53.32+08		\N	
\.


--
-- Data for Name: attachments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.attachments (id, "isShare", "sharePassword", name, path, size, "noteId", "createdAt", "updatedAt", type, "sortOrder", depth, "perfixPath", "accountId") FROM stdin;
1	f		pic01.png	/api/file/pic01.png	1360952	2	2025-02-27 23:52:53.348+08	2025-03-08 22:22:00.394+08		0	0		\N
2	f		pic02.png	/api/file/pic02.png	971782	2	2025-02-27 23:52:53.348+08	2025-03-08 22:22:00.402+08		0	0		\N
3	f		pic03.png	/api/file/pic03.png	141428	2	2025-02-27 23:52:53.348+08	2025-03-08 22:22:00.405+08		0	0		\N
4	f		pic04.png	/api/file/pic04.png	589371	2	2025-02-27 23:52:53.348+08	2025-03-08 22:22:00.408+08		0	0		\N
5	f		pic06.png	/api/file/pic06.png	875361	2	2025-02-27 23:52:53.348+08	2025-03-08 22:22:00.411+08		0	0		\N
6	f		story.txt	/api/file/story.txt	0	2	2025-02-27 23:52:53.348+08	2025-03-08 22:22:00.414+08		0	0		\N
\.


--
-- Data for Name: cache; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.cache (id, key, value, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: comments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.comments (id, content, "accountId", "guestName", "guestIP", "guestUA", "noteId", "parentId", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: config; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.config (id, key, config, "userId") FROM stdin;
1	NEXTAUTH_SECRET	{"value":"CSVoNVGcIBxOUXLoyX/JsCKWAr6ShsTNJUEVByswtMc="}	\N
2	theme	{"value":"system"}	1
3	isUseAI	{"type":"boolean","value":true}	\N
7	aiApiKey	{"type":"string","value":"sk-FN0OmEVkXsA442zJ2U05uZ6CEsu5ckpTh6ryBiYY"}	\N
9	spotifyConsumerKey	{"type":"string","value":"Cotton"}	\N
10	spotifyConsumerSecret	{"type":"string","value":"TCW147896"}	\N
5	aiModel	{"type":"string","value":"gpt-4o-mini"}	\N
4	embeddingModel	{"type":"string","value":"text-embedding-ada-002"}	\N
11	tavilyApiKey	{"type":"string","value":"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"}	\N
12	tavilyMaxResult	{"type":"number","value":4}	\N
13	language	{"type":"string","value":"zh"}	1
6	aiModelProvider	{"type":"string","value":"OpenAI"}	\N
8	aiApiEndpoint	{"type":"string","value":"https://free.yunwu.ai/v1"}	\N
14	localCustomPath	{"type":"string","value":""}	\N
15	objectStorage	{"type":"string","value":"s3"}	\N
\.


--
-- Data for Name: conversation; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.conversation (id, title, "accountId", "createdAt", "updatedAt") FROM stdin;
1		1	2025-03-01 18:19:01.212+08	2025-03-01 18:19:01.212+08
2		1	2025-03-01 18:23:09.277+08	2025-03-01 18:23:09.277+08
3		1	2025-03-01 18:38:02.276+08	2025-03-01 18:38:02.276+08
4		1	2025-03-08 11:03:07.056+08	2025-03-08 11:03:07.056+08
\.


--
-- Data for Name: follows; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.follows (id, "siteName", "siteUrl", "siteAvatar", description, "createdAt", "updatedAt", "followType", "accountId") FROM stdin;
\.


--
-- Data for Name: message; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.message (id, content, role, "conversationId", "createdAt", "updatedAt", metadata) FROM stdin;
1	你好	user	1	2025-03-01 18:19:01.624+08	2025-03-01 18:19:01.624+08	\N
2	你好	user	1	2025-03-01 18:21:25.442+08	2025-03-01 18:21:25.442+08	\N
3	你能帮我做什么	user	2	2025-03-01 18:23:09.678+08	2025-03-01 18:23:09.678+08	\N
4	你好	user	2	2025-03-01 18:32:58.664+08	2025-03-01 18:32:58.664+08	\N
5	你好呀	user	3	2025-03-01 18:38:02.478+08	2025-03-01 18:38:02.478+08	\N
6	你好	user	4	2025-03-08 11:03:07.191+08	2025-03-08 11:03:07.191+08	\N
7	你好	user	4	2025-03-08 11:05:47.394+08	2025-03-08 11:05:47.394+08	\N
\.


--
-- Data for Name: noteReference; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."noteReference" (id, "fromNoteId", "toNoteId", "createdAt") FROM stdin;
\.


--
-- Data for Name: notes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notes (id, type, content, "isArchived", "isRecycle", "isShare", "isTop", "sharePassword", metadata, "createdAt", "updatedAt", "isReviewed", "accountId", "shareEncryptedUrl", "shareExpiryDate", "shareMaxView", "shareViewCount") FROM stdin;
1	0	#Welcome\n\nWelcome to Blinko!\n\nWhether you're capturing ideas, taking meeting notes, or planning your schedule, Blinko provides an easy and efficient way to manage it all. Here, you can create, edit, and share notes anytime, anywhere, ensuring you never lose a valuable thought.	f	f	f	f		\N	2025-02-27 23:52:53.337+08	2025-02-27 23:52:53.337+08	f	1	\N	\N	0	0
2	0	#Welcome/Attachment	f	f	f	f		\N	2025-02-27 23:52:53.337+08	2025-02-27 23:52:53.337+08	f	1	\N	\N	0	0
3	0	#Welcome/Code\n\n\n\n```js\nfunction Welcome(){\n  console.log("Hello! Blinko");\n}\n```	f	f	f	f		\N	2025-02-27 23:52:53.337+08	2025-02-27 23:52:53.337+08	f	1	\N	\N	0	0
4	0	#Welcome/To-Do\n\n* Create a blinko\n* Create a note\n* Upload file	f	f	f	f		\N	2025-02-27 23:52:53.337+08	2025-02-27 23:52:53.337+08	f	1	\N	\N	0	0
5	0	#Welcome/Multi-Level-Tags\n\nUse the "/" shortcut to effortlessly create and organize multi-level tags.	f	f	f	f		\N	2025-02-27 23:52:53.337+08	2025-02-27 23:52:53.337+08	f	1	\N	\N	0	0
6	0	https://github.com/blinko-space/blinko/	f	f	f	f		\N	2025-02-27 23:52:53.337+08	2025-02-27 23:52:53.337+08	f	1	\N	\N	0	0
8	0	#p/项目 少女\n	f	f	f	f		\N	2025-02-27 23:56:07.517+08	2025-02-27 23:56:07.517+08	f	1	\N	\N	0	0
9	0	#硅基 sk-jlrbbqfihhxdxfzftkviokwvbyjmkayejednthwxqjrrrtwq\n	f	f	f	f		\N	2025-03-08 10:23:11.817+08	2025-03-08 10:23:11.817+08	f	1	\N	\N	0	0
\.


--
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notifications (id, type, title, content, metadata, "isRead", "accountId", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: plugin; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.plugin (id, metadata, path, "isUse", "isDev", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: scheduledTask; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."scheduledTask" (name, schedule, "lastRun", "isSuccess", "isRunning", output) FROM stdin;
\.


--
-- Data for Name: tag; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.tag (id, name, icon, parent, "createdAt", "updatedAt", "accountId", "sortOrder") FROM stdin;
1	Welcome	🎉	0	2025-02-27 23:52:53.341+08	2025-02-27 23:52:53.341+08	1	0
2	Attachment	🔖	1	2025-02-27 23:52:53.341+08	2025-02-27 23:52:53.341+08	1	0
3	Code	🪄	1	2025-02-27 23:52:53.341+08	2025-02-27 23:52:53.341+08	1	0
4	To-Do	✨	1	2025-02-27 23:52:53.341+08	2025-02-27 23:52:53.341+08	1	0
5	Multi-Level-Tags	🏷️	1	2025-02-27 23:52:53.341+08	2025-02-27 23:52:53.341+08	1	0
7	p		0	2025-02-27 23:56:07.527+08	2025-02-27 23:56:07.527+08	1	0
8	项目		7	2025-02-27 23:56:07.537+08	2025-02-27 23:56:07.537+08	1	0
9	硅基		0	2025-03-08 10:23:15.386+08	2025-03-08 10:23:15.386+08	1	0
\.


--
-- Data for Name: tagsToNote; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."tagsToNote" (id, "noteId", "tagId") FROM stdin;
1	1	1
2	2	1
3	2	2
4	3	1
5	3	3
6	4	1
7	4	4
8	5	1
9	5	5
11	8	7
12	8	8
\.


--
-- Name: accounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.accounts_id_seq', 1, true);


--
-- Name: attachments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.attachments_id_seq', 7, true);


--
-- Name: cache_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.cache_id_seq', 1, false);


--
-- Name: comments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.comments_id_seq', 1, false);


--
-- Name: config_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.config_id_seq', 15, true);


--
-- Name: conversation_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.conversation_id_seq', 4, true);


--
-- Name: follows_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.follows_id_seq', 1, false);


--
-- Name: message_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.message_id_seq', 7, true);


--
-- Name: noteReference_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."noteReference_id_seq"', 1, false);


--
-- Name: notes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.notes_id_seq', 9, true);


--
-- Name: notifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.notifications_id_seq', 1, false);


--
-- Name: plugin_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.plugin_id_seq', 1, false);


--
-- Name: tag_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.tag_id_seq', 9, true);


--
-- Name: tagsToNote_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."tagsToNote_id_seq"', 12, true);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: accounts accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (id);


--
-- Name: attachments attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments
    ADD CONSTRAINT attachments_pkey PRIMARY KEY (id);


--
-- Name: cache cache_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cache
    ADD CONSTRAINT cache_pkey PRIMARY KEY (id);


--
-- Name: comments comments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_pkey PRIMARY KEY (id);


--
-- Name: config config_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.config
    ADD CONSTRAINT config_pkey PRIMARY KEY (id);


--
-- Name: conversation conversation_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversation
    ADD CONSTRAINT conversation_pkey PRIMARY KEY (id);


--
-- Name: follows follows_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT follows_pkey PRIMARY KEY (id);


--
-- Name: message message_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message
    ADD CONSTRAINT message_pkey PRIMARY KEY (id);


--
-- Name: noteReference noteReference_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference"
    ADD CONSTRAINT "noteReference_pkey" PRIMARY KEY (id);


--
-- Name: notes notes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notes
    ADD CONSTRAINT notes_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: plugin plugin_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.plugin
    ADD CONSTRAINT plugin_pkey PRIMARY KEY (id);


--
-- Name: scheduledTask scheduledTask_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."scheduledTask"
    ADD CONSTRAINT "scheduledTask_pkey" PRIMARY KEY (name);


--
-- Name: tag tag_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag
    ADD CONSTRAINT tag_pkey PRIMARY KEY (id);


--
-- Name: tagsToNote tagsToNote_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote"
    ADD CONSTRAINT "tagsToNote_pkey" PRIMARY KEY ("noteId", "tagId");


--
-- Name: cache_key_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cache_key_key ON public.cache USING btree (key);


--
-- Name: conversation_accountId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "conversation_accountId_idx" ON public.conversation USING btree ("accountId");


--
-- Name: message_conversationId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "message_conversationId_idx" ON public.message USING btree ("conversationId");


--
-- Name: noteReference_fromNoteId_toNoteId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "noteReference_fromNoteId_toNoteId_key" ON public."noteReference" USING btree ("fromNoteId", "toNoteId");


--
-- Name: attachments attachments_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments
    ADD CONSTRAINT "attachments_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: attachments attachments_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments
    ADD CONSTRAINT "attachments_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: comments comments_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT "comments_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: comments comments_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT "comments_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: comments comments_parentId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT "comments_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES public.comments(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: config config_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.config
    ADD CONSTRAINT "config_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: conversation conversation_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversation
    ADD CONSTRAINT "conversation_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: follows follows_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT "follows_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: message message_conversationId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message
    ADD CONSTRAINT "message_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES public.conversation(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: noteReference noteReference_fromNoteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference"
    ADD CONSTRAINT "noteReference_fromNoteId_fkey" FOREIGN KEY ("fromNoteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: noteReference noteReference_toNoteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference"
    ADD CONSTRAINT "noteReference_toNoteId_fkey" FOREIGN KEY ("toNoteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: notes notes_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notes
    ADD CONSTRAINT "notes_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: notifications notifications_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "notifications_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: tag tag_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag
    ADD CONSTRAINT "tag_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: tagsToNote tagsToNote_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote"
    ADD CONSTRAINT "tagsToNote_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: tagsToNote tagsToNote_tagId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote"
    ADD CONSTRAINT "tagsToNote_tagId_fkey" FOREIGN KEY ("tagId") REFERENCES public.tag(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- PostgreSQL database dump complete
--

--
-- PostgreSQL database cluster dump complete
--

SET session_replication_role = 'origin';
