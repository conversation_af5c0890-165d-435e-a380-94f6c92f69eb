#!/bin/bash
# ========================================
# Blinko Docker 简化导入脚本
# 直接执行预设的数据导入
# ========================================

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

POSTGRES_CONTAINER="blinko-postgres"

echo -e "${BLUE}🚀 Blinko Docker 快速导入${NC}"
echo -e "${BLUE}===========================${NC}"

# 检查容器
if ! docker ps | grep -q "$POSTGRES_CONTAINER"; then
    echo -e "${RED}❌ PostgreSQL容器未运行${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker容器检查通过${NC}"

# 确认导入
read -p "是否导入示例标签和笔记到Docker数据库？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}操作已取消${NC}"
    exit 0
fi

echo -e "${YELLOW}📥 开始导入...${NC}"

# 导入标签
echo -e "${YELLOW}1. 导入标签...${NC}"
docker exec $POSTGRES_CONTAINER psql -U postgres -d blinko -c "
INSERT INTO tag (name, icon, parent, \"createdAt\", \"updatedAt\", \"accountId\", \"sortOrder\") VALUES
('Welcome', '🎉', 0, NOW(), NOW(), 1, 0),
('知识管理', '📚', 0, NOW(), NOW(), 1, 0),
('稍后读', '📖', 0, NOW(), NOW(), 1, 0),
('翻译', '🌐', 0, NOW(), NOW(), 1, 0),
('经验', '💡', 0, NOW(), NOW(), 1, 0),
('api', '🔑', 0, NOW(), NOW(), 1, 0),
('闪念', '⚡', 0, NOW(), NOW(), 1, 0),
('项目', '📁', 0, NOW(), NOW(), 1, 0),
('AI', '🤖', 0, NOW(), NOW(), 1, 0),
('资源', '📦', 0, NOW(), NOW(), 1, 0),
('工具', '🔧', 0, NOW(), NOW(), 1, 0),
('学习', '🎓', 0, NOW(), NOW(), 1, 0)
ON CONFLICT (name) DO NOTHING;
"

# 导入笔记
echo -e "${YELLOW}2. 导入笔记...${NC}"
docker exec $POSTGRES_CONTAINER psql -U postgres -d blinko -c "
INSERT INTO notes (type, content, \"isArchived\", \"isRecycle\", \"isShare\", \"isTop\", metadata, \"createdAt\", \"updatedAt\", \"accountId\") VALUES
(0, '#Welcome

🎉 欢迎使用 Blinko！

Blinko 是一个功能强大的笔记和知识管理工具。无论您是在记录想法、做会议记录，还是规划日程，Blinko 都能为您提供简单高效的管理方式。

## 主要功能
- 📝 快速记录想法和笔记
- 🏷️ 灵活的标签系统
- 🔍 强大的搜索功能
- 📎 文件附件支持
- 🌐 多设备同步', 
false, false, false, false, '{\"isIndexed\":true}', NOW(), NOW(), 1),

(0, '#知识管理

## 核心原则
重要的事情做备份、分摊影响（把鸡蛋放在几个篮子里）、多方案思维（尽可能做几个方案，即使一个方案不成，还有额外的方案做弥补）

## 实践方法
1. **信息收集**: 使用标签系统分类整理
2. **定期回顾**: 建立复习机制
3. **知识连接**: 建立笔记间的关联
4. **输出倒逼**: 通过写作加深理解', 
false, false, false, false, '{\"isIndexed\":true}', NOW(), NOW(), 1),

(0, '#经验

## 学习与工作的思考
工作的时候也是需要学习大量的工作相关的知识和技能，以及大学阶段比较有价值的是打基础。

## 技能获得的渠道
我有很多技能并不是上课学来的，都是在实习项目中学到的，包括很多软实力软技能：
- 沟通协调能力
- 项目管理能力  
- 解决问题的思维
- 团队协作经验', 
false, false, false, false, '{\"isIndexed\":true}', NOW(), NOW(), 1),

(0, '#AI

## 现代开发技术栈
ts + nextjs (t3 stack) + react 就是大模型最擅长的框架（有必要了解一下这几个玩意）

## AI 工具应用
- ChatGPT/Claude: 编程助手和问题解答
- GitHub Copilot: 代码自动完成
- Cursor: AI 增强的代码编辑器
- Midjourney: 图像生成', 
false, false, false, false, '{\"isIndexed\":true}', NOW(), NOW(), 1),

(0, '#工具

## 开发工具推荐
- **编辑器**: VS Code, Cursor
- **版本控制**: Git, GitHub
- **容器化**: Docker, Docker Compose
- **数据库**: PostgreSQL, Redis
- **部署**: Vercel, Railway, Cloudflare

## 效率工具
- **笔记**: Blinko, Notion, Obsidian
- **任务管理**: Todoist, Things
- **时间管理**: Toggl, RescueTime', 
false, false, false, false, '{\"isIndexed\":true}', NOW(), NOW(), 1);
"

# 重置序列
echo -e "${YELLOW}3. 重置序列...${NC}"
docker exec $POSTGRES_CONTAINER psql -U postgres -d blinko -c "
SELECT setval('tag_id_seq', (SELECT MAX(id) FROM tag));
SELECT setval('notes_id_seq', (SELECT MAX(id) FROM notes));
"

# 验证结果
echo -e "${YELLOW}4. 验证结果...${NC}"
STATS=$(docker exec $POSTGRES_CONTAINER psql -U postgres -d blinko -t -c "
SELECT 'Tags: ' || COUNT(*) FROM tag
UNION ALL
SELECT 'Notes: ' || COUNT(*) FROM notes
UNION ALL  
SELECT 'Active: ' || COUNT(*) FROM notes WHERE \"isRecycle\" = false;
")

echo -e "${GREEN}📊 导入统计:${NC}"
echo "$STATS" | while read line; do
    echo -e "${GREEN}   $line${NC}"
done

# 重启Blinko应用
echo -e "${YELLOW}5. 重启应用...${NC}"
docker restart blinko-website > /dev/null 2>&1

echo -e "\n${GREEN}🎉 导入完成！${NC}"
echo -e "${YELLOW}💡 请访问 https://ccnu.me 查看结果${NC}"
echo -e "${BLUE}===========================${NC}"
