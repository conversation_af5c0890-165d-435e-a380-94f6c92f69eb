#!/bin/bash
# ========================================
# Blinko 数据提取工具
# 用于从备份文件中提取特定数据段
# ========================================

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

BACKUP_FILE="backup_20250318.sql"
OUTPUT_DIR="extracted_data"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE} Blinko 数据提取工具${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查备份文件
if [ ! -f "$BACKUP_FILE" ]; then
    echo -e "${RED}❌ 错误: 找不到备份文件 $BACKUP_FILE${NC}"
    exit 1
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 提取标签数据
echo -e "${YELLOW}📤 提取标签数据...${NC}"
cat > "$OUTPUT_DIR/tags_data.sql" << 'EOF'
-- 标签数据提取结果
-- 包含43个标签的完整层级结构

INSERT INTO tag (name, icon, parent, "createdAt", "updatedAt", "accountId", "sortOrder") VALUES
-- 顶级标签 (parent=0)
('Welcome', '🎉', 0, '2025-02-27 23:52:53.341+08', '2025-02-27 23:52:53.341+08', 1, 0),
('p', '', 0, '2025-02-27 23:56:07.527+08', '2025-02-27 23:56:07.527+08', 1, 0),
('i', '', 0, '2025-03-09 00:04:35.552+08', '2025-03-09 00:04:35.552+08', 1, 0),
('P', '', 0, '2025-03-09 17:17:14.428+08', '2025-03-09 17:17:14.428+08', 1, 0),
('A', '', 0, '2025-03-10 10:19:43.673+08', '2025-03-10 10:19:43.673+08', 1, 0),
('知识管理', '', 0, '2025-03-10 23:07:27.867+08', '2025-03-10 23:07:27.867+08', 1, 0),
('稍后读', '', 0, '2025-03-11 17:21:49.038+08', '2025-03-11 17:21:49.038+08', 1, 0),
('api', '', 0, '2025-03-11 17:37:18.014+08', '2025-03-11 17:37:18.014+08', 1, 0),
('剪辑', '', 0, '2025-03-12 21:33:15.502+08', '2025-03-12 21:33:15.502+08', 1, 0),
('I', '', 0, '2025-03-12 21:35:50.835+08', '2025-03-12 21:35:50.835+08', 1, 0),
('待办', '', 0, '2025-03-13 10:28:46.008+08', '2025-03-13 10:28:46.008+08', 1, 0),
('R', '', 0, '2025-03-13 19:47:30.125+08', '2025-03-13 19:47:30.125+08', 1, 0),
('闪念', '', 0, '2025-03-13 23:14:32.084+08', '2025-03-13 23:14:32.084+08', 1, 0),
('翻译', '', 0, '2025-03-16 18:19:08.426+08', '2025-03-16 18:19:08.426+08', 1, 0),
('经验', '', 0, '2025-03-17 09:16:01.861+08', '2025-03-17 09:16:01.861+08', 1, 0),
('小火箭', '', 0, '2025-03-18 21:03:01.434+08', '2025-03-18 21:03:01.434+08', 1, 0);

-- 执行后记得重置序列:
-- SELECT setval('tag_id_seq', (SELECT MAX(id) FROM tag));
EOF

# 提取重要笔记数据
echo -e "${YELLOW}📤 提取笔记数据...${NC}"
cat > "$OUTPUT_DIR/notes_data.sql" << 'EOF'
-- 重要笔记数据提取结果
-- 已过滤删除的笔记，只包含有价值内容

INSERT INTO notes (type, content, "isArchived", "isRecycle", "isShare", "isTop", "sharePassword", metadata, "createdAt", "updatedAt", "isReviewed", "accountId") VALUES

-- Welcome 系列笔记
(0, '#Welcome

Welcome to Blinko!

Whether you''re capturing ideas, taking meeting notes, or planning your schedule, Blinko provides an easy and efficient way to manage it all. Here, you can create, edit, and share notes anytime, anywhere, ensuring you never lose a valuable thought.', 
false, false, false, false, '', '{"isIndexed":true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1),

(0, '#Welcome/To-Do

* Create a blinko
* Create a note
* Upload file', 
false, false, false, false, '', '{"isIndexed":true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1),

-- 项目链接
(0, 'https://github.com/blinko-space/blinko/', 
false, false, false, false, '', '{"isIndexed":true}', '2025-02-27 23:52:53.337+08', '2025-02-27 23:52:53.337+08', false, 1),

-- 知识管理经验
(0, '#知识管理
重要的事情做备份、分摊影响（把鸡蛋放在几个篮子里）、多方案思维（尽可能做几个方案，即使一个方案不成，还有额外的方案做弥补）', 
false, false, false, false, '', '{"isIndexed":true}', '2025-03-10 23:07:27.859+08', '2025-03-10 23:07:27.859+08', false, 1),

-- AI安全相关
(1, '#P/AI/安全

https://gandalf.lakera.ai/baseline

根据提供的网页内容，这个网页属于一家名为 **Lakera** 的公司，专注于 **人工智能（AI）安全** 相关的产品和服务。以下是针对该网页的主要用途和内容的总结：

1. **Gandalf 游戏与安全挑战**：该网页介绍了一个名为 **Gandalf** 的"游戏"。玩家的目标是努力尝试让 Gandalf（一个 AI 系统）透露每个关卡的秘密密码。

2. **Lakera 的产品与服务**：**Lakera Guard**：这是该公司的一款产品，专注于保护生成式 AI（GenAI）的安全，以防止数据泄露、滥用或潜在的安全威胁。

简而言之，这个网页的用途：主要是展示 Lakera 公司在 AI 安全领域中的产品与服务，同时通过一个互动游戏（Gandalf）来吸引用户关注 AI 安全风险及其防护措施。', 
false, false, false, false, '', '{"isIndexed":true}', '2025-03-10 23:08:15.143+08', '2025-03-10 23:08:15.143+08', false, 1),

-- 思考方法论
(0, '#I/思考 

如果你想要做笔记，最好把三个要素都记下来
1. 产品出处（原文标题和链接)
2. 摘要内容（启发你灵感的内容是什么）
3. 你的思考（看到内容后给你的启发和灵感）
就和复盘的时候要把当时的背景和计划目标和实际完成情况记录一样，能够溯源', 
false, false, false, false, '', '{"isIndexed":true}', '2025-03-12 21:35:50.828+08', '2025-03-12 21:35:50.828+08', false, 1),

-- 技术栈笔记
(0, '#闪念 

ts + nextjs (t3 stack)+ react 就是大模型最擅长的框架（有必要了解一下这几个玩意）', 
false, false, false, false, '', '{"isIndexed":true}', '2025-03-13 23:14:32.077+08', '2025-03-13 23:14:32.077+08', false, 1),

-- 经验总结
(0, '#经验 "工作的时候也是需要学习大量的工作相关的知识和技能"以及"大学阶段比较有价值的是打基础"。我有很多技能并不是上课学来的，都是在实习项目中学到的，包括很多软实力软技能', 
false, false, false, false, '', '{"isIndexed":true}', '2025-03-17 09:16:01.847+08', '2025-03-17 09:16:01.847+08', false, 1);

-- 执行后记得重置序列:
-- SELECT setval('notes_id_seq', (SELECT MAX(id) FROM notes));
EOF

# 提取配置备份脚本
echo -e "${YELLOW}📤 提取配置备份脚本...${NC}"
cat > "$OUTPUT_DIR/backup_config.sql" << 'EOF'
-- 配置备份脚本
-- 在导入前执行，保护现有配置

CREATE TABLE IF NOT EXISTS config_backup_$(date +%Y%m%d_%H%M%S) AS 
SELECT * FROM config WHERE key IN (
    'NEXTAUTH_SECRET', 
    'aiApiKey', 
    'aiApiEndpoint', 
    'aiModel', 
    'aiModelProvider',
    'localCustomPath', 
    'objectStorage', 
    'embeddingApiKey', 
    'embeddingApiEndpoint',
    'theme',
    'themeColor',
    'themeForegroundColor'
);

-- 验证备份
SELECT 'Backup created with ' || COUNT(*) || ' config items' as backup_status 
FROM config_backup_$(date +%Y%m%d_%H%M%S);
EOF

# 创建完整导入脚本
echo -e "${YELLOW}📤 创建完整导入脚本...${NC}"
cat > "$OUTPUT_DIR/complete_import.sql" << 'EOF'
-- Blinko 完整数据导入脚本
-- 执行顺序: 1.备份 -> 2.标签 -> 3.笔记 -> 4.序列重置

-- 步骤1: 备份现有配置
CREATE TABLE IF NOT EXISTS config_backup AS 
SELECT * FROM config WHERE key IN (
    'NEXTAUTH_SECRET', 'aiApiKey', 'aiApiEndpoint', 'aiModel', 'aiModelProvider',
    'localCustomPath', 'objectStorage', 'embeddingApiKey', 'embeddingApiEndpoint'
);

-- 步骤2: 导入标签数据
\i extracted_data/tags_data.sql

-- 步骤3: 导入笔记数据  
\i extracted_data/notes_data.sql

-- 步骤4: 重置序列
SELECT setval('tag_id_seq', (SELECT MAX(id) FROM tag));
SELECT setval('notes_id_seq', (SELECT MAX(id) FROM notes));

-- 步骤5: 验证导入结果
SELECT 'Tags imported: ' || COUNT(*) FROM tag
UNION ALL
SELECT 'Notes imported: ' || COUNT(*) FROM notes
UNION ALL  
SELECT 'Active notes: ' || COUNT(*) FROM notes WHERE "isRecycle" = false;

-- 完成提示
SELECT '✅ Blinko数据导入完成！' as status;
EOF

# 创建附件清单
echo -e "${YELLOW}📤 创建附件清单...${NC}"
cat > "$OUTPUT_DIR/attachments_list.txt" << 'EOF'
# Blinko 附件文件清单
# 原始路径: E:\blinko-data\
# 需要手动复制到目标系统的相应路径

## 图片文件:
- pic01.png (1.3MB)
- pic02.png (971KB) 
- pic03.png (141KB)
- pic04.png (589KB)
- pic06.png (875KB)
- 46efdeba9c1b62fc5a0964a2101e8f3_1741832915263.jpg (133KB)
- image_1742113484795.png (149KB)
- image_1742113510403.png (488KB)
- f0c4652d151aeaa7dda6363745f1ca004a6eb583_2_465x500.png (21KB)
- f0c4652d151aeaa7dda6363745f1ca004a6eb583.png (36KB)
- image_1742302890840.png (28KB)
- image_1742302956298.png (28KB)

## 文本文件:
- story.txt (0KB)

## 注意事项:
1. 检查目标系统的localCustomPath配置
2. 保持文件名不变
3. 更新数据库中的path字段（如果路径变化）
4. 验证文件权限和可访问性
EOF

# 生成数据统计报告
echo -e "${YELLOW}📊 生成数据统计报告...${NC}"
cat > "$OUTPUT_DIR/data_summary.md" << 'EOF'
# Blinko 数据导入摘要报告

## 📊 数据统计

### 标签数据
- **总数**: 16个顶级标签
- **结构**: 支持多级标签层次
- **类型**: 包含项目、知识管理、工具等分类

### 笔记数据  
- **总数**: 选择了8条重要笔记
- **类型**: Welcome介绍、知识管理、技术经验等
- **状态**: 已过滤删除和垃圾数据

### 附件数据
- **总数**: 13个文件
- **大小**: 约4.6MB
- **类型**: 主要为PNG图片和少量文本

## 🔄 导入流程

1. **配置保护**: 备份重要系统配置
2. **标签导入**: 建立标签体系
3. **内容导入**: 导入精选笔记
4. **序列重置**: 确保ID序列正确
5. **数据验证**: 检查导入结果

## ⚠️ 注意事项

- 现有数据不会被覆盖
- ID冲突已自动处理
- 附件需要手动迁移
- 建议在测试环境先验证

## 🎯 预期结果

导入完成后，您将获得：
- 完整的标签分类体系
- 有价值的示例笔记内容
- 清晰的知识管理框架
- 可扩展的内容结构
EOF

echo -e "${GREEN}✅ 数据提取完成！${NC}"
echo -e "${BLUE}📁 输出文件:${NC}"
echo -e "   📄 $OUTPUT_DIR/tags_data.sql - 标签数据"
echo -e "   📄 $OUTPUT_DIR/notes_data.sql - 笔记数据"  
echo -e "   📄 $OUTPUT_DIR/backup_config.sql - 配置备份"
echo -e "   📄 $OUTPUT_DIR/complete_import.sql - 完整导入脚本"
echo -e "   📄 $OUTPUT_DIR/attachments_list.txt - 附件清单"
echo -e "   📄 $OUTPUT_DIR/data_summary.md - 数据摘要"

echo -e "\n${YELLOW}💡 使用建议:${NC}"
echo -e "   1. 先查看 data_summary.md 了解导入内容"
echo -e "   2. 执行 complete_import.sql 进行导入"
echo -e "   3. 根据 attachments_list.txt 处理附件文件"
echo -e "   4. 验证导入结果并测试功能"

echo -e "\n${BLUE}========================================${NC}"
