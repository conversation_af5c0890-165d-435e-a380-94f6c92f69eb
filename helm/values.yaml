# Default values for blinko.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: blinko
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 1111
  targetPort: 1111

ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - paths:
        - path: /
          pathType: Prefix
  tls: []

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

livenessProbe:
  httpGet:
    path: /
    port: http
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 5

readinessProbe:
  httpGet:
    path: /
    port: http
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 5

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

# Blinko application configuration
config:
  # Base URL for the application
  baseUrl: "http://localhost:1111"
  # NextAuth configuration
  nextauth:
    url: "http://localhost:1111"
    secret: "my_ultra_secure_nextauth_secret"
  # Node environment
  nodeEnv: "production"

# External database configuration (when not using built-in PostgreSQL)
externalDatabase:
  # Set to true to use an external database
  enabled: false
  # Database type (postgresql)
  type: postgresql
  # Database host
  host: ""
  # Database port
  port: 5432
  # Database name
  database: postgres
  # Database username
  username: postgres
  # Database password
  password: ""
  # Existing secret containing database credentials
  existingSecret: ""
  # Key in the secret containing the password
  existingSecretPasswordKey: "password"

# PostgreSQL subchart configuration
postgresql:
  # Enable PostgreSQL subchart
  enabled: true
  auth:
    # PostgreSQL root user password
    postgresPassword: "mysecretpassword"
    # PostgreSQL username
    username: "postgres"
    # PostgreSQL password
    password: "mysecretpassword"
    # PostgreSQL database name
    database: "postgres"
  primary:
    service:
      ports:
        postgresql: 5432
    persistence:
      enabled: true
      size: 8Gi
  # PostgreSQL architecture
  architecture: standalone 