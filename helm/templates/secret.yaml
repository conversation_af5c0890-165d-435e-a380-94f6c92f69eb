apiVersion: v1
kind: Secret
metadata:
  name: {{ include "blinko.fullname" . }}-secret
  labels:
    {{- include "blinko.labels" . | nindent 4 }}
type: Opaque
data:
  NEXTAUTH_SECRET: {{ .Values.config.nextauth.secret | b64enc | quote }}
---
{{- if and .Values.externalDatabase.enabled (not .Values.externalDatabase.existingSecret) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "blinko.fullname" . }}-db-secret
  labels:
    {{- include "blinko.labels" . | nindent 4 }}
type: Opaque
data:
  password: {{ .Values.externalDatabase.password | b64enc | quote }}
{{- end }} 