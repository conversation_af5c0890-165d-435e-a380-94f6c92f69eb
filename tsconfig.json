{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "typeRoots": ["./node_modules/@types", "./server/types"], "paths": {"@/*": ["./app/src/*"], "@/routerTrpc/*": ["./server/routerTrpc/*"], "@/routerExpress/*": ["./server/routerExpress/*"]}}, "include": ["app/src", "server"], "references": [{"path": "./tsconfig.node.json"}]}