name: App Release

on:
  push:
    tags:
      - '*' # Match all tags, not limited to v prefix
  repository_dispatch:
    types: [trigger-app-release]
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag Version (e.g. 1.0.0)'
        required: true
        type: string

jobs:
  # Set version first
  set-version:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.set_version.outputs.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Set Version Variable
        id: set_version
        run: |
          # Print debug info
          echo "Event name: ${{ github.event_name }}"
          
          # Always use version number without v prefix
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            VERSION="${{ github.event.inputs.tag }}"
          elif [[ "${{ github.event_name }}" == "repository_dispatch" ]]; then
            if [[ "${{ github.event.client_payload.version }}" != "" ]]; then
              VERSION="${{ github.event.client_payload.version }}"
            else
              VERSION="${{ github.event.client_payload.tag }}"
            fi
          else
            # Get version from tag (remove v prefix if present)
            TAG="${GITHUB_REF_NAME}"
            VERSION="${TAG#v}"
          fi
          
          # Ensure VERSION is not empty, default to 1.0.0
          if [ -z "$VERSION" ]; then
            echo "WARNING: Empty version, using default 1.0.0"
            VERSION="1.0.0"
          fi
          
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Set version: $VERSION"

  # Update Tauri Config Version
  update-version:
    runs-on: ubuntu-latest
    needs: set-version
    permissions:
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Update Tauri Config Version
        run: |
          echo "Current version: ${{ needs.set-version.outputs.version }}"
          # Update version using jq
          jq '.version = "${{ needs.set-version.outputs.version }}"' app/src-tauri/tauri.conf.json > tmp.json && mv tmp.json app/src-tauri/tauri.conf.json
          echo "Updated tauri.conf.json:"
          cat app/src-tauri/tauri.conf.json
      
      - name: Commit and Push Updated Version
        run: |
          git config --global user.name 'GitHub Actions'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git add app/src-tauri/tauri.conf.json
          if git diff --staged --quiet; then
            echo "No changes to commit"
          else
            git commit -m "[ci skip] Update version to ${{ needs.set-version.outputs.version }}"
            git push
            echo "Pushed version update to repository"
          fi
      
      - name: Upload tauri.conf.json as artifact
        uses: actions/upload-artifact@v4
        with:
          name: tauri-config
          path: app/src-tauri/tauri.conf.json

  # Desktop Platform Build Task
  publish-desktop:
    needs: [set-version, update-version]
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: 'macos-latest' # Apple M-series chip (ARM)
            args: '--target aarch64-apple-darwin'
          - platform: 'macos-latest' # Intel chip Mac
            args: '--target x86_64-apple-darwin'
          - platform: 'ubuntu-22.04' # Linux Platform
            args: ''
          - platform: 'windows-latest' # Windows Platform
            args: ''

    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Download tauri.conf.json
        uses: actions/download-artifact@v4
        with:
          name: tauri-config
          path: app/src-tauri/

      - name: Fix version format for Windows MSI
        if: matrix.platform == 'windows-latest'
        run: |
          $versionJson = Get-Content -Path app/src-tauri/tauri.conf.json | ConvertFrom-Json
          $currentVersion = $versionJson.version
          
          if ($currentVersion -match '-(.+)$') {
            $newVersion = $currentVersion -replace '-(.+)$', ''
            
            $versionJson.version = $newVersion
            
            $jsonContent = Get-Content -Path app/src-tauri/tauri.conf.json -Raw
            $jsonContent = $jsonContent -replace '"version": "([^"]+)"', "`"version`": `"$newVersion`""
            $jsonContent | Set-Content -Path app/src-tauri/tauri.conf.json -NoNewline
            
            echo "Windows version $currentVersion changed to $newVersion"
          } else {
            echo "Version $currentVersion does not need to be modified"
          }

      - name: Install Ubuntu Dependencies
        if: matrix.platform == 'ubuntu-22.04'
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf

      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Install bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install Rust Stable
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.platform == 'macos-latest' && 'aarch64-apple-darwin,x86_64-apple-darwin' || '' }}

      - name: Rust Cache
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: app/src-tauri
          cache-on-failure: true

      - name: Cache Dependencies
        uses: actions/cache@v3
        with:
          path: |
            **/node_modules
          key: ${{ runner.os }}-modules-${{ hashFiles('**/bun.lockb', '**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-modules-

      - name: Install Dependencies
        run: |
          bun install
          cd app && bun install

      # Using official Tauri Action to build and publish
      - name: Build and Publish Desktop App
        uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          TAURI_SIGNING_PRIVATE_KEY: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY }}
          TAURI_SIGNING_PRIVATE_KEY_PASSWORD: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY_PASSWORD }}
        with:
          projectPath: 'app'
          tauriScript: '../node_modules/.bin/tauri'
          args: ${{ matrix.args }}
          tagName: ${{ needs.set-version.outputs.version }}
          releaseName: Blinko ${{ needs.set-version.outputs.version }}
          releaseBody: "Under construction, full changelog will be updated after build completion..."
          releaseDraft: false
          prerelease: false
          includeUpdaterJson: true

  # Android Platform Build Task
  publish-android:
    needs: [set-version, update-version]
    permissions:
      contents: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Download tauri.conf.json
        uses: actions/download-artifact@v4
        with:
          name: tauri-config
          path: app/src-tauri/

      - name: Setup JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '17'

      - name: Setup Android SDK
        uses: android-actions/setup-android@v3

      - name: Install NDK
        run: sdkmanager "ndk;27.0.11902837"

      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Install Rust Stable
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: aarch64-linux-android,armv7-linux-androideabi,i686-linux-android,x86_64-linux-android

      - name: Rust Cache
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: app/src-tauri
          cache-on-failure: true

      - name: Cache Dependencies
        uses: actions/cache@v3
        with:
          path: |
            **/node_modules
          key: ${{ runner.os }}-modules-${{ hashFiles('**/bun.lockb', '**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-modules-

      - name: Install bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install Dependencies
        run: |
          bun install
          cd app && bun install

      - name: Run Prisma Generate
        run: bun run prisma:generate

      - name: Configure Gradle Cache
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Install Tauri CLI
        run: cargo install tauri-cli --version "^2.0.0-alpha"

      - name: Upload Keystore File
        run: |
          # Create keystore directory
          mkdir -p ~/.android
          # Create keystore file
          echo "${{ secrets.UPLOAD_KEYSTORE }}" | base64 --decode > ~/.android/upload-keystore.jks
          # Create keystore.properties
          mkdir -p app/src-tauri/gen/android
          cat > app/src-tauri/gen/android/keystore.properties << EOF
          password=106111
          keyAlias=upload
          storeFile=$HOME/.android/upload-keystore.jks
          EOF

      - name: Build Android App
        run: |
          cd app
          bun run tauri:android:build
        env:
          NDK_HOME: ${{ env.ANDROID_HOME }}/ndk/27.0.11902837

      - name: Rename Android App File
        run: |
          cd app/src-tauri/gen/android/app/build/outputs/apk/universal/release
          VERSION="${{ needs.set-version.outputs.version }}"
          echo "Original APK file:"
          ls -la
          # Rename APK file
          mv app-universal-release.apk Blinko_${VERSION}_universal.apk
          echo "Renamed APK file:"
          ls -la

      - name: Publish Android App
        uses: softprops/action-gh-release@v1
        with:
          files: app/src-tauri/gen/android/app/build/outputs/apk/universal/release/Blinko_${{ needs.set-version.outputs.version }}_universal.apk
          tag_name: ${{ needs.set-version.outputs.version }}
          body: "Under construction, full changelog will be updated after build completion..."
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Generate changelog after all builds are complete
  generate-changelog:
    runs-on: ubuntu-latest
    needs: [set-version, publish-desktop, publish-android]
    outputs:
      changelog: ${{ steps.changelog.outputs.changes }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Fetch all tags
        run: git fetch --tags
      
      - name: Create Changelog
        id: changelog
        uses: loopwerk/tag-changelog@v1.3.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          config_file: .github/changelog/changelog.js

  # Update release with final changelog
  update-release:
    runs-on: ubuntu-latest
    needs: [set-version, generate-changelog]
    permissions:
      contents: write
    steps:
      - name: Update GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ needs.set-version.outputs.version }}
          body: ${{ needs.generate-changelog.outputs.changelog }}
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} 