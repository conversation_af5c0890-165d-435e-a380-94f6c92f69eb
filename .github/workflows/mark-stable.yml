name: Mark <PERSON>able Version

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'stable tag version (such as: 1.2.3)'
        required: true
        type: string

jobs:
  tag-stable:
    runs-on: ubuntu-latest
    steps:
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ github.token }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Create and Push Multi-arch Manifests
        run: |
          # Docker Hub
          docker buildx imagetools create -t blinkospace/blinko:stable \
            blinkospace/blinko:${{ inputs.version }}
          
          # GitHub Container Registry
          docker buildx imagetools create -t ghcr.io/blinko-space/blinko:stable \
            ghcr.io/blinko-space/blinko:${{ inputs.version }} 