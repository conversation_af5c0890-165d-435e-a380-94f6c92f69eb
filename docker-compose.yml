# docker-compose.dev.yml
version: '3'

networks:
  blinko-network:
    driver: bridge

volumes:
  postgres-data:

services:
  blinko-website:
    container_name: blinko-website
    image: blinko-with-notion:latest
    environment:
      NODE_ENV: production
      NEXTAUTH_URL: http://ccnu.me
      NEXT_PUBLIC_BASE_URL: http://ccnu.me
      NEXTAUTH_SECRET: my_ultra_secure_nextauth_secret
      DATABASE_URL: ****************************************************/postgres
    depends_on:
      postgres:
        condition: service_healthy
    restart: always
    ports:
      - 1111:1111

    networks:
      - blinko-network

  postgres:
    image: postgres:14
    container_name: blinko-postgres
    restart: always
    ports:
      - 5432:5432
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: mysecretpassword
    healthcheck:
      test:
        ["CMD", "pg_isready", "-U", "postgres", "-d", "postgres"]
      interval: 5s
      timeout: 10s
      retries: 5
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - blinko-network
