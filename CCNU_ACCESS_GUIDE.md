# 🌐 Blinko ccnu.me 访问指南

## 🎯 您的Blinko服务

**服务地址**: https://ccnu.me  
**域名**: ccnu.me  
**端口映射**: 1111 (Docker内部)  
**数据导入状态**: ✅ 已完成

## 📊 导入成功验证

### 立即访问
点击访问您的Blinko服务：**https://ccnu.me**

### 验证新内容
登录后请检查以下导入的内容：

#### 新增标签 🏷️
- 📥 **备份导入** - 导入相关内容
- 💡 **经验总结** - 个人经验记录  
- 🤖 **AI技术** - AI工具和技术
- 🔧 **开发工具** - 开发相关工具

#### 新增笔记 📝
- **备份导入说明** - 导入成功提示
- **知识管理方法论** - 管理原则和方法
- **AI技术栈** - 现代开发技术

## 🔧 技术信息

### Docker环境
```bash
# 查看服务状态
docker ps | grep blinko

# 查看日志
docker logs blinko-website

# 重启应用（如需要）
docker restart blinko-website
```

### 数据库信息
```bash
# 连接数据库
docker exec -it blinko-postgres psql -U postgres -d blinko

# 查看导入统计
docker exec blinko-postgres psql -U postgres -d blinko -c "
SELECT 'Tags: ' || COUNT(*) FROM tag
UNION ALL  
SELECT 'Notes: ' || COUNT(*) FROM notes WHERE \"isRecycle\" = false;"
```

## 📱 使用建议

### 1. 界面熟悉
- 浏览新导入的标签系统
- 查看示例笔记内容
- 测试搜索功能

### 2. 个性化设置
- 调整标签图标和颜色
- 修改笔记内容
- 添加个人标签

### 3. 功能测试
- 创建新笔记
- 使用标签分类
- 测试附件上传
- 验证搜索准确性

## 🔍 故障排除

### 如果无法访问 ccnu.me
1. **检查域名解析**
   ```bash
   nslookup ccnu.me
   ping ccnu.me
   ```

2. **检查防火墙/端口**
   ```bash
   # 如果使用nginx代理
   sudo nginx -t
   sudo systemctl status nginx
   
   # 如果直接暴露端口
   netstat -tlnp | grep 1111
   ```

3. **检查Docker容器**
   ```bash
   docker ps
   docker logs blinko-website
   ```

### 如果新内容未显示
1. **清除浏览器缓存** - Ctrl+F5 或 Cmd+Shift+R
2. **重启Blinko应用**
   ```bash
   docker restart blinko-website
   ```
3. **检查数据库数据**
   ```bash
   docker exec blinko-postgres psql -U postgres -d blinko -c "SELECT name FROM tag ORDER BY \"createdAt\" DESC LIMIT 5;"
   ```

## 🔄 备份与维护

### 定期备份
```bash
# 备份数据库
docker exec blinko-postgres pg_dump -U postgres blinko > blinko_backup_$(date +%Y%m%d).sql

# 备份整个Docker卷（如有）
docker run --rm -v blinko_data:/data -v $(pwd):/backup alpine tar czf /backup/blinko_data_backup.tar.gz -C /data .
```

### 更新应用
```bash
# 拉取最新镜像
docker pull blinkospace/blinko:latest

# 重启服务
docker-compose down && docker-compose up -d
```

## 📞 支持信息

### 配置文件位置
- Docker Compose: `docker-compose.yml`
- Nginx配置: `/etc/nginx/sites-available/ccnu.me`
- SSL证书: `/etc/letsencrypt/live/ccnu.me/`

### 日志位置
```bash
# 应用日志
docker logs blinko-website

# 数据库日志  
docker logs blinko-postgres

# Nginx日志（如果使用）
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

---

## 🎉 成功！

您的Blinko服务现在可以通过 **https://ccnu.me** 访问，并且已经成功导入了备份数据。

**立即体验**: [https://ccnu.me](https://ccnu.me)

享受您的个人知识管理系统！ 🚀
