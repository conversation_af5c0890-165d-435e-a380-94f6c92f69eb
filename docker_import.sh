#!/bin/bash
# ========================================
# Blinko Docker环境数据导入脚本
# 适用于Docker容器中的PostgreSQL数据库
# ========================================

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Docker容器配置
POSTGRES_CONTAINER="blinko-postgres"
BLINKO_CONTAINER="blinko-website"

echo -e "${BLUE}🐳 Blinko Docker环境数据导入${NC}"
echo -e "${BLUE}================================${NC}"

# 检查Docker容器状态
echo -e "${YELLOW}🔍 检查Docker容器状态...${NC}"
if ! docker ps | grep -q "$POSTGRES_CONTAINER"; then
    echo -e "${RED}❌ PostgreSQL容器 '$POSTGRES_CONTAINER' 未运行${NC}"
    echo -e "${YELLOW}💡 请先启动Blinko服务：docker-compose up -d${NC}"
    exit 1
fi

if ! docker ps | grep -q "$BLINKO_CONTAINER"; then
    echo -e "${RED}❌ Blinko容器 '$BLINKO_CONTAINER' 未运行${NC}"
    echo -e "${YELLOW}💡 请先启动Blinko服务：docker-compose up -d${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker容器状态正常${NC}"

# 测试数据库连接
echo -e "${YELLOW}🔗 测试数据库连接...${NC}"
if ! docker exec $POSTGRES_CONTAINER psql -U postgres -d blinko -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${RED}❌ 数据库连接失败${NC}"
    echo -e "${YELLOW}💡 检查数据库容器是否正常启动${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 数据库连接成功${NC}"

# 确认操作
echo -e "\n${YELLOW}⚠️  警告: 此操作将向Docker容器中的数据库导入新数据${NC}"
echo -e "${YELLOW}   现有数据不会被删除，但会添加新的标签和笔记${NC}"
echo -e "${YELLOW}   容器: $POSTGRES_CONTAINER${NC}"
read -p "是否继续？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}操作已取消${NC}"
    exit 0
fi

# 函数：执行Docker中的SQL
execute_docker_sql() {
    local description="$1"
    local sql="$2"
    
    echo -e "${YELLOW}📝 $description...${NC}"
    if docker exec $POSTGRES_CONTAINER psql -U postgres -d blinko -c "$sql" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $description 完成${NC}"
        return 0
    else
        echo -e "${RED}❌ $description 失败${NC}"
        return 1
    fi
}

# 函数：执行Docker中的SQL文件
execute_docker_sql_file() {
    local description="$1"
    local sql_file="$2"
    
    echo -e "${YELLOW}📝 $description...${NC}"
    
    # 将SQL文件复制到容器中
    docker cp "$sql_file" $POSTGRES_CONTAINER:/tmp/import.sql
    
    if docker exec $POSTGRES_CONTAINER psql -U postgres -d blinko -f /tmp/import.sql > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $description 完成${NC}"
        # 清理临时文件
        docker exec $POSTGRES_CONTAINER rm -f /tmp/import.sql
        return 0
    else
        echo -e "${RED}❌ $description 失败${NC}"
        docker exec $POSTGRES_CONTAINER rm -f /tmp/import.sql
        return 1
    fi
}

# 开始导入流程
echo -e "\n${BLUE}📥 开始数据导入流程...${NC}"

# 步骤1: 备份现有配置
echo -e "${YELLOW}1/4 备份现有配置...${NC}"
execute_docker_sql "备份重要配置" "
CREATE TABLE IF NOT EXISTS config_backup_$(date +%Y%m%d_%H%M%S) AS 
SELECT * FROM config WHERE key IN (
    'NEXTAUTH_SECRET', 'aiApiKey', 'aiApiEndpoint', 'aiModel', 'aiModelProvider',
    'localCustomPath', 'objectStorage', 'embeddingApiKey', 'embeddingApiEndpoint',
    'theme', 'themeColor', 'themeForegroundColor'
);"

# 步骤2: 导入标签数据
echo -e "${YELLOW}2/4 导入标签数据...${NC}"
if [ -f "extracted_data/tags_data.sql" ]; then
    execute_docker_sql_file "导入标签" "extracted_data/tags_data.sql"
else
    echo -e "${YELLOW}   使用内置标签数据...${NC}"
    execute_docker_sql "导入基础标签" "
    INSERT INTO tag (name, icon, parent, \"createdAt\", \"updatedAt\", \"accountId\", \"sortOrder\") VALUES
    ('Welcome', '🎉', 0, NOW(), NOW(), 1, 0),
    ('知识管理', '', 0, NOW(), NOW(), 1, 0),
    ('稍后读', '', 0, NOW(), NOW(), 1, 0),
    ('翻译', '', 0, NOW(), NOW(), 1, 0),
    ('经验', '', 0, NOW(), NOW(), 1, 0),
    ('api', '', 0, NOW(), NOW(), 1, 0),
    ('闪念', '', 0, NOW(), NOW(), 1, 0),
    ('项目', '', 0, NOW(), NOW(), 1, 0),
    ('AI', '', 0, NOW(), NOW(), 1, 0),
    ('资源', '', 0, NOW(), NOW(), 1, 0)
    ON CONFLICT (name) DO NOTHING;"
fi

# 步骤3: 导入笔记数据
echo -e "${YELLOW}3/4 导入笔记数据...${NC}"
if [ -f "extracted_data/notes_data.sql" ]; then
    execute_docker_sql_file "导入笔记" "extracted_data/notes_data.sql"
else
    echo -e "${YELLOW}   使用内置笔记数据...${NC}"
    execute_docker_sql "导入示例笔记" "
    INSERT INTO notes (type, content, \"isArchived\", \"isRecycle\", \"isShare\", \"isTop\", metadata, \"createdAt\", \"updatedAt\", \"accountId\") VALUES
    (0, '#Welcome

Welcome to Blinko!

Whether you''re capturing ideas, taking meeting notes, or planning your schedule, Blinko provides an easy and efficient way to manage it all.', 
    false, false, false, false, '{\"isIndexed\":true}', NOW(), NOW(), 1),
    
    (0, '#知识管理
重要的事情做备份、分摊影响（把鸡蛋放在几个篮子里）、多方案思维（尽可能做几个方案，即使一个方案不成，还有额外的方案做弥补）', 
    false, false, false, false, '{\"isIndexed\":true}', NOW(), NOW(), 1),
    
    (0, '#经验
工作的时候也是需要学习大量的工作相关的知识和技能，以及大学阶段比较有价值的是打基础。我有很多技能并不是上课学来的，都是在实习项目中学到的，包括很多软实力软技能', 
    false, false, false, false, '{\"isIndexed\":true}', NOW(), NOW(), 1),
    
    (0, '#闪念
ts + nextjs (t3 stack)+ react 就是大模型最擅长的框架（有必要了解一下这几个玩意）', 
    false, false, false, false, '{\"isIndexed\":true}', NOW(), NOW(), 1);"
fi

# 步骤4: 重置序列计数器
echo -e "${YELLOW}4/4 重置序列计数器...${NC}"
execute_docker_sql "重置序列" "
SELECT setval('tag_id_seq', (SELECT MAX(id) FROM tag));
SELECT setval('notes_id_seq', (SELECT MAX(id) FROM notes));
SELECT setval('attachments_id_seq', (SELECT COALESCE(MAX(id), 1) FROM attachments));"

# 验证导入结果
echo -e "\n${BLUE}📊 导入结果验证:${NC}"
RESULT=$(docker exec $POSTGRES_CONTAINER psql -U postgres -d blinko -t -c "
SELECT 'Tags: ' || COUNT(*) FROM tag
UNION ALL
SELECT 'Notes: ' || COUNT(*) FROM notes  
UNION ALL
SELECT 'Active Notes: ' || COUNT(*) FROM notes WHERE \"isRecycle\" = false;
" | tr -d ' ')

echo -e "${GREEN}$RESULT${NC}"

# 检查Blinko应用状态
echo -e "\n${YELLOW}🔄 重启Blinko应用以刷新缓存...${NC}"
docker restart $BLINKO_CONTAINER > /dev/null 2>&1
echo -e "${GREEN}✅ Blinko应用已重启${NC}"

echo -e "\n${GREEN}🎉 Docker环境数据导入完成！${NC}"
echo -e "\n${YELLOW}📝 后续步骤:${NC}"
echo -e "   1. 打开浏览器访问 https://ccnu.me"
echo -e "   2. 检查标签和笔记是否正常显示"
echo -e "   3. 测试搜索和标签功能"
echo -e "   4. 如有附件需要，请查看 extracted_data/attachments_list.txt"

echo -e "\n${BLUE}🐳 Docker容器状态:${NC}"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(blinko|postgres)"

echo -e "\n${BLUE}================================${NC}"
