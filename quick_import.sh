#!/bin/bash
# ========================================
# Blinko 快速导入脚本
# 一键执行数据导入
# ========================================

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Blinko 快速数据导入${NC}"
echo -e "${BLUE}================================${NC}"

# 检查PostgreSQL连接
echo -e "${YELLOW}🔍 检查数据库连接...${NC}"
if ! command -v psql &> /dev/null; then
    echo -e "${RED}❌ PostgreSQL客户端未安装${NC}"
    exit 1
fi

# 设置数据库变量（如果未设置）
DB_NAME=${DB_NAME:-blinko}
DB_USER=${DB_USER:-postgres}
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}

echo -e "${GREEN}📊 数据库配置:${NC}"
echo -e "   主机: $DB_HOST:$DB_PORT"
echo -e "   数据库: $DB_NAME"
echo -e "   用户: $DB_USER"

# 测试连接
if ! psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${RED}❌ 数据库连接失败${NC}"
    echo -e "${YELLOW}💡 请检查连接参数或运行:${NC}"
    echo -e "   export PGPASSWORD=your_password"
    echo -e "   export DB_HOST=your_host"
    echo -e "   export DB_USER=your_username"
    exit 1
fi

echo -e "${GREEN}✅ 数据库连接成功${NC}"

# 确认操作
echo -e "\n${YELLOW}⚠️  警告: 此操作将向数据库导入新数据${NC}"
echo -e "${YELLOW}   现有数据不会被删除，但会添加新内容${NC}"
read -p "是否继续？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}操作已取消${NC}"
    exit 0
fi

# 执行导入
echo -e "\n${BLUE}📥 开始导入数据...${NC}"

# 步骤1: 备份配置
echo -e "${YELLOW}1/4 备份现有配置...${NC}"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f extracted_data/backup_config.sql > /dev/null 2>&1
echo -e "${GREEN}✅ 配置备份完成${NC}"

# 步骤2: 导入标签
echo -e "${YELLOW}2/4 导入标签数据...${NC}"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f extracted_data/tags_data.sql > /dev/null 2>&1
echo -e "${GREEN}✅ 标签导入完成${NC}"

# 步骤3: 导入笔记
echo -e "${YELLOW}3/4 导入笔记数据...${NC}"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f extracted_data/notes_data.sql > /dev/null 2>&1
echo -e "${GREEN}✅ 笔记导入完成${NC}"

# 步骤4: 重置序列
echo -e "${YELLOW}4/4 重置序列计数器...${NC}"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT setval('tag_id_seq', (SELECT MAX(id) FROM tag));
SELECT setval('notes_id_seq', (SELECT MAX(id) FROM notes));
" > /dev/null 2>&1
echo -e "${GREEN}✅ 序列重置完成${NC}"

# 验证结果
echo -e "\n${BLUE}📊 导入结果验证:${NC}"
RESULT=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
SELECT 'Tags: ' || COUNT(*) FROM tag
UNION ALL
SELECT 'Notes: ' || COUNT(*) FROM notes  
UNION ALL
SELECT 'Active Notes: ' || COUNT(*) FROM notes WHERE \"isRecycle\" = false;
" | tr -d ' ')

echo -e "${GREEN}$RESULT${NC}"

echo -e "\n${GREEN}🎉 数据导入完成！${NC}"
echo -e "\n${YELLOW}📝 后续步骤:${NC}"
echo -e "   1. 查看 extracted_data/attachments_list.txt 处理附件文件"
echo -e "   2. 登录Blinko检查标签和笔记是否正常显示"
echo -e "   3. 测试搜索和标签功能"
echo -e "   4. 根据需要调整标签结构"

echo -e "\n${BLUE}================================${NC}"
