import { NOTION_SYNC_TASK_NAME } from "@shared/lib/sharedConstant";
import { prisma } from "../prisma";
import { BaseScheduleJob } from "./baseScheduleJob";
import { NotionService } from "../lib/notionService";
import { DataTransformer } from "../lib/dataTransformer";
import { SyncStateManager, SyncResult } from "../lib/syncStateManager";
import { getGlobalConfig } from "../routerTrpc/config";
import { Note } from "@shared/lib/types";

export class NotionSyncJob extends BaseScheduleJob {
  protected static taskName = NOTION_SYNC_TASK_NAME;
  protected static job = this.createJob();

  static {
    this.initializeJob();
  }

  protected static async RunTask(): Promise<SyncResult> {
    const startTime = Date.now();
    let syncedCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    try {
      // 检查是否已在运行
      const isRunning = await SyncStateManager.isSyncRunning();
      if (isRunning) {
        throw new Error('Notion sync is already running');
      }

      // 标记同步开始
      await SyncStateManager.markSyncStart();

      // 检查配置
      const config = await getGlobalConfig({ useAdmin: true });
      if (!config.notionSyncEnabled) {
        throw new Error('Notion sync is disabled');
      }

      if (!config.notionApiToken) {
        throw new Error('Notion API token is not configured');
      }

      // 初始化Notion服务
      const notionService = new NotionService();
      
      // 测试连接
      const isConnected = await notionService.testConnection();
      if (!isConnected) {
        throw new Error('Failed to connect to Notion API');
      }

      // 获取或创建数据库
      let databaseId = config.notionDatabaseId;
      if (!databaseId) {
        console.log('Creating new Notion database for Blinko sync...');
        databaseId = await notionService.createDatabase('Blinko Notes Backup');
        
        // 保存数据库ID到配置
        await prisma.config.upsert({
          where: { key: 'notionDatabaseId' },
          update: {
            config: {
              type: 'string',
              value: databaseId,
            },
          },
          create: {
            key: 'notionDatabaseId',
            config: {
              type: 'string',
              value: databaseId,
            },
          },
        });
      }

      // 获取上次同步时间
      const lastSyncTime = await SyncStateManager.getLastSyncTime();
      console.log(`Starting incremental sync from: ${lastSyncTime.toISOString()}`);

      // 构建增量查询条件
      const queryFilter = DataTransformer.buildIncrementalFilter(lastSyncTime);

      // 获取需要同步的笔记
      const notesToSync = await prisma.notes.findMany(queryFilter);
      console.log(`Found ${notesToSync.length} notes to sync`);

      // 批量处理笔记
      for (const note of notesToSync) {
        try {
          await this.syncSingleNote(notionService, databaseId, note as Note);
          syncedCount++;
        } catch (error) {
          errorCount++;
          const errorMsg = `Failed to sync note ${note.id}: ${error.message}`;
          errors.push(errorMsg);
          console.error(errorMsg);
          
          // 如果错误太多，停止同步
          if (errorCount > 10) {
            throw new Error('Too many sync errors, stopping sync');
          }
        }
      }

      // 计算执行时间
      const duration = Date.now() - startTime;

      // 创建同步结果
      const result: SyncResult = {
        success: errorCount === 0,
        syncedCount,
        errorCount,
        errors,
        duration,
      };

      // 标记同步完成
      await SyncStateManager.markSyncComplete(result);

      console.log(`Notion sync completed: ${syncedCount} synced, ${errorCount} errors, ${duration}ms`);
      return result;

    } catch (error) {
      errorCount++;
      const errorMsg = error.message || 'Unknown sync error';
      errors.push(errorMsg);
      
      // 记录同步错误
      await SyncStateManager.recordSyncError(errorMsg);
      
      console.error('Notion sync failed:', errorMsg);
      
      const duration = Date.now() - startTime;
      return {
        success: false,
        syncedCount,
        errorCount,
        errors,
        duration,
      };
    }
  }

  /**
   * 同步单个笔记到Notion
   */
  private static async syncSingleNote(
    notionService: NotionService,
    databaseId: string,
    note: Note
  ): Promise<void> {
    // 验证笔记数据
    if (!DataTransformer.validateNoteData(note)) {
      throw new Error(`Invalid note data for note ${note.id}`);
    }

    // 检查笔记是否已存在于Notion
    const existingPage = await notionService.findPageByBlinkoId(databaseId, note.id);

    // 转换笔记数据
    const notionProperties = DataTransformer.transformNoteToNotionProperties(note);

    if (existingPage) {
      // 更新现有页面
      await notionService.updatePage(existingPage.id, notionProperties);
      console.log(`Updated note ${note.id} in Notion`);
    } else {
      // 创建新页面
      await notionService.createPage(databaseId, notionProperties);
      console.log(`Created note ${note.id} in Notion`);
    }
  }

  /**
   * 手动触发同步
   */
  static async ManualSync(): Promise<SyncResult> {
    console.log('Manual Notion sync triggered');
    return await this.RunTask();
  }

  /**
   * 获取同步状态
   */
  static async GetSyncStatus() {
    return await SyncStateManager.getSyncState();
  }

  /**
   * 获取同步统计
   */
  static async GetSyncStats() {
    return await SyncStateManager.getSyncStats();
  }

  /**
   * 重置同步状态（用于调试）
   */
  static async ResetSyncState(): Promise<void> {
    await SyncStateManager.resetSyncState();
    console.log('Notion sync state has been reset');
  }
}
