import { NOTION_SYNC_TASK_NAME } from "@shared/lib/sharedConstant";
import { prisma } from "../prisma";
import { BaseScheduleJob } from "./baseScheduleJob";
import { NotionService } from "../lib/notionService";
import { DataTransformer } from "../lib/dataTransformer";
import { SyncStateManager, SyncResult } from "../lib/syncStateManager";
import { getGlobalConfig } from "../routerTrpc/config";
import { Note } from "@shared/lib/types";

export class NotionSyncJob extends BaseScheduleJob {
  protected static taskName = NOTION_SYNC_TASK_NAME;
  protected static job = this.createJob();

  static {
    this.initializeJob();
  }

  protected static async RunTask(): Promise<SyncResult> {
    const startTime = Date.now();
    let syncedCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    try {
      // 检查是否已在运行
      const isRunning = await SyncStateManager.isSyncRunning();
      if (isRunning) {
        throw new Error('Notion sync is already running');
      }

      // 标记同步开始
      await SyncStateManager.markSyncStart();

      // 检查配置
      const config = await getGlobalConfig({ useAdmin: true });
      if (!config.notionSyncEnabled) {
        throw new Error('Notion sync is disabled');
      }

      if (!config.notionApiToken) {
        throw new Error('Notion API token is not configured');
      }

      // 初始化Notion服务
      const notionService = new NotionService();
      
      // 测试连接
      const isConnected = await notionService.testConnection();
      if (!isConnected) {
        throw new Error('Failed to connect to Notion API');
      }

      // 获取或创建数据库
      let databaseId = config.notionDatabaseId;
      if (!databaseId) {
        console.log('Creating new Notion database for Blinko sync...');
        databaseId = await notionService.createDatabase('Blinko Notes Backup');
        
        // 保存数据库ID到配置
        await prisma.config.upsert({
          where: { key: 'notionDatabaseId' },
          update: {
            config: {
              type: 'string',
              value: databaseId,
            },
          },
          create: {
            key: 'notionDatabaseId',
            config: {
              type: 'string',
              value: databaseId,
            },
          },
        });
      }

      // 获取上次同步时间
      const lastSyncTime = await SyncStateManager.getLastSyncTime();
      console.log(`Starting incremental sync from: ${lastSyncTime.toISOString()}`);

      // 构建增量查询条件
      const queryFilter = DataTransformer.buildIncrementalFilter(lastSyncTime);

      // 获取需要同步的笔记
      const notesToSync = await prisma.notes.findMany(queryFilter);
      console.log(`Found ${notesToSync.length} notes to sync`);

      // 批量处理笔记，使用并发控制
      const batchSize = 5; // 控制并发数量，避免API限制
      for (let i = 0; i < notesToSync.length; i += batchSize) {
        const batch = notesToSync.slice(i, i + batchSize);

        const batchPromises = batch.map(async (note) => {
          try {
            await this.syncSingleNote(notionService, databaseId, note as Note);
            return { success: true, noteId: note.id };
          } catch (error) {
            const errorMsg = `Failed to sync note ${note.id}: ${error.message}`;
            console.error(errorMsg);
            return { success: false, noteId: note.id, error: errorMsg };
          }
        });

        const batchResults = await Promise.allSettled(batchPromises);

        // 处理批次结果
        for (const result of batchResults) {
          if (result.status === 'fulfilled') {
            const { success, error } = result.value;
            if (success) {
              syncedCount++;
            } else {
              errorCount++;
              errors.push(error);
            }
          } else {
            errorCount++;
            errors.push(`Batch processing failed: ${result.reason}`);
          }
        }

        // 如果错误太多，停止同步
        if (errorCount > 10) {
          throw new Error('Too many sync errors, stopping sync');
        }

        // 添加延迟以避免API限制
        if (i + batchSize < notesToSync.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 计算执行时间
      const duration = Date.now() - startTime;

      // 创建同步结果
      const result: SyncResult = {
        success: errorCount === 0,
        syncedCount,
        errorCount,
        errors,
        duration,
      };

      // 标记同步完成
      await SyncStateManager.markSyncComplete(result);

      console.log(`Notion sync completed: ${syncedCount} synced, ${errorCount} errors, ${duration}ms`);
      return result;

    } catch (error) {
      errorCount++;
      const errorMsg = error.message || 'Unknown sync error';
      errors.push(errorMsg);
      
      // 记录同步错误
      await SyncStateManager.recordSyncError(errorMsg);
      
      console.error('Notion sync failed:', errorMsg);
      
      const duration = Date.now() - startTime;
      return {
        success: false,
        syncedCount,
        errorCount,
        errors,
        duration,
      };
    }
  }

  /**
   * 同步单个笔记到Notion
   */
  private static async syncSingleNote(
    notionService: NotionService,
    databaseId: string,
    note: Note
  ): Promise<void> {
    // 验证笔记数据
    if (!DataTransformer.validateNoteData(note)) {
      throw new Error(`Invalid note data for note ${note.id}`);
    }

    // 实现重试机制
    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // 检查笔记是否已存在于Notion
        const existingPage = await notionService.findPageByBlinkoId(databaseId, note.id);

        // 转换笔记数据
        const notionProperties = DataTransformer.transformNoteToNotionProperties(note);

        if (existingPage) {
          // 更新现有页面
          await notionService.updatePage(existingPage.id, notionProperties);
          console.log(`Updated note ${note.id} in Notion (attempt ${attempt})`);
        } else {
          // 创建新页面
          await notionService.createPage(databaseId, notionProperties);
          console.log(`Created note ${note.id} in Notion (attempt ${attempt})`);
        }

        // 成功则跳出重试循环
        return;

      } catch (error) {
        lastError = error;
        console.warn(`Sync attempt ${attempt} failed for note ${note.id}: ${error.message}`);

        // 如果是API限制错误，等待更长时间
        if (error.message.includes('rate_limited') || error.message.includes('429')) {
          const waitTime = Math.pow(2, attempt) * 1000; // 指数退避
          console.log(`Rate limited, waiting ${waitTime}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        } else if (attempt < maxRetries) {
          // 其他错误，短暂等待后重试
          await new Promise(resolve => setTimeout(resolve, 500 * attempt));
        }
      }
    }

    // 所有重试都失败，抛出最后的错误
    throw new Error(`Failed to sync note ${note.id} after ${maxRetries} attempts: ${lastError?.message}`);
  }

  /**
   * 手动触发同步
   */
  static async ManualSync(): Promise<SyncResult> {
    console.log('Manual Notion sync triggered');
    return await this.RunTask();
  }

  /**
   * 获取同步状态
   */
  static async GetSyncStatus() {
    return await SyncStateManager.getSyncState();
  }

  /**
   * 获取同步统计
   */
  static async GetSyncStats() {
    return await SyncStateManager.getSyncStats();
  }

  /**
   * 重置同步状态（用于调试）
   */
  static async ResetSyncState(): Promise<void> {
    await SyncStateManager.resetSyncState();
    console.log('Notion sync state has been reset');
  }
}
