import { Note, NoteType } from '@shared/lib/types';

export interface NotionPageProperties {
  Title: {
    title: Array<{
      type: 'text';
      text: { content: string };
    }>;
  };
  Content: {
    rich_text: Array<{
      type: 'text';
      text: { content: string };
    }>;
  };
  Tags: {
    multi_select: Array<{
      name: string;
    }>;
  };
  Type: {
    select: {
      name: string;
    };
  };
  'Blinko ID': {
    number: number;
  };
  'Created At': {
    date: {
      start: string;
    };
  };
  'Updated At': {
    date: {
      start: string;
    };
  };
  'Is Archived': {
    checkbox: boolean;
  };
  'Is Top': {
    checkbox: boolean;
  };
}

export class DataTransformer {
  /**
   * 将Blinko笔记转换为Notion页面属性
   */
  static transformNoteToNotionProperties(note: Note): NotionPageProperties {
    // 提取标题（取内容的前50个字符作为标题）
    const title = this.extractTitle(note.content);
    
    // 转换笔记类型
    const noteType = this.transformNoteType(note.type);
    
    // 转换标签
    const tags = this.transformTags(note.tags);
    
    return {
      Title: {
        title: [
          {
            type: 'text',
            text: { content: title },
          },
        ],
      },
      Content: {
        rich_text: [
          {
            type: 'text',
            text: { content: this.sanitizeContent(note.content) },
          },
        ],
      },
      Tags: {
        multi_select: tags,
      },
      Type: {
        select: {
          name: noteType,
        },
      },
      'Blinko ID': {
        number: note.id,
      },
      'Created At': {
        date: {
          start: note.createdAt.toISOString(),
        },
      },
      'Updated At': {
        date: {
          start: note.updatedAt.toISOString(),
        },
      },
      'Is Archived': {
        checkbox: note.isArchived,
      },
      'Is Top': {
        checkbox: note.isTop,
      },
    };
  }

  /**
   * 从笔记内容中提取标题
   */
  private static extractTitle(content: string): string {
    // 移除markdown标记和多余空白
    const cleanContent = content
      .replace(/^#+\s*/gm, '') // 移除markdown标题标记
      .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
      .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
      .replace(/`(.*?)`/g, '$1') // 移除代码标记
      .trim();

    // 取第一行或前50个字符作为标题
    const firstLine = cleanContent.split('\n')[0];
    const title = firstLine.length > 50 
      ? firstLine.substring(0, 47) + '...'
      : firstLine;

    return title || '无标题笔记';
  }

  /**
   * 转换笔记类型
   */
  private static transformNoteType(type: number): string {
    switch (type) {
      case NoteType.BLINKO:
        return 'Blinko';
      case NoteType.NOTE:
        return 'Note';
      case NoteType.TODO:
        return 'Todo';
      default:
        return 'Blinko';
    }
  }

  /**
   * 转换标签数组
   */
  private static transformTags(tags: any[]): Array<{ name: string }> {
    if (!tags || !Array.isArray(tags)) {
      return [];
    }

    return tags
      .filter(tagRelation => tagRelation.tag && tagRelation.tag.name)
      .map(tagRelation => {
        // 处理多级标签，将层级结构转换为扁平化标签
        const tagName = tagRelation.tag.name;
        // 如果标签名包含特殊字符，进行清理
        const cleanTagName = tagName.replace(/[^\w\s\u4e00-\u9fff-]/g, '').trim();
        return {
          name: cleanTagName || tagName, // 如果清理后为空，使用原名
        };
      })
      .filter(tag => tag.name.length > 0); // 过滤空标签
  }

  /**
   * 构建增量同步的过滤条件
   */
  static buildIncrementalFilter(lastSyncTime: Date) {
    return {
      where: {
        OR: [
          {
            updatedAt: {
              gt: lastSyncTime,
            },
          },
          {
            createdAt: {
              gt: lastSyncTime,
            },
          },
        ],
        isRecycle: false, // 不同步已删除的笔记
      },
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
        attachments: true,
      },
    };
  }

  /**
   * 验证笔记数据完整性
   */
  static validateNoteData(note: Note): boolean {
    return !!(
      note.id &&
      note.content &&
      note.createdAt &&
      note.updatedAt
    );
  }

  /**
   * 处理特殊字符和格式
   */
  static sanitizeContent(content: string): string {
    // 处理Notion不支持的特殊字符
    return content
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
      .substring(0, 2000); // Notion rich_text有长度限制
  }
}
