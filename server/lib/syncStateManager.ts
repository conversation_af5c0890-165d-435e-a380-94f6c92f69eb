import { prisma } from '../prisma';
import { getGlobalConfig } from '../routerTrpc/config';

export interface SyncState {
  lastSyncTime: Date;
  totalSynced: number;
  totalErrors: number;
  isRunning: boolean;
  lastError?: string;
}

export interface SyncResult {
  success: boolean;
  syncedCount: number;
  errorCount: number;
  errors: string[];
  duration: number;
}

export class SyncStateManager {
  private static readonly SYNC_STATE_KEY = 'notion_sync_state';
  private static readonly LAST_SYNC_TIME_KEY = 'notionLastSyncTime';

  /**
   * 获取上次同步时间
   */
  static async getLastSyncTime(): Promise<Date> {
    try {
      const config = await getGlobalConfig({ useAdmin: true });
      const lastSyncTime = config.notionLastSyncTime;
      
      if (lastSyncTime) {
        return new Date(lastSyncTime);
      }
      
      // 如果没有记录，返回一个很早的时间，确保首次同步包含所有数据
      return new Date('2020-01-01T00:00:00.000Z');
    } catch (error) {
      console.error('Failed to get last sync time:', error);
      return new Date('2020-01-01T00:00:00.000Z');
    }
  }

  /**
   * 更新同步时间
   */
  static async updateLastSyncTime(syncTime: Date): Promise<void> {
    try {
      await prisma.config.upsert({
        where: { key: this.LAST_SYNC_TIME_KEY },
        update: {
          config: {
            type: 'string',
            value: syncTime.toISOString(),
          },
        },
        create: {
          key: this.LAST_SYNC_TIME_KEY,
          config: {
            type: 'string',
            value: syncTime.toISOString(),
          },
        },
      });
    } catch (error) {
      console.error('Failed to update last sync time:', error);
      throw error;
    }
  }

  /**
   * 获取同步状态
   */
  static async getSyncState(): Promise<SyncState> {
    try {
      const stateConfig = await prisma.config.findFirst({
        where: { key: this.SYNC_STATE_KEY },
      });

      if (stateConfig && stateConfig.config) {
        const config = stateConfig.config as any;
        return {
          lastSyncTime: new Date(config.value.lastSyncTime),
          totalSynced: config.value.totalSynced || 0,
          totalErrors: config.value.totalErrors || 0,
          isRunning: config.value.isRunning || false,
          lastError: config.value.lastError,
        };
      }

      // 默认状态
      return {
        lastSyncTime: new Date('2020-01-01T00:00:00.000Z'),
        totalSynced: 0,
        totalErrors: 0,
        isRunning: false,
      };
    } catch (error) {
      console.error('Failed to get sync state:', error);
      throw error;
    }
  }

  /**
   * 更新同步状态
   */
  static async updateSyncState(state: Partial<SyncState>): Promise<void> {
    try {
      const currentState = await this.getSyncState();
      const newState = { ...currentState, ...state };

      await prisma.config.upsert({
        where: { key: this.SYNC_STATE_KEY },
        update: {
          config: {
            type: 'object',
            value: newState,
          },
        },
        create: {
          key: this.SYNC_STATE_KEY,
          config: {
            type: 'object',
            value: newState,
          },
        },
      });
    } catch (error) {
      console.error('Failed to update sync state:', error);
      throw error;
    }
  }

  /**
   * 标记同步开始
   */
  static async markSyncStart(): Promise<void> {
    await this.updateSyncState({
      isRunning: true,
      lastError: undefined,
    });
  }

  /**
   * 标记同步完成
   */
  static async markSyncComplete(result: SyncResult): Promise<void> {
    const currentState = await this.getSyncState();
    
    await this.updateSyncState({
      isRunning: false,
      lastSyncTime: new Date(),
      totalSynced: currentState.totalSynced + result.syncedCount,
      totalErrors: currentState.totalErrors + result.errorCount,
      lastError: result.errors.length > 0 ? result.errors[0] : undefined,
    });

    // 同时更新配置中的同步时间
    await this.updateLastSyncTime(new Date());
  }

  /**
   * 记录同步错误
   */
  static async recordSyncError(error: string): Promise<void> {
    await this.updateSyncState({
      isRunning: false,
      lastError: error,
    });
  }

  /**
   * 检查是否正在同步
   */
  static async isSyncRunning(): Promise<boolean> {
    const state = await this.getSyncState();
    return state.isRunning;
  }

  /**
   * 重置同步状态（用于调试或重新开始）
   */
  static async resetSyncState(): Promise<void> {
    await this.updateSyncState({
      lastSyncTime: new Date('2020-01-01T00:00:00.000Z'),
      totalSynced: 0,
      totalErrors: 0,
      isRunning: false,
      lastError: undefined,
    });
  }

  /**
   * 获取同步统计信息
   */
  static async getSyncStats(): Promise<{
    totalSynced: number;
    totalErrors: number;
    lastSyncTime: Date;
    successRate: number;
  }> {
    const state = await this.getSyncState();
    const total = state.totalSynced + state.totalErrors;
    const successRate = total > 0 ? (state.totalSynced / total) * 100 : 100;

    return {
      totalSynced: state.totalSynced,
      totalErrors: state.totalErrors,
      lastSyncTime: state.lastSyncTime,
      successRate: Math.round(successRate * 100) / 100,
    };
  }
}
