import { Client } from '@notionhq/client';
import { CreatePageParameters, QueryDatabaseParameters } from '@notionhq/client/build/src/api-endpoints';
import { getGlobalConfig } from '../routerTrpc/config';

export interface NotionPage {
  id: string;
  properties: any;
  created_time: string;
  last_edited_time: string;
}

export interface NotionDatabase {
  id: string;
  title: string;
  properties: any;
}

export class NotionService {
  private client: Client | null = null;
  // private databaseId: string | null = null; // 暂时不使用，避免警告

  constructor() {
    this.initializeClient();
  }

  private async initializeClient() {
    try {
      const config = await getGlobalConfig({ useAdmin: true });
      const apiToken = config.notionApiToken;

      if (apiToken) {
        this.client = new Client({
          auth: apiToken,
        });
        console.log('Notion client initialized successfully');
      }
    } catch (error) {
      console.error('Failed to initialize Notion client:', error);
    }
  }

  async ensureClient(): Promise<Client> {
    if (!this.client) {
      await this.initializeClient();
    }

    if (!this.client) {
      throw new Error('Notion client not initialized. Please check API token configuration.');
    }

    return this.client;
  }

  async testConnection(): Promise<boolean> {
    try {
      const client = await this.ensureClient();
      await client.users.me({});
      return true;
    } catch (error) {
      console.error('Notion connection test failed:', error);
      return false;
    }
  }

  async createDatabase(title: string, parentPageId?: string): Promise<string> {
    const client = await this.ensureClient();

    // 如果没有提供父页面ID，尝试获取用户的根页面
    let parent: any;
    if (parentPageId) {
      parent = {
        type: 'page_id' as const,
        page_id: parentPageId,
      };
    } else {
      // 获取用户的根页面或工作区
      try {
        const searchResponse = await client.search({
          filter: {
            value: 'page',
            property: 'object',
          },
          page_size: 1,
        });

        if (searchResponse.results.length > 0) {
          parent = {
            type: 'page_id' as const,
            page_id: searchResponse.results[0].id,
          };
        } else {
          throw new Error('No accessible pages found. Please provide a parent page ID.');
        }
      } catch (error) {
        throw new Error('Failed to find parent page. Please provide a parent page ID in configuration.');
      }
    }

    const response = await client.databases.create({
      parent,
      title: [
        {
          type: 'text',
          text: {
            content: title,
          },
        },
      ],
      properties: {
        'Title': {
          title: {},
        },
        'Content': {
          rich_text: {},
        },
        'Tags': {
          multi_select: {
            options: [],
          },
        },
        'Type': {
          select: {
            options: [
              { name: 'Blinko', color: 'blue' },
              { name: 'Note', color: 'green' },
              { name: 'Todo', color: 'yellow' },
            ],
          },
        },
        'Blinko ID': {
          number: {},
        },
        'Created At': {
          date: {},
        },
        'Updated At': {
          date: {},
        },
        'Is Archived': {
          checkbox: {},
        },
        'Is Top': {
          checkbox: {},
        },
        'Attachments': {
          files: {},
        },
      },
    });

    return response.id;
  }

  async queryDatabase(databaseId: string, filter?: any): Promise<NotionPage[]> {
    const client = await this.ensureClient();
    
    const queryParams: QueryDatabaseParameters = {
      database_id: databaseId,
    };
    
    if (filter) {
      queryParams.filter = filter;
    }

    const response = await client.databases.query(queryParams);
    return response.results as NotionPage[];
  }

  async createPage(databaseId: string, properties: any): Promise<string> {
    const client = await this.ensureClient();
    
    const createParams: CreatePageParameters = {
      parent: {
        database_id: databaseId,
      },
      properties,
    };

    const response = await client.pages.create(createParams);
    return response.id;
  }

  async updatePage(pageId: string, properties: any): Promise<void> {
    const client = await this.ensureClient();
    
    await client.pages.update({
      page_id: pageId,
      properties,
    });
  }

  async uploadFile(_file: any, _fileName: string): Promise<string> {
    // Notion API 不直接支持文件上传，需要先上传到外部存储
    // 这里返回一个占位符URL，实际实现需要配合文件存储服务
    throw new Error('File upload not implemented yet');
  }

  async findPageByBlinkoId(databaseId: string, blinkoId: number): Promise<NotionPage | null> {
    const pages = await this.queryDatabase(databaseId, {
      property: 'Blinko ID',
      number: {
        equals: blinkoId,
      },
    });

    return pages.length > 0 ? pages[0] : null;
  }
}
