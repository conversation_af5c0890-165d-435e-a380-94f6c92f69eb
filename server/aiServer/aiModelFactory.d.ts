import { LibSQLVector } from './vector';
import { Agent } from '@mastra/core';
import { LanguageModelV1, EmbeddingModelV1 } from '@ai-sdk/provider';
import { MarkdownTextSplitter, TokenTextSplitter } from '@langchain/textsplitters';
import { AiBaseModelProvider } from './providers';
export declare class AiModelFactory {
    #private;
    static queryAndDeleteVectorById(targetId: number): Promise<{
        success: boolean;
        deletedData: import("@libsql/client").Row;
        error?: undefined;
    } | {
        success: boolean;
        error: string;
        deletedData?: undefined;
    }>;
    static queryVector(query: string, accountId: number, _topK?: number): Promise<{
        notes: {
            score: number;
            attachments: {
                id: number;
                type: string;
                isShare: boolean;
                sharePassword: string;
                accountId: number | null;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                noteId: number | null;
                sortOrder: number;
                path: string;
                size: import("@prisma/client/runtime/library").Decimal;
                depth: number | null;
                perfixPath: string | null;
            }[];
            tags: ({
                tag: {
                    id: number;
                    accountId: number | null;
                    createdAt: Date;
                    updatedAt: Date;
                    name: string;
                    icon: string;
                    parent: number;
                    sortOrder: number;
                };
            } & {
                id: number;
                noteId: number;
                tagId: number;
            })[];
            referencedBy: {
                fromNoteId: number;
                fromNote: {
                    content: string;
                    createdAt: Date;
                    updatedAt: Date;
                };
            }[];
            references: {
                toNoteId: number;
                toNote: {
                    content: string;
                    createdAt: Date;
                    updatedAt: Date;
                };
            }[];
            _count: {
                comments: number;
                histories: number;
            };
            id: number;
            type: number;
            content: string;
            isArchived: boolean;
            isRecycle: boolean;
            isShare: boolean;
            isTop: boolean;
            isReviewed: boolean;
            sharePassword: string;
            shareEncryptedUrl: string | null;
            shareExpiryDate: Date | null;
            shareMaxView: number | null;
            shareViewCount: number | null;
            metadata: import("@prisma/client/runtime/library").JsonValue | null;
            accountId: number | null;
            createdAt: Date;
            updatedAt: Date;
        }[];
        aiContext: string[];
    }>;
    static rebuildVectorIndex({ vectorStore, isDelete }: {
        vectorStore: LibSQLVector;
        isDelete?: boolean;
    }): Promise<void>;
    static globalConfig(): Promise<{
        isAutoArchived?: boolean | undefined;
        autoArchivedDays?: number | undefined;
        isUseAI?: boolean | undefined;
        aiModelProvider?: any;
        aiApiKey?: any;
        aiApiEndpoint?: any;
        aiApiVersion?: any;
        aiModel?: any;
        isHiddenMobileBar?: boolean | undefined;
        toolbarVisibility?: any;
        isAllowRegister?: any;
        isCloseBackgroundAnimation?: boolean | undefined;
        customBackgroundUrl?: any;
        isOrderByCreateTime?: any;
        timeFormat?: any;
        smallDeviceCardColumns?: any;
        mediumDeviceCardColumns?: any;
        largeDeviceCardColumns?: any;
        textFoldLength?: number | undefined;
        objectStorage?: any;
        s3AccessKeyId?: any;
        s3AccessKeySecret?: any;
        s3Endpoint?: any;
        s3Bucket?: any;
        s3CustomPath?: any;
        s3Region?: any;
        localCustomPath?: any;
        embeddingModel?: any;
        embeddingDimensions?: number | undefined;
        embeddingTopK?: number | undefined;
        embeddingLambda?: number | undefined;
        embeddingScore?: number | undefined;
        excludeEmbeddingTagId?: number | undefined;
        language?: any;
        theme?: any;
        themeColor?: any;
        themeForegroundColor?: any;
        webhookEndpoint?: any;
        twoFactorEnabled?: boolean | undefined;
        twoFactorSecret?: string | undefined;
        spotifyConsumerKey?: string | undefined;
        spotifyConsumerSecret?: string | undefined;
        isCloseDailyReview?: boolean | undefined;
        maxHomePageWidth?: number | undefined;
        oauth2Providers?: {
            id: string;
            name: string;
            tokenUrl: string;
            userinfoUrl: string;
            clientId: string;
            clientSecret: string;
            icon?: string | undefined;
            wellKnown?: string | undefined;
            scope?: string | undefined;
            authorizationUrl?: string | undefined;
        }[] | undefined;
        isUseBlinkoHub?: boolean | undefined;
        embeddingApiEndpoint?: string | undefined;
        embeddingApiKey?: string | undefined;
        isHiddenNotification?: boolean | undefined;
        tavilyApiKey?: any;
        tavilyMaxResult?: any;
        isHideBlogImages?: boolean | undefined;
        isUseAiPostProcessing?: boolean | undefined;
        aiCommentPrompt?: string | undefined;
        aiTagsPrompt?: string | undefined;
        aiPostProcessingMode?: string | undefined;
        isUseHttpProxy?: boolean | undefined;
        httpProxyHost?: string | undefined;
        httpProxyPort?: number | undefined;
        httpProxyUsername?: string | undefined;
        httpProxyPassword?: string | undefined;
        aiSmartEditPrompt?: string | undefined;
        rerankModel?: string | undefined;
        rerankTopK?: number | undefined;
        rerankScore?: number | undefined;
        rerankUseEembbingEndpoint?: boolean | undefined;
    }>;
    static ValidConfig(): Promise<{
        isAutoArchived?: boolean | undefined;
        autoArchivedDays?: number | undefined;
        isUseAI?: boolean | undefined;
        aiModelProvider?: any;
        aiApiKey?: any;
        aiApiEndpoint?: any;
        aiApiVersion?: any;
        aiModel?: any;
        isHiddenMobileBar?: boolean | undefined;
        toolbarVisibility?: any;
        isAllowRegister?: any;
        isCloseBackgroundAnimation?: boolean | undefined;
        customBackgroundUrl?: any;
        isOrderByCreateTime?: any;
        timeFormat?: any;
        smallDeviceCardColumns?: any;
        mediumDeviceCardColumns?: any;
        largeDeviceCardColumns?: any;
        textFoldLength?: number | undefined;
        objectStorage?: any;
        s3AccessKeyId?: any;
        s3AccessKeySecret?: any;
        s3Endpoint?: any;
        s3Bucket?: any;
        s3CustomPath?: any;
        s3Region?: any;
        localCustomPath?: any;
        embeddingModel?: any;
        embeddingDimensions?: number | undefined;
        embeddingTopK?: number | undefined;
        embeddingLambda?: number | undefined;
        embeddingScore?: number | undefined;
        excludeEmbeddingTagId?: number | undefined;
        language?: any;
        theme?: any;
        themeColor?: any;
        themeForegroundColor?: any;
        webhookEndpoint?: any;
        twoFactorEnabled?: boolean | undefined;
        twoFactorSecret?: string | undefined;
        spotifyConsumerKey?: string | undefined;
        spotifyConsumerSecret?: string | undefined;
        isCloseDailyReview?: boolean | undefined;
        maxHomePageWidth?: number | undefined;
        oauth2Providers?: {
            id: string;
            name: string;
            tokenUrl: string;
            userinfoUrl: string;
            clientId: string;
            clientSecret: string;
            icon?: string | undefined;
            wellKnown?: string | undefined;
            scope?: string | undefined;
            authorizationUrl?: string | undefined;
        }[] | undefined;
        isUseBlinkoHub?: boolean | undefined;
        embeddingApiEndpoint?: string | undefined;
        embeddingApiKey?: string | undefined;
        isHiddenNotification?: boolean | undefined;
        tavilyApiKey?: any;
        tavilyMaxResult?: any;
        isHideBlogImages?: boolean | undefined;
        isUseAiPostProcessing?: boolean | undefined;
        aiCommentPrompt?: string | undefined;
        aiTagsPrompt?: string | undefined;
        aiPostProcessingMode?: string | undefined;
        isUseHttpProxy?: boolean | undefined;
        httpProxyHost?: string | undefined;
        httpProxyPort?: number | undefined;
        httpProxyUsername?: string | undefined;
        httpProxyPassword?: string | undefined;
        aiSmartEditPrompt?: string | undefined;
        rerankModel?: string | undefined;
        rerankTopK?: number | undefined;
        rerankScore?: number | undefined;
        rerankUseEembbingEndpoint?: boolean | undefined;
    }>;
    static GetProvider(): Promise<{
        LLM: LanguageModelV1;
        VectorStore: LibSQLVector;
        Embeddings: EmbeddingModelV1<string>;
        MarkdownSplitter: MarkdownTextSplitter;
        TokenTextSplitter: TokenTextSplitter;
        provider: AiBaseModelProvider;
    }>;
    static BaseChatAgent({ withTools, withOnlineSearch }: {
        withTools?: boolean;
        withOnlineSearch?: boolean;
    }): Promise<Agent<"Blinko Chat Agent", Record<string, import("@mastra/core").ToolAction<any, any, any>>, Record<string, import("@mastra/core").Metric>>>;
    static TagAgent: (type?: "expand" | "polish" | "custom" | string) => Promise<Agent<string, Record<string, import("@mastra/core").ToolAction<any, any, any>>, Record<string, import("@mastra/core").Metric>>>;
    static EmojiAgent: (type?: "expand" | "polish" | "custom" | string) => Promise<Agent<string, Record<string, import("@mastra/core").ToolAction<any, any, any>>, Record<string, import("@mastra/core").Metric>>>;
    static RelatedNotesAgent: (type?: "expand" | "polish" | "custom" | string) => Promise<Agent<string, Record<string, import("@mastra/core").ToolAction<any, any, any>>, Record<string, import("@mastra/core").Metric>>>;
    static CommentAgent: (type?: "expand" | "polish" | "custom" | string) => Promise<Agent<string, Record<string, import("@mastra/core").ToolAction<any, any, any>>, Record<string, import("@mastra/core").Metric>>>;
    static SummarizeAgent: (type?: "expand" | "polish" | "custom" | string) => Promise<Agent<string, Record<string, import("@mastra/core").ToolAction<any, any, any>>, Record<string, import("@mastra/core").Metric>>>;
    static WritingAgent: (type?: "expand" | "polish" | "custom" | string) => Promise<Agent<string, Record<string, import("@mastra/core").ToolAction<any, any, any>>, Record<string, import("@mastra/core").Metric>>>;
    static TestConnectAgent: (type?: "expand" | "polish" | "custom" | string) => Promise<Agent<string, Record<string, import("@mastra/core").ToolAction<any, any, any>>, Record<string, import("@mastra/core").Metric>>>;
}
