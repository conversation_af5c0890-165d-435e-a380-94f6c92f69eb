{"name": "@blinko/backend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "bun --env-file ../.env --watch index.ts", "build:web": "bun esbuild.config.ts", "start": "NODE_ENV=production dotenv -e ../.env -- bun --env ../dist/index.js"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/azure": "^1.3.13", "@ai-sdk/deepseek": "^0.2.11", "@ai-sdk/google": "^1.2.11", "@ai-sdk/openai": "^1.3.12", "@ai-sdk/xai": "^1.2.13", "@auth/express": "^0.9.0", "@aws-sdk/client-s3": "3.693.0", "@aws-sdk/lib-storage": "3.693.0", "@aws-sdk/s3-request-presigner": "3.693.0", "@langchain/community": "^0.3.40", "@libsql/client": "^0.15.4", "@libsql/core": "^0.15.4", "@libsql/linux-arm64-gnu": "0.5.8", "@libsql/linux-arm64-musl": "0.5.8", "@libsql/linux-x64-gnu": "0.5.8", "@libsql/linux-x64-musl": "0.5.8", "@mastra/core": "^0.8.2", "@mastra/rag": "^0.1.17", "@openrouter/ai-sdk-provider": "^0.4.5", "@prisma/client": "^5.22.0", "@prisma/engines": "^6.5.0", "@tavily/core": "^0.3.7", "@trpc/server": "11.1.0", "@types/express": "^5.0.1", "adm-zip": "^0.5.16", "archiver": "^7.0.1", "axios": "^1.6.7", "bowser": "^2.11.0", "busboy": "^1.6.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^4.1.4", "dotenv": "^16.4.5", "express": "^5.1.0", "express-list-endpoints": "^7.1.1", "express-session": "^1.18.1", "feed": "^4.2.2", "file-type": "^20.4.1", "jsonwebtoken": "^9.0.2", "music-metadata": "^11.2.0", "ncp": "^2.0.0", "ollama-ai-provider": "^1.2.0", "otplib": "^12.0.1", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-discord": "^0.1.4", "passport-facebook": "^3.0.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-line": "^0.0.4", "passport-local": "^1.0.0", "passport-oauth2": "^1.8.0", "passport-slack": "^0.0.7", "passport-spotify": "^2.0.0", "passport-twitch-new": "^0.0.3", "passport-twitter": "^1.0.4", "pdf-parse": "^1.1.1", "pgvector": "^0.2.0", "pino-pretty": "^10.3.1", "proxy-agent": "^6.5.0", "request-ip": "^3.3.0", "sharp": "0.34.1", "sqlite3": "^5.1.7", "superjson": "^2.2.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "trpc-to-openapi": "^2.0.2", "unfurl.js": "^6.4.0", "vite-express": "^0.20.0", "voyage-ai-provider": "^1.0.1", "ws": "^8.14.2", "yauzl-promise": "^4.0.0", "zod": "^3.23.8"}, "resolutions": {"@langchain/core": "^0.3.40", "sharp": "0.34.1"}, "devDependencies": {"@types/adm-zip": "^0.5.7", "@types/archiver": "^6.0.3", "@types/bowser": "^1.1.5", "@types/cookie-parser": "^1.4.8", "@types/express-session": "^1.18.1", "@types/jsonwebtoken": "^9.0.5", "@types/node": "20.2.5", "@types/passport": "^1.0.17", "@types/passport-facebook": "^3.0.3", "@types/passport-github2": "^1.2.9", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-twitter": "^1.0.40", "@types/request-ip": "^0.0.41", "@types/ws": "^8.5.10", "@types/yauzl-promise": "^4.0.1", "bun-types": "latest", "esbuild": "^0.25.3", "esbuild-node-externals": "^1.18.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.2.5", "prisma": "5.21.1", "tsup": "^8.4.0", "typescript": "^5.1.6"}}