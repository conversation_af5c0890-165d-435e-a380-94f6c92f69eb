import { router, authProcedure, demoAuthMiddleware, superAdminAuthMiddleware } from '@server/middleware';
import { z } from 'zod';
import { prisma } from '@server/prisma';
import { DBJob } from '@server/jobs/dbjob';
import '@server/jobs/recommandJob';
import { ArchiveJob } from '@server/jobs/archivejob';
import { UPLOAD_FILE_PATH } from '@shared/lib/pathConstant';
import { ARCHIVE_BLINKO_TASK_NAME, DBBAK_TASK_NAME, NOTION_SYNC_TASK_NAME } from '@shared/lib/sharedConstant';
import { scheduledTaskSchema } from '@shared/lib/prismaZodType';
import { Memos } from '../jobs/memosJob';
import { unlink } from 'fs/promises';
import { FileService } from '../lib/files';
import path from 'path';
import fs from 'fs';
import { MarkdownImporter } from '../jobs/markdownJob';
import { NotionSyncJob } from '../jobs/notionSyncJob';


export const taskRouter = router({
  list: authProcedure.use(superAdminAuthMiddleware)
    .meta({ openapi: { method: 'GET', path: '/v1/tasks/list', summary: 'Query user task list', protect: true, tags: ['Task'] } })
    .input(z.void())
    .output(z.array(scheduledTaskSchema))
    .query(async () => {
      return await prisma.scheduledTask.findMany()
    }),
  upsertTask: authProcedure.use(superAdminAuthMiddleware)
    .meta({ openapi: { method: 'GET', path: '/v1/tasks/upsert', summary: 'Upsert Task', protect: true, tags: ['Task'] } })
    .input(z.object({
      time: z.string().optional(),
      type: z.enum(['start', 'stop', 'update']),
      task: z.enum([ARCHIVE_BLINKO_TASK_NAME, DBBAK_TASK_NAME, NOTION_SYNC_TASK_NAME]),
    }))
    .output(z.any())
    .mutation(async ({ input }) => {
      const { time, type, task } = input
      if (type == 'start') {
        const cronTime = time ?? '0 0 * * *'
        if (task == DBBAK_TASK_NAME) {
          return await DBJob.Start(cronTime, true)
        } else if (task == ARCHIVE_BLINKO_TASK_NAME) {
          return await ArchiveJob.Start(cronTime, true)
        } else if (task == NOTION_SYNC_TASK_NAME) {
          return await NotionSyncJob.Start(cronTime, true)
        }
      }
      if (type == 'stop') {
        if (task == DBBAK_TASK_NAME) {
          return await DBJob.Stop()
        } else if (task == ARCHIVE_BLINKO_TASK_NAME) {
          return await ArchiveJob.Stop()
        } else if (task == NOTION_SYNC_TASK_NAME) {
          return await NotionSyncJob.Stop()
        }
      }
      if (type == 'update' && time) {
        if (task == DBBAK_TASK_NAME) {
          return await DBJob.SetCronTime(time)
        } else if (task == ARCHIVE_BLINKO_TASK_NAME) {
          return await ArchiveJob.SetCronTime(time)
        } else if (task == NOTION_SYNC_TASK_NAME) {
          return await NotionSyncJob.SetCronTime(time)
        }
      }
    }),
  importFromBlinko: authProcedure.use(demoAuthMiddleware).use(superAdminAuthMiddleware)
    .input(z.object({
      filePath: z.string()
    }))
    .mutation(async function* ({ input, ctx }) {
      const { filePath } = input
      try {
        const localFilePath = await FileService.getFile(filePath)
        const res = DBJob.RestoreDB(localFilePath, ctx)
        for await (const result of res) {
          yield result;
        }
        try {
          await unlink(localFilePath)
          await FileService.deleteFile(filePath)
        } catch (error) {
        }
      } catch (error) {
        throw new Error(error)
      }
    }),

  importFromMemos: authProcedure.use(demoAuthMiddleware).use(superAdminAuthMiddleware)
    .input(z.object({
      filePath: z.string() //xxxx.db
    }))
    .mutation(async function* ({ input, ctx }) {
      try {
        const memos = new Memos();
        const dbPath = await memos.initDB(input.filePath);
        for await (const result of memos.importMemosDB(ctx)) {
          yield result;
        }
        for await (const result of memos.importFiles(ctx)) {
          yield result;
        }
        memos.closeDB();
        try {
          await unlink(dbPath)
          await FileService.deleteFile(input.filePath)
        } catch (error) {
        }
      } catch (error) {
        throw new Error(error)
      }
    }),

  importFromMarkdown: authProcedure.use(demoAuthMiddleware)
    .input(z.object({
      filePath: z.string() // Path to .md file or .zip containing md files
    }))
    .mutation(async function* ({ input, ctx }) {
      try {
        const localFilePath = await FileService.getFile(input.filePath);
        const markdownImporter = new MarkdownImporter();
        
        for await (const result of markdownImporter.importMarkdown(localFilePath, ctx)) {
          yield result;
        }
        
        // Clean up the file after import
        try {
          await unlink(localFilePath);
          await FileService.deleteFile(input.filePath);
        } catch (error) {
          console.error("Failed to clean up files after markdown import:", error);
        }
      } catch (error) {
        console.error("Error in importFromMarkdown:", error);
        throw new Error(error);
      }
    }),

  exportMarkdown: authProcedure
    .input(z.object({
      format: z.enum(['markdown', 'csv', 'json']),
      baseURL: z.string(),
      startDate: z.date().optional(),
      endDate: z.date().optional(),
    })).output(z.object({
      success: z.boolean(),
      downloadUrl: z.string().optional(),
      fileCount: z.number().optional(),
      error: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const result = await DBJob.ExporMDFiles({ ...input, ctx });
      setTimeout(async () => {
        try {
          const zipPath = path.join(UPLOAD_FILE_PATH, result.path);
          if (fs.existsSync(zipPath)) {
            await unlink(zipPath);
          }
        } catch (error) {
          console.warn('Failed to cleanup export zip file:', error);
        }
      }, 5 * 60 * 1000);
      return {
        success: true,
        downloadUrl: `/api/file${result.path}`,
        fileCount: result.fileCount
      };
    }),
})
