{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "Node", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": false, "skipLibCheck": true, "sourceMap": true, "outDir": "../dist", "rootDir": "..", "baseUrl": ".", "resolveJsonModule": true, "useUnknownInCatchVariables": false, "paths": {"@server/*": ["./*"], "@prisma/*": ["../prisma/*"], "@shared/*": ["../shared/*"]}, "types": ["bun-types"]}, "include": ["./**/*", "../shared/**/*", "../dist"], "exclude": ["node_modules", "../dist"]}