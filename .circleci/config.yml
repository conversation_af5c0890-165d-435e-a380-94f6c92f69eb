version: 2.1

jobs:
  build-and-push:
    machine:
      image: ubuntu-2204:2024.01.1
      docker_layer_caching: true
    steps:
      - checkout
      
      - run:
          name: Install Docker Buildx
          command: |
            docker version
            docker buildx version
            docker buildx create --use
            docker buildx inspect --bootstrap
      
      - run:
          name: Login to Docker Hub
          command: echo "$DOCKERHUB_PASS" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin
      
      - run:
          name: Build and Push Multi-arch Image
          command: |
            VERSION=$(date +%Y%m%d)-${CIRCLE_SHA1:0:7}
            
            docker buildx build \
              --platform linux/amd64,linux/arm64 \
              --tag dlhtx/blinko:latest \
              --tag dlhtx/blinko:$VERSION \
              --push \
              .

workflows:
  version: 2
  build-deploy:
    jobs:
      - build-and-push:
          context:
            - docker-hub-creds