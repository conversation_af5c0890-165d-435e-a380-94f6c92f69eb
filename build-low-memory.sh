#!/bin/bash

echo "🔧 低内存服务器构建脚本"
echo "========================"

# 检查内存
echo "📊 当前内存状态："
free -h

# 停止不必要的服务释放内存
echo "🛑 停止 Docker 服务释放内存..."
docker-compose down

echo "🧹 清理系统缓存..."
sync
echo 3 > /proc/sys/vm/drop_caches

echo "📊 清理后内存状态："
free -h

# 设置环境变量限制内存使用
export NODE_OPTIONS="--max-old-space-size=1024"
export VITE_BUILD_MEMORY_LIMIT=512

echo "🔨 开始构建（限制内存使用）..."

# 分步构建，避免内存峰值
echo "📦 Step 1: 安装依赖..."
if ! bun install; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "🔄 清理缓存..."
sync
echo 3 > /proc/sys/vm/drop_caches

echo "🏗️ Step 2: 生成 Prisma..."
if ! bunx prisma generate; then
    echo "❌ Prisma 生成失败"
    exit 1
fi

echo "🔄 清理缓存..."
sync
echo 3 > /proc/sys/vm/drop_caches

echo "🎯 Step 3: 构建前端（低内存模式）..."
# 使用更保守的内存设置
export NODE_OPTIONS="--max-old-space-size=768"

if ! timeout 600 bun run build:web; then
    echo "❌ 前端构建失败或超时"
    echo "💡 建议使用方案1：在其他机器构建镜像"
    exit 1
fi

echo "🔄 清理缓存..."
sync
echo 3 > /proc/sys/vm/drop_caches

echo "🌱 Step 4: 构建种子文件..."
if ! bun run build:seed; then
    echo "❌ 种子文件构建失败"
    exit 1
fi

echo "✅ 构建完成！"
echo ""
echo "🐳 现在可以构建 Docker 镜像："
echo "   docker build -t blinko-with-notion:latest ."
echo ""
echo "或者直接启动测试："
echo "   bun start"

# 恢复 Docker 服务
echo "🔄 恢复 Docker 服务..."
docker-compose up -d
