# Blinko Notion 同步功能

## 概述

Blinko Notion 同步功能允许您将 Blinko 中的笔记自动备份到 Notion，确保数据安全并提供额外的访问方式。

## 功能特点

- ✅ **增量同步**：只同步新增或修改的笔记，提高效率
- ✅ **自动备份**：每日定时执行，无需手动干预
- ✅ **错误恢复**：具备重试机制和详细错误日志
- ✅ **状态监控**：实时查看同步状态和统计信息
- ✅ **手动触发**：支持立即执行同步任务
- ✅ **数据完整性**：保持标签、附件等完整信息

## 安装和配置

### 1. 安装依赖

```bash
npm install @notionhq/client --legacy-peer-deps
```

### 2. 获取 Notion API Token

1. 访问 [Notion Integrations](https://www.notion.so/my-integrations)
2. 点击 "New integration"
3. 填写集成信息并创建
4. 复制生成的 API Token

### 3. 配置 Blinko

1. 登录 Blinko 管理界面
2. 进入 "设置" → "任务管理"
3. 找到 "Notion 同步设置" 部分
4. 输入 Notion API Token
5. 点击 "测试连接" 验证配置
6. 启用 "Notion 同步" 开关

## 使用方法

### 自动同步

系统默认每天凌晨执行同步任务，您可以在任务管理中修改执行时间：

1. 进入 "设置" → "任务管理"
2. 找到 "Notion Sync" 任务
3. 修改 Cron 表达式（默认：`0 0 * * *`）

### 手动同步

在 Notion 同步设置中点击 "手动同步" 按钮即可立即执行同步。

### 查看同步状态

同步设置界面会显示：
- 已同步笔记数量
- 错误数量和成功率
- 上次同步时间
- 当前运行状态

## 数据映射

Blinko 笔记在 Notion 中的映射关系：

| Blinko 字段 | Notion 属性 | 类型 | 说明 |
|------------|------------|------|------|
| 笔记内容 | Title | 标题 | 取内容前50字符 |
| 完整内容 | Content | 富文本 | 完整笔记内容 |
| 标签 | Tags | 多选 | 所有关联标签 |
| 笔记类型 | Type | 选择 | Blinko/Note/Todo |
| 笔记ID | Blinko ID | 数字 | 用于去重和更新 |
| 创建时间 | Created At | 日期 | 原始创建时间 |
| 更新时间 | Updated At | 日期 | 最后修改时间 |
| 是否归档 | Is Archived | 复选框 | 归档状态 |
| 是否置顶 | Is Top | 复选框 | 置顶状态 |

## 技术架构

### 核心组件

1. **NotionSyncJob** - 定时同步任务
   - 继承 `BaseScheduleJob`
   - 支持 Cron 表达式调度
   - 错误处理和重试机制

2. **NotionService** - Notion API 封装
   - 客户端初始化和连接管理
   - 数据库和页面操作
   - API 限制处理

3. **DataTransformer** - 数据转换器
   - Blinko 到 Notion 格式转换
   - 数据验证和清理
   - 增量同步过滤

4. **SyncStateManager** - 状态管理
   - 同步时间戳追踪
   - 统计信息记录
   - 错误状态管理

### 同步流程

```mermaid
graph TD
    A[开始同步] --> B[检查配置]
    B --> C[连接 Notion API]
    C --> D[获取上次同步时间]
    D --> E[查询增量数据]
    E --> F[批量处理笔记]
    F --> G[转换数据格式]
    G --> H[上传到 Notion]
    H --> I[更新同步状态]
    I --> J[完成同步]
    
    F --> K[错误处理]
    K --> L[重试机制]
    L --> F
```

### 错误处理

- **API 限制**：指数退避重试策略
- **网络错误**：最多重试 3 次
- **数据错误**：跳过无效数据，继续处理
- **批量失败**：错误超过 10 个时停止同步

## 配置选项

### 环境变量

```bash
# Notion API 配置
NOTION_API_TOKEN=your_notion_api_token
NOTION_DATABASE_ID=your_database_id  # 可选，系统会自动创建

# 同步配置
NOTION_SYNC_ENABLED=true
NOTION_SYNC_SCHEDULE="0 0 * * *"  # 每天凌晨执行
```

### 数据库配置

系统会在 `config` 表中存储以下配置：

- `notionApiToken` - Notion API 密钥
- `notionDatabaseId` - 目标数据库 ID
- `notionSyncEnabled` - 同步开关
- `notionLastSyncTime` - 上次同步时间

## 故障排除

### 常见问题

1. **连接测试失败**
   - 检查 API Token 是否正确
   - 确认网络连接正常
   - 验证 Notion 集成权限

2. **同步失败**
   - 查看错误日志
   - 检查数据库连接
   - 确认 Notion 数据库权限

3. **数据不完整**
   - 检查标签映射
   - 验证字符编码
   - 确认数据格式

### 日志查看

同步日志会记录在系统日志中，包括：
- 同步开始和结束时间
- 处理的笔记数量
- 错误信息和重试次数
- 性能统计信息

## API 接口

### 手动同步

```typescript
POST /api/trpc/task.manualNotionSync
```

### 获取同步状态

```typescript
GET /api/trpc/task.getNotionSyncStatus
```

### 获取同步统计

```typescript
GET /api/trpc/task.getNotionSyncStats
```

## 开发指南

### 添加新功能

1. 扩展 `NotionService` 类
2. 更新 `DataTransformer` 映射
3. 修改前端配置界面
4. 添加相应测试

### 测试

```bash
# 运行单元测试
npm test server/lib/notionService.test.ts

# 运行集成测试
npm test -- --testPathPattern=notion
```

### 调试

启用调试模式：

```bash
DEBUG=notion:* npm start
```

## 安全考虑

- API Token 加密存储
- 数据传输使用 HTTPS
- 定期轮换访问密钥
- 最小权限原则

## 性能优化

- 批量处理（每批 5 个笔记）
- 并发控制避免 API 限制
- 增量同步减少数据传输
- 缓存机制提高响应速度

## 更新日志

### v1.0.0 (2025-01-17)
- 初始版本发布
- 支持基础同步功能
- 增量同步和错误处理
- 前端配置界面

## 支持

如有问题或建议，请：
1. 查看本文档的故障排除部分
2. 检查系统日志
3. 提交 GitHub Issue
4. 联系技术支持

---

**注意**：首次同步可能需要较长时间，建议在低峰期执行。
