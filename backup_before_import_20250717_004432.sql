--
-- PostgreSQL database dump
--

-- Dumped from database version 14.18 (Debian 14.18-1.pgdg120+1)
-- Dumped by pg_dump version 14.18 (Debian 14.18-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO postgres;

--
-- Name: accounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accounts (
    id integer NOT NULL,
    name character varying DEFAULT ''::character varying NOT NULL,
    nickname character varying DEFAULT ''::character varying NOT NULL,
    password character varying DEFAULT ''::character varying NOT NULL,
    image character varying DEFAULT ''::character varying NOT NULL,
    "apiToken" character varying DEFAULT ''::character varying NOT NULL,
    note integer DEFAULT 0 NOT NULL,
    role character varying DEFAULT ''::character varying NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "loginType" character varying DEFAULT ''::character varying NOT NULL,
    "linkAccountId" integer,
    description text DEFAULT ''::text NOT NULL
);


ALTER TABLE public.accounts OWNER TO postgres;

--
-- Name: accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accounts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.accounts_id_seq OWNER TO postgres;

--
-- Name: accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accounts_id_seq OWNED BY public.accounts.id;


--
-- Name: attachments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.attachments (
    id integer NOT NULL,
    "isShare" boolean DEFAULT false NOT NULL,
    "sharePassword" character varying DEFAULT ''::character varying NOT NULL,
    name character varying DEFAULT ''::character varying NOT NULL,
    path character varying DEFAULT ''::character varying NOT NULL,
    size numeric DEFAULT 0 NOT NULL,
    "noteId" integer,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    type character varying DEFAULT ''::character varying NOT NULL,
    "sortOrder" integer DEFAULT 0 NOT NULL,
    depth integer,
    "perfixPath" character varying DEFAULT ''::character varying,
    "accountId" integer
);


ALTER TABLE public.attachments OWNER TO postgres;

--
-- Name: attachments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.attachments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.attachments_id_seq OWNER TO postgres;

--
-- Name: attachments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.attachments_id_seq OWNED BY public.attachments.id;


--
-- Name: cache; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cache (
    id integer NOT NULL,
    key character varying NOT NULL,
    value json NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.cache OWNER TO postgres;

--
-- Name: cache_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cache_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.cache_id_seq OWNER TO postgres;

--
-- Name: cache_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cache_id_seq OWNED BY public.cache.id;


--
-- Name: comments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.comments (
    id integer NOT NULL,
    content text NOT NULL,
    "accountId" integer,
    "guestName" character varying,
    "guestIP" character varying,
    "guestUA" character varying,
    "noteId" integer NOT NULL,
    "parentId" integer,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.comments OWNER TO postgres;

--
-- Name: comments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.comments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.comments_id_seq OWNER TO postgres;

--
-- Name: comments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.comments_id_seq OWNED BY public.comments.id;


--
-- Name: config; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.config (
    id integer NOT NULL,
    key character varying DEFAULT ''::character varying NOT NULL,
    config json,
    "userId" integer
);


ALTER TABLE public.config OWNER TO postgres;

--
-- Name: config_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.config_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.config_id_seq OWNER TO postgres;

--
-- Name: config_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.config_id_seq OWNED BY public.config.id;


--
-- Name: conversation; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.conversation (
    id integer NOT NULL,
    title character varying(255) DEFAULT ''::character varying NOT NULL,
    "accountId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "isShare" boolean DEFAULT false NOT NULL
);


ALTER TABLE public.conversation OWNER TO postgres;

--
-- Name: conversation_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.conversation_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.conversation_id_seq OWNER TO postgres;

--
-- Name: conversation_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.conversation_id_seq OWNED BY public.conversation.id;


--
-- Name: follows; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.follows (
    id integer NOT NULL,
    "siteName" character varying,
    "siteUrl" character varying NOT NULL,
    "siteAvatar" character varying,
    description text,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "followType" character varying DEFAULT 'following'::character varying NOT NULL,
    "accountId" integer NOT NULL
);


ALTER TABLE public.follows OWNER TO postgres;

--
-- Name: follows_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.follows_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.follows_id_seq OWNER TO postgres;

--
-- Name: follows_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.follows_id_seq OWNED BY public.follows.id;


--
-- Name: message; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.message (
    id integer NOT NULL,
    content text NOT NULL,
    role character varying(50) NOT NULL,
    "conversationId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    metadata json
);


ALTER TABLE public.message OWNER TO postgres;

--
-- Name: message_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.message_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.message_id_seq OWNER TO postgres;

--
-- Name: message_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.message_id_seq OWNED BY public.message.id;


--
-- Name: noteHistory; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."noteHistory" (
    id integer NOT NULL,
    "noteId" integer NOT NULL,
    content text NOT NULL,
    metadata json,
    version integer NOT NULL,
    "accountId" integer,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."noteHistory" OWNER TO postgres;

--
-- Name: noteHistory_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."noteHistory_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."noteHistory_id_seq" OWNER TO postgres;

--
-- Name: noteHistory_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."noteHistory_id_seq" OWNED BY public."noteHistory".id;


--
-- Name: noteInternalShare; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."noteInternalShare" (
    id integer NOT NULL,
    "noteId" integer NOT NULL,
    "accountId" integer NOT NULL,
    "canEdit" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public."noteInternalShare" OWNER TO postgres;

--
-- Name: noteInternalShare_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."noteInternalShare_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."noteInternalShare_id_seq" OWNER TO postgres;

--
-- Name: noteInternalShare_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."noteInternalShare_id_seq" OWNED BY public."noteInternalShare".id;


--
-- Name: noteReference; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."noteReference" (
    id integer NOT NULL,
    "fromNoteId" integer NOT NULL,
    "toNoteId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."noteReference" OWNER TO postgres;

--
-- Name: noteReference_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."noteReference_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."noteReference_id_seq" OWNER TO postgres;

--
-- Name: noteReference_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."noteReference_id_seq" OWNED BY public."noteReference".id;


--
-- Name: notes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notes (
    id integer NOT NULL,
    type integer DEFAULT 0 NOT NULL,
    content character varying DEFAULT ''::character varying NOT NULL,
    "isArchived" boolean DEFAULT false NOT NULL,
    "isRecycle" boolean DEFAULT false NOT NULL,
    "isShare" boolean DEFAULT false NOT NULL,
    "isTop" boolean DEFAULT false NOT NULL,
    "sharePassword" character varying DEFAULT ''::character varying NOT NULL,
    metadata json,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "isReviewed" boolean DEFAULT false NOT NULL,
    "accountId" integer,
    "shareEncryptedUrl" character varying,
    "shareExpiryDate" timestamp(6) with time zone,
    "shareMaxView" integer DEFAULT 0,
    "shareViewCount" integer DEFAULT 0
);


ALTER TABLE public.notes OWNER TO postgres;

--
-- Name: notes_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.notes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.notes_id_seq OWNER TO postgres;

--
-- Name: notes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.notes_id_seq OWNED BY public.notes.id;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notifications (
    id integer NOT NULL,
    type character varying NOT NULL,
    title character varying NOT NULL,
    content text NOT NULL,
    metadata json,
    "isRead" boolean DEFAULT false NOT NULL,
    "accountId" integer NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.notifications OWNER TO postgres;

--
-- Name: notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.notifications_id_seq OWNER TO postgres;

--
-- Name: notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.notifications_id_seq OWNED BY public.notifications.id;


--
-- Name: plugin; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.plugin (
    id integer NOT NULL,
    metadata json NOT NULL,
    path character varying NOT NULL,
    "isUse" boolean DEFAULT true NOT NULL,
    "isDev" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.plugin OWNER TO postgres;

--
-- Name: plugin_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.plugin_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.plugin_id_seq OWNER TO postgres;

--
-- Name: plugin_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.plugin_id_seq OWNED BY public.plugin.id;


--
-- Name: scheduledTask; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."scheduledTask" (
    name text NOT NULL,
    schedule text NOT NULL,
    "lastRun" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "isSuccess" boolean DEFAULT true NOT NULL,
    "isRunning" boolean DEFAULT false NOT NULL,
    output json
);


ALTER TABLE public."scheduledTask" OWNER TO postgres;

--
-- Name: session; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.session (
    id text NOT NULL,
    sid text NOT NULL,
    data text NOT NULL,
    "expiresAt" timestamp(3) without time zone NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.session OWNER TO postgres;

--
-- Name: tag; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tag (
    id integer NOT NULL,
    name character varying DEFAULT ''::character varying NOT NULL,
    icon character varying DEFAULT ''::character varying NOT NULL,
    parent integer DEFAULT 0 NOT NULL,
    "createdAt" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(6) with time zone NOT NULL,
    "accountId" integer,
    "sortOrder" integer DEFAULT 0 NOT NULL
);


ALTER TABLE public.tag OWNER TO postgres;

--
-- Name: tag_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tag_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.tag_id_seq OWNER TO postgres;

--
-- Name: tag_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.tag_id_seq OWNED BY public.tag.id;


--
-- Name: tagsToNote; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."tagsToNote" (
    id integer NOT NULL,
    "noteId" integer DEFAULT 0 NOT NULL,
    "tagId" integer DEFAULT 0 NOT NULL
);


ALTER TABLE public."tagsToNote" OWNER TO postgres;

--
-- Name: tagsToNote_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."tagsToNote_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."tagsToNote_id_seq" OWNER TO postgres;

--
-- Name: tagsToNote_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."tagsToNote_id_seq" OWNED BY public."tagsToNote".id;


--
-- Name: accounts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts ALTER COLUMN id SET DEFAULT nextval('public.accounts_id_seq'::regclass);


--
-- Name: attachments id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments ALTER COLUMN id SET DEFAULT nextval('public.attachments_id_seq'::regclass);


--
-- Name: cache id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cache ALTER COLUMN id SET DEFAULT nextval('public.cache_id_seq'::regclass);


--
-- Name: comments id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments ALTER COLUMN id SET DEFAULT nextval('public.comments_id_seq'::regclass);


--
-- Name: config id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.config ALTER COLUMN id SET DEFAULT nextval('public.config_id_seq'::regclass);


--
-- Name: conversation id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversation ALTER COLUMN id SET DEFAULT nextval('public.conversation_id_seq'::regclass);


--
-- Name: follows id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows ALTER COLUMN id SET DEFAULT nextval('public.follows_id_seq'::regclass);


--
-- Name: message id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message ALTER COLUMN id SET DEFAULT nextval('public.message_id_seq'::regclass);


--
-- Name: noteHistory id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteHistory" ALTER COLUMN id SET DEFAULT nextval('public."noteHistory_id_seq"'::regclass);


--
-- Name: noteInternalShare id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteInternalShare" ALTER COLUMN id SET DEFAULT nextval('public."noteInternalShare_id_seq"'::regclass);


--
-- Name: noteReference id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference" ALTER COLUMN id SET DEFAULT nextval('public."noteReference_id_seq"'::regclass);


--
-- Name: notes id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notes ALTER COLUMN id SET DEFAULT nextval('public.notes_id_seq'::regclass);


--
-- Name: notifications id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications ALTER COLUMN id SET DEFAULT nextval('public.notifications_id_seq'::regclass);


--
-- Name: plugin id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.plugin ALTER COLUMN id SET DEFAULT nextval('public.plugin_id_seq'::regclass);


--
-- Name: tag id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag ALTER COLUMN id SET DEFAULT nextval('public.tag_id_seq'::regclass);


--
-- Name: tagsToNote id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote" ALTER COLUMN id SET DEFAULT nextval('public."tagsToNote_id_seq"'::regclass);


--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
b46ce3a6-9800-4b76-b7ed-378e951042bb	9fa97d3b49d43eb600745ee4c4845358be7d3e1139f98bed17df6c8e8afe8e46	2025-02-27 15:31:11.800519+00	20241026033301_0_0_1	\N	\N	2025-02-27 15:31:11.727358+00	1
93686754-967c-48df-8e44-33ef900735fd	2ce1697d65421c8f9e35edd5c2c53763bdebe368434bfc6b24e30fdb42f2dff4	2025-02-27 15:31:11.943045+00	20250110022717_0_34_15	\N	\N	2025-02-27 15:31:11.935775+00	1
1622c434-2cdd-452a-bcab-6fd80ae8bfd2	bca4800423e0a93798bd3bd4495222c5f1a290ee6a013be2b2250eae49fd9b5f	2025-02-27 15:31:11.809355+00	20241027072348_0_0_3	\N	\N	2025-02-27 15:31:11.803237+00	1
4497093b-c2d0-4322-ac46-5864340b2bd1	f66411c9c48f9b8d624d3d0a84183ea59fa005f34143a2348cbec15213d2c829	2025-02-27 15:31:11.818425+00	20241112084821_0_2_9	\N	\N	2025-02-27 15:31:11.811808+00	1
d284dd9e-fce2-49ce-9bc6-d998a6c75863	570c238793a05d26ea62ac05fb2c4ecf55e704e5a648b2d346e92ce727cd9dc2	2025-02-27 15:31:11.82783+00	20241121082228_0_6_0	\N	\N	2025-02-27 15:31:11.82196+00	1
0264d961-f599-4f90-9c2e-fb4f916991a6	7a72662f97fe7d3ff25f48ba8b0b4d775a7786fb382f9ffa74d1568d11041996	2025-02-27 15:31:11.960911+00	20250110105226_0_36_0	\N	\N	2025-02-27 15:31:11.944946+00	1
01d08f66-4a63-4739-9265-3a656f3048f2	08cb30f3ae17306833068ee37027f90243ec9fe0ad1ccf317097d408d8f08778	2025-02-27 15:31:11.835802+00	20241202103221_0_12_11	\N	\N	2025-02-27 15:31:11.82985+00	1
5d7a7dd4-e908-4502-8a1f-f2ae6b19a531	d7ba53a4d86751d7d3b05477676eace96e0907eee7ca35e5d77d32559631c323	2025-02-27 15:31:11.852223+00	20241205035247_18_0_1	\N	\N	2025-02-27 15:31:11.837755+00	1
28d1100e-68ad-4a91-8137-592b617699be	cb212e993eeb326e7092e5cea14f069bde6601c29863602b3f998b3847afbcb7	2025-02-27 15:31:11.861016+00	20241212033352_0_23_3	\N	\N	2025-02-27 15:31:11.854958+00	1
c6ef5b0c-88c3-4dc8-91d4-35c85f592202	bb7b9ee3fe8d49311ead5709ef71579ca099b4f0e05011a07229fc49e4273cd5	2025-02-27 15:31:11.982831+00	20250115050059_0_37_0	\N	\N	2025-02-27 15:31:11.963633+00	1
72643890-4893-437c-ae3a-ea86c99e7143	60e2f4991f43b9abed2c0982c0cda50be307e7f6673d9aaf099a6f4b6edb90d6	2025-02-27 15:31:11.869354+00	20241218023101_0_27_0	\N	\N	2025-02-27 15:31:11.863365+00	1
050d30fd-8893-486a-9c9b-2e9fc577932c	5f13a60e92bfbe447840075a94ac3e2f37159c611c08f75e78c79e9b3ac1fcff	2025-02-27 15:31:11.876685+00	20241219062514_0_27_7	\N	\N	2025-02-27 15:31:11.871131+00	1
834ffe0c-018e-455c-be48-c42169d8390b	471626763957618b7b81e0a266dc4179c63a8d62b254a88c7499734bd163ed04	2025-02-27 15:31:11.885907+00	20241226141834_0_30_7	\N	\N	2025-02-27 15:31:11.878537+00	1
030d7b96-35ef-4ac3-a853-6e6a588506d2	73c54b7c727c21868a10b47577b678fbd15b2e720410773ccf0087a67c371e0d	2025-02-27 15:31:12.00228+00	20250116052731_0_37_4	\N	\N	2025-02-27 15:31:11.984727+00	1
2d0891da-4134-46a5-89e8-bb0ca9a954bd	a626e584989cc0e1712e9a1b16e4cbbae3a2c0ab2214d137bb748fd115687821	2025-02-27 15:31:11.89455+00	20241231014246_0_32_0	\N	\N	2025-02-27 15:31:11.888658+00	1
feb61e73-def0-49ca-b26c-438d52d1d3d4	89a28b674edab6177c0b56968bb4920ef98b144ec4e8bb490290e3cd8eb081e1	2025-02-27 15:31:11.902694+00	20250101032000_0_32_4	\N	\N	2025-02-27 15:31:11.897162+00	1
dec5e363-f0ad-4266-a256-919ea67e06b1	b577841cbcd0b79b95b59569c546caced160b42ae811c42cbdc7d952ffe736c9	2025-02-27 15:31:11.923314+00	20250108081202_0_34_0	\N	\N	2025-02-27 15:31:11.904603+00	1
53244c5e-8526-45f1-ae4d-77ffa33ab162	5ff61439418b8e46dbf00dcddd25ff1fe09adbf8c191b4285fbe9f65b67e4a07	2025-02-27 15:31:12.019893+00	20250219083523_0_39_0	\N	\N	2025-02-27 15:31:12.004065+00	1
bb8e9b9b-fae2-475f-8a56-ecea286eca95	df97e584d79952e8a5a5463ec9f46bd3aaae1776d38aacbb5320adb7576e518f	2025-02-27 15:31:11.93327+00	20250110011520_0_34_13	\N	\N	2025-02-27 15:31:11.925354+00	1
63ba26f8-0121-4a5d-b587-87653320e8b8	ba9d281c54e8e733318de494b4b8e37d4977f9611dea63bc38b96751d10b9973	2025-02-27 15:31:12.049134+00	20250222125610_0_41_0	\N	\N	2025-02-27 15:31:12.022231+00	1
c2e77048-59b8-4cb4-8f3b-079712dfe40d	d1cd034aa7e81c8e6ed452103a686644c9a6c5a8dff5aeaf4c5063ec87b229ae	2025-07-16 12:07:42.912912+00	20250306102203_0_42_4	\N	\N	2025-07-16 12:07:42.891564+00	1
464df19e-f0fe-4e48-88e5-dcb569222094	184cced2759341c1fdee86f23546a9c964aa94cf044820603d309e6b83cf07ec	2025-07-16 12:07:42.926154+00	20250331072252_0_48_0	\N	\N	2025-07-16 12:07:42.913816+00	1
fa223f3d-c8eb-409e-94e3-f944a01beaf3	9762fcc2a98534a8e77886ff2af3b58c8b450e92745a6a5ef7555cdb7d58d23b	2025-07-16 12:07:42.937389+00	20250418115430_1_0_0	\N	\N	2025-07-16 12:07:42.927044+00	1
********-15e4-4f66-a380-bfa045ad82d1	2bbc73c91486741fff8d710ba26b9c7e70c8e403f2bb9f31ef41154be8253198	2025-07-16 12:07:42.941564+00	20250529145705_1_0_4	\N	\N	2025-07-16 12:07:42.938116+00	1
\.


--
-- Data for Name: accounts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.accounts (id, name, nickname, password, image, "apiToken", note, role, "createdAt", "updatedAt", "loginType", "linkAccountId", description) FROM stdin;
1	Cotton	Cotton	pbkdf2:531683872cc50d0c077610277b383b13:2a3597f0423fc9c4740f513c7043329d5d62c03cc9521376e2b18a0fc2733880175eebcd03dfd5f21415881e14cd3091aaf4117dcbd7d1dfc48e0dc54b943b6e		eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..A3SABkHSW5nHBN7J.9Xj93PzoiEie8V9PwM7dnj-5fMFy7M8SwJf-tT25VxUJjtA_KXo-Nh2et8Eblt8WJWROsy0F4y_Q6spIP99tXn9dempStMPDlNXynezCDQAFubhLhQ9owqeLiz3gMI_gUpfQmIoWlPKw7jLYJH_Tr153rDT0dHwEwILOOtFN.qpS-8mlotMNaAQxI9lxPRg	0	superadmin	2025-02-27 15:52:53.307+00	2025-02-27 15:52:53.32+00		\N	
\.


--
-- Data for Name: attachments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.attachments (id, "isShare", "sharePassword", name, path, size, "noteId", "createdAt", "updatedAt", type, "sortOrder", depth, "perfixPath", "accountId") FROM stdin;
1	f		pic01.png	/api/file/pic01.png	1360952	2	2025-02-27 15:52:53.348+00	2025-03-08 14:22:00.394+00		0	0		\N
2	f		pic02.png	/api/file/pic02.png	971782	2	2025-02-27 15:52:53.348+00	2025-03-08 14:22:00.402+00		0	0		\N
3	f		pic03.png	/api/file/pic03.png	141428	2	2025-02-27 15:52:53.348+00	2025-03-08 14:22:00.405+00		0	0		\N
4	f		pic04.png	/api/file/pic04.png	589371	2	2025-02-27 15:52:53.348+00	2025-03-08 14:22:00.408+00		0	0		\N
5	f		pic06.png	/api/file/pic06.png	875361	2	2025-02-27 15:52:53.348+00	2025-03-08 14:22:00.411+00		0	0		\N
6	f		story.txt	/api/file/story.txt	0	2	2025-02-27 15:52:53.348+00	2025-03-08 14:22:00.414+00		0	0		\N
\.


--
-- Data for Name: cache; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.cache (id, key, value, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: comments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.comments (id, content, "accountId", "guestName", "guestIP", "guestUA", "noteId", "parentId", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: config; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.config (id, key, config, "userId") FROM stdin;
1	NEXTAUTH_SECRET	{"value":"CSVoNVGcIBxOUXLoyX/JsCKWAr6ShsTNJUEVByswtMc="}	\N
3	isUseAI	{"type":"boolean","value":true}	\N
7	aiApiKey	{"type":"string","value":"sk-FN0OmEVkXsA442zJ2U05uZ6CEsu5ckpTh6ryBiYY"}	\N
9	spotifyConsumerKey	{"type":"string","value":"Cotton"}	\N
10	spotifyConsumerSecret	{"type":"string","value":"TCW147896"}	\N
5	aiModel	{"type":"string","value":"gpt-4o-mini"}	\N
4	embeddingModel	{"type":"string","value":"text-embedding-ada-002"}	\N
11	tavilyApiKey	{"type":"string","value":"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"}	\N
12	tavilyMaxResult	{"type":"number","value":4}	\N
13	language	{"type":"string","value":"zh"}	1
6	aiModelProvider	{"type":"string","value":"OpenAI"}	\N
8	aiApiEndpoint	{"type":"string","value":"https://free.yunwu.ai/v1"}	\N
14	localCustomPath	{"type":"string","value":""}	\N
15	objectStorage	{"type":"string","value":"s3"}	\N
16	JWT_SECRET	{"value":"U9pq6Mjj0Zd5sKg5uYXnN1hNpNHBQG/NGp0IOI+OY0s="}	\N
17	themeColor	{"type":"string","value":"#65a30d"}	1
18	themeForegroundColor	{"type":"string","value":"#ffffff"}	1
2	theme	{"type":"string","value":"light"}	1
\.


--
-- Data for Name: conversation; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.conversation (id, title, "accountId", "createdAt", "updatedAt", "isShare") FROM stdin;
1		1	2025-03-01 10:19:01.212+00	2025-03-01 10:19:01.212+00	f
2		1	2025-03-01 10:23:09.277+00	2025-03-01 10:23:09.277+00	f
3		1	2025-03-01 10:38:02.276+00	2025-03-01 10:38:02.276+00	f
4		1	2025-03-08 03:03:07.056+00	2025-03-08 03:03:07.056+00	f
\.


--
-- Data for Name: follows; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.follows (id, "siteName", "siteUrl", "siteAvatar", description, "createdAt", "updatedAt", "followType", "accountId") FROM stdin;
\.


--
-- Data for Name: message; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.message (id, content, role, "conversationId", "createdAt", "updatedAt", metadata) FROM stdin;
1	你好	user	1	2025-03-01 10:19:01.624+00	2025-03-01 10:19:01.624+00	\N
2	你好	user	1	2025-03-01 10:21:25.442+00	2025-03-01 10:21:25.442+00	\N
3	你能帮我做什么	user	2	2025-03-01 10:23:09.678+00	2025-03-01 10:23:09.678+00	\N
4	你好	user	2	2025-03-01 10:32:58.664+00	2025-03-01 10:32:58.664+00	\N
5	你好呀	user	3	2025-03-01 10:38:02.478+00	2025-03-01 10:38:02.478+00	\N
6	你好	user	4	2025-03-08 03:03:07.191+00	2025-03-08 03:03:07.191+00	\N
7	你好	user	4	2025-03-08 03:05:47.394+00	2025-03-08 03:05:47.394+00	\N
\.


--
-- Data for Name: noteHistory; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."noteHistory" (id, "noteId", content, metadata, version, "accountId", "createdAt") FROM stdin;
\.


--
-- Data for Name: noteInternalShare; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."noteInternalShare" (id, "noteId", "accountId", "canEdit", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: noteReference; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."noteReference" (id, "fromNoteId", "toNoteId", "createdAt") FROM stdin;
\.


--
-- Data for Name: notes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notes (id, type, content, "isArchived", "isRecycle", "isShare", "isTop", "sharePassword", metadata, "createdAt", "updatedAt", "isReviewed", "accountId", "shareEncryptedUrl", "shareExpiryDate", "shareMaxView", "shareViewCount") FROM stdin;
1	0	#Welcome\n\nWelcome to Blinko!\n\nWhether you're capturing ideas, taking meeting notes, or planning your schedule, Blinko provides an easy and efficient way to manage it all. Here, you can create, edit, and share notes anytime, anywhere, ensuring you never lose a valuable thought.	f	f	f	f		\N	2025-02-27 15:52:53.337+00	2025-02-27 15:52:53.337+00	f	1	\N	\N	0	0
2	0	#Welcome/Attachment	f	f	f	f		\N	2025-02-27 15:52:53.337+00	2025-02-27 15:52:53.337+00	f	1	\N	\N	0	0
3	0	#Welcome/Code\n\n\n\n```js\nfunction Welcome(){\n  console.log("Hello! Blinko");\n}\n```	f	f	f	f		\N	2025-02-27 15:52:53.337+00	2025-02-27 15:52:53.337+00	f	1	\N	\N	0	0
4	0	#Welcome/To-Do\n\n* Create a blinko\n* Create a note\n* Upload file	f	f	f	f		\N	2025-02-27 15:52:53.337+00	2025-02-27 15:52:53.337+00	f	1	\N	\N	0	0
5	0	#Welcome/Multi-Level-Tags\n\nUse the "/" shortcut to effortlessly create and organize multi-level tags.	f	f	f	f		\N	2025-02-27 15:52:53.337+00	2025-02-27 15:52:53.337+00	f	1	\N	\N	0	0
6	0	https://github.com/blinko-space/blinko/	f	f	f	f		\N	2025-02-27 15:52:53.337+00	2025-02-27 15:52:53.337+00	f	1	\N	\N	0	0
8	0	#p/项目 少女\n	f	f	f	f		\N	2025-02-27 15:56:07.517+00	2025-02-27 15:56:07.517+00	f	1	\N	\N	0	0
9	0	#硅基 sk-jlrbbqfihhxdxfzftkviokwvbyjmkayejednthwxqjrrrtwq\n	f	f	f	f		\N	2025-03-08 02:23:11.817+00	2025-03-08 02:23:11.817+00	f	1	\N	\N	0	0
10	0	#记录 这是一个测试\n\n#Welcome\n	f	f	f	f		{}	2025-07-16 14:11:26.158+00	2025-07-16 14:11:26.158+00	f	1	\N	\N	0	0
11	1	#笔记 没有笔记\n	f	f	f	f		{}	2025-07-16 14:11:48.511+00	2025-07-16 14:11:48.511+00	f	1	\N	\N	0	0
12	0	#备份导入\n\n🎉 成功从备份导入数据！\n\n这是一个示例笔记，展示了从backup_20250318.sql中成功导入的数据结构。\n\n## 导入内容包括：\n- 📝 精选标签体系\n- 📄 示例笔记内容\n- 🔗 保持数据关联性\n\n## 下一步操作：\n1. 查看并组织标签\n2. 添加个人笔记内容\n3. 建立知识管理体系	f	f	f	f		{"isIndexed":true}	2025-07-17 00:06:40.555339+00	2025-07-17 00:06:40.555339+00	f	1	\N	\N	0	0
13	0	#知识管理\n\n## 核心原则\n重要的事情做备份、分摊影响（把鸡蛋放在几个篮子里）、多方案思维（尽可能做几个方案，即使一个方案不成，还有额外的方案做弥补）\n\n## 实践方法\n1. **信息收集**: 使用标签系统分类整理\n2. **定期回顾**: 建立复习机制  \n3. **知识连接**: 建立笔记间的关联\n4. **输出倒逼**: 通过写作加深理解\n\n## 工具推荐\n- Blinko: 快速记录和管理\n- 标签体系: 分类和检索\n- 定期整理: 保持知识库的活力	f	f	f	f		{"isIndexed":true}	2025-07-17 00:07:08.225332+00	2025-07-17 00:07:08.225332+00	f	1	\N	\N	0	0
14	0	#AI技术\n\n## 现代开发技术栈\nTypeScript + Next.js (T3 Stack) + React 就是大模型最擅长的框架组合\n\n## AI工具应用\n- **ChatGPT/Claude**: 编程助手和问题解答\n- **GitHub Copilot**: 代码自动完成\n- **Cursor**: AI增强的代码编辑器  \n- **Midjourney**: 图像生成\n\n## 学习建议\n1. 掌握基础的TypeScript语法\n2. 熟悉React组件开发\n3. 了解Next.js的SSR特性\n4. 实践AI辅助开发流程	f	f	f	f		{"isIndexed":true}	2025-07-17 00:07:40.445165+00	2025-07-17 00:07:40.445165+00	f	1	\N	\N	0	0
\.


--
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notifications (id, type, title, content, metadata, "isRead", "accountId", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: plugin; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.plugin (id, metadata, path, "isUse", "isDev", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: scheduledTask; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."scheduledTask" (name, schedule, "lastRun", "isSuccess", "isRunning", output) FROM stdin;
rebuildEmbedding	0 0 * * *	2025-07-17 00:00:00.512	t	t	{}
\.


--
-- Data for Name: session; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.session (id, sid, data, "expiresAt", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: tag; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.tag (id, name, icon, parent, "createdAt", "updatedAt", "accountId", "sortOrder") FROM stdin;
1	Welcome	🎉	0	2025-02-27 15:52:53.341+00	2025-02-27 15:52:53.341+00	1	0
2	Attachment	🔖	1	2025-02-27 15:52:53.341+00	2025-02-27 15:52:53.341+00	1	0
3	Code	🪄	1	2025-02-27 15:52:53.341+00	2025-02-27 15:52:53.341+00	1	0
4	To-Do	✨	1	2025-02-27 15:52:53.341+00	2025-02-27 15:52:53.341+00	1	0
5	Multi-Level-Tags	🏷️	1	2025-02-27 15:52:53.341+00	2025-02-27 15:52:53.341+00	1	0
7	p		0	2025-02-27 15:56:07.527+00	2025-02-27 15:56:07.527+00	1	0
8	项目		7	2025-02-27 15:56:07.537+00	2025-02-27 15:56:07.537+00	1	0
9	硅基		0	2025-03-08 02:23:15.386+00	2025-03-08 02:23:15.386+00	1	0
10	记录		0	2025-07-16 14:11:26.173+00	2025-07-16 14:11:26.173+00	1	0
11	笔记		0	2025-07-16 14:11:48.518+00	2025-07-16 14:11:48.518+00	1	0
12	备份导入	📥	0	2025-07-17 00:06:09.263743+00	2025-07-17 00:06:09.263743+00	1	0
13	经验总结	💡	0	2025-07-17 00:06:09.263743+00	2025-07-17 00:06:09.263743+00	1	0
14	AI技术	🤖	0	2025-07-17 00:06:09.263743+00	2025-07-17 00:06:09.263743+00	1	0
15	开发工具	🔧	0	2025-07-17 00:06:09.263743+00	2025-07-17 00:06:09.263743+00	1	0
\.


--
-- Data for Name: tagsToNote; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."tagsToNote" (id, "noteId", "tagId") FROM stdin;
1	1	1
2	2	1
3	2	2
4	3	1
5	3	3
6	4	1
7	4	4
8	5	1
9	5	5
11	8	7
12	8	8
13	10	10
14	10	1
15	11	11
\.


--
-- Name: accounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.accounts_id_seq', 1, true);


--
-- Name: attachments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.attachments_id_seq', 7, true);


--
-- Name: cache_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.cache_id_seq', 1, false);


--
-- Name: comments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.comments_id_seq', 1, false);


--
-- Name: config_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.config_id_seq', 18, true);


--
-- Name: conversation_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.conversation_id_seq', 4, true);


--
-- Name: follows_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.follows_id_seq', 1, false);


--
-- Name: message_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.message_id_seq', 7, true);


--
-- Name: noteHistory_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."noteHistory_id_seq"', 1, false);


--
-- Name: noteInternalShare_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."noteInternalShare_id_seq"', 1, false);


--
-- Name: noteReference_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."noteReference_id_seq"', 1, false);


--
-- Name: notes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.notes_id_seq', 14, true);


--
-- Name: notifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.notifications_id_seq', 1, false);


--
-- Name: plugin_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.plugin_id_seq', 1, false);


--
-- Name: tag_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.tag_id_seq', 15, true);


--
-- Name: tagsToNote_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."tagsToNote_id_seq"', 15, true);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: accounts accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (id);


--
-- Name: attachments attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments
    ADD CONSTRAINT attachments_pkey PRIMARY KEY (id);


--
-- Name: cache cache_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cache
    ADD CONSTRAINT cache_pkey PRIMARY KEY (id);


--
-- Name: comments comments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_pkey PRIMARY KEY (id);


--
-- Name: config config_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.config
    ADD CONSTRAINT config_pkey PRIMARY KEY (id);


--
-- Name: conversation conversation_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversation
    ADD CONSTRAINT conversation_pkey PRIMARY KEY (id);


--
-- Name: follows follows_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT follows_pkey PRIMARY KEY (id);


--
-- Name: message message_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message
    ADD CONSTRAINT message_pkey PRIMARY KEY (id);


--
-- Name: noteHistory noteHistory_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteHistory"
    ADD CONSTRAINT "noteHistory_pkey" PRIMARY KEY (id);


--
-- Name: noteInternalShare noteInternalShare_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteInternalShare"
    ADD CONSTRAINT "noteInternalShare_pkey" PRIMARY KEY (id);


--
-- Name: noteReference noteReference_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference"
    ADD CONSTRAINT "noteReference_pkey" PRIMARY KEY (id);


--
-- Name: notes notes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notes
    ADD CONSTRAINT notes_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: plugin plugin_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.plugin
    ADD CONSTRAINT plugin_pkey PRIMARY KEY (id);


--
-- Name: scheduledTask scheduledTask_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."scheduledTask"
    ADD CONSTRAINT "scheduledTask_pkey" PRIMARY KEY (name);


--
-- Name: session session_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.session
    ADD CONSTRAINT session_pkey PRIMARY KEY (id);


--
-- Name: tag tag_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag
    ADD CONSTRAINT tag_pkey PRIMARY KEY (id);


--
-- Name: tagsToNote tagsToNote_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote"
    ADD CONSTRAINT "tagsToNote_pkey" PRIMARY KEY ("noteId", "tagId");


--
-- Name: cache_key_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX cache_key_key ON public.cache USING btree (key);


--
-- Name: conversation_accountId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "conversation_accountId_idx" ON public.conversation USING btree ("accountId");


--
-- Name: message_conversationId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "message_conversationId_idx" ON public.message USING btree ("conversationId");


--
-- Name: noteHistory_accountId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "noteHistory_accountId_idx" ON public."noteHistory" USING btree ("accountId");


--
-- Name: noteHistory_noteId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "noteHistory_noteId_idx" ON public."noteHistory" USING btree ("noteId");


--
-- Name: noteInternalShare_accountId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "noteInternalShare_accountId_idx" ON public."noteInternalShare" USING btree ("accountId");


--
-- Name: noteInternalShare_noteId_accountId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "noteInternalShare_noteId_accountId_key" ON public."noteInternalShare" USING btree ("noteId", "accountId");


--
-- Name: noteInternalShare_noteId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "noteInternalShare_noteId_idx" ON public."noteInternalShare" USING btree ("noteId");


--
-- Name: noteReference_fromNoteId_toNoteId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "noteReference_fromNoteId_toNoteId_key" ON public."noteReference" USING btree ("fromNoteId", "toNoteId");


--
-- Name: session_expiresAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "session_expiresAt_idx" ON public.session USING btree ("expiresAt");


--
-- Name: session_sid_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX session_sid_key ON public.session USING btree (sid);


--
-- Name: attachments attachments_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments
    ADD CONSTRAINT "attachments_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: attachments attachments_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attachments
    ADD CONSTRAINT "attachments_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: comments comments_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT "comments_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: comments comments_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT "comments_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: comments comments_parentId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT "comments_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES public.comments(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: config config_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.config
    ADD CONSTRAINT "config_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: conversation conversation_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversation
    ADD CONSTRAINT "conversation_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: follows follows_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT "follows_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: message message_conversationId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message
    ADD CONSTRAINT "message_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES public.conversation(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: noteHistory noteHistory_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteHistory"
    ADD CONSTRAINT "noteHistory_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: noteInternalShare noteInternalShare_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteInternalShare"
    ADD CONSTRAINT "noteInternalShare_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: noteInternalShare noteInternalShare_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteInternalShare"
    ADD CONSTRAINT "noteInternalShare_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: noteReference noteReference_fromNoteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference"
    ADD CONSTRAINT "noteReference_fromNoteId_fkey" FOREIGN KEY ("fromNoteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: noteReference noteReference_toNoteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."noteReference"
    ADD CONSTRAINT "noteReference_toNoteId_fkey" FOREIGN KEY ("toNoteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: notes notes_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notes
    ADD CONSTRAINT "notes_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: notifications notifications_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "notifications_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: tag tag_accountId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tag
    ADD CONSTRAINT "tag_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: tagsToNote tagsToNote_noteId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote"
    ADD CONSTRAINT "tagsToNote_noteId_fkey" FOREIGN KEY ("noteId") REFERENCES public.notes(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: tagsToNote tagsToNote_tagId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."tagsToNote"
    ADD CONSTRAINT "tagsToNote_tagId_fkey" FOREIGN KEY ("tagId") REFERENCES public.tag(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- PostgreSQL database dump complete
--

