<svg width="3345" height="907" viewBox="0 0 3345 907" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3007.87 731.844C2973.04 693.074 2955.62 639.413 2955.62 570.862C2955.62 502.311 2973.04 448.651 3007.87 409.88C3043.27 371.11 3090.75 351.724 3150.31 351.724C3209.87 351.724 3257.07 371.11 3291.91 409.88C3326.75 448.651 3344.16 502.311 3344.16 570.862C3344.16 639.413 3326.75 693.074 3291.91 731.844C3257.07 770.615 3209.87 790 3150.31 790C3090.75 790 3043.27 770.615 3007.87 731.844ZM3150.31 492.478C3118.85 492.478 3103.11 518.325 3103.11 570.019C3103.11 621.713 3118.85 647.56 3150.31 647.56C3182.34 647.56 3198.35 621.713 3198.35 570.019C3198.35 518.325 3182.34 492.478 3150.31 492.478Z" fill="#030303"/>
<path d="M2545.27 198.328C2545.27 189.337 2570.84 184.842 2621.97 184.842C2669.73 186.528 2693.61 190.18 2693.61 195.799V519.449L2777.05 365.21C2780.99 356.781 2804.87 352.567 2848.69 352.567C2892.52 352.567 2920.62 355.377 2932.98 360.995L2823.41 539.677L2949.83 776.515C2950.96 778.762 2940.28 780.448 2917.81 781.572C2895.89 783.257 2877.63 784.1 2863.02 784.1C2809.08 784.1 2780.14 781.572 2776.21 776.515L2692.77 589.405V775.672C2692.77 782.976 2668.05 786.629 2618.6 786.629C2569.71 786.629 2545.27 783.257 2545.27 776.515V198.328Z" fill="#030303"/>
<path d="M2257.21 776.515C2257.21 780.448 2236.98 782.414 2196.52 782.414C2140.33 780.729 2112.24 777.638 2112.24 773.143V500.064H2090.32C2075.72 500.064 2066.16 487.702 2061.67 462.979C2060.54 455.674 2059.98 448.37 2059.98 441.065C2059.98 423.646 2063.35 406.228 2070.1 388.809C2080.21 364.086 2095.94 351.724 2117.3 351.724C2138.65 351.724 2157.75 356.781 2174.61 366.895C2191.47 377.009 2202.7 387.123 2208.32 397.238C2243.72 366.895 2285.86 351.724 2334.75 351.724C2383.63 351.724 2419.03 364.929 2440.95 391.338C2462.86 417.747 2473.82 461.012 2473.82 521.135V773.986C2473.82 780.729 2449.65 784.1 2401.33 784.1C2353.57 784.1 2329.69 780.729 2329.69 773.986V530.406C2329.69 502.311 2319.86 488.264 2300.19 488.264C2294.57 488.264 2286.71 490.793 2276.59 495.85C2266.48 500.345 2260.02 503.716 2257.21 505.964V776.515Z" fill="#030303"/>
<path d="M2004.83 776.515C2004.83 781.01 1978.98 783.258 1927.29 783.258C1876.16 783.258 1850.59 781.291 1850.59 777.358V363.525C1850.59 359.591 1876.16 357.625 1927.29 357.625C1978.98 357.625 2004.83 359.31 2004.83 362.682V776.515ZM2004.83 312.954C2004.83 316.887 1978.98 318.854 1927.29 318.854C1876.16 318.854 1850.59 317.168 1850.59 313.797V192.428C1850.59 186.809 1876.16 184 1927.29 184C1978.98 184 2004.83 185.686 2004.83 189.057V312.954Z" fill="#030303"/>
<path d="M1774.35 774.829C1774.35 779.886 1745.69 782.415 1688.38 782.415C1643.42 781.853 1620.95 779.605 1620.95 775.672V201.699C1620.95 193.271 1648.2 189.057 1702.7 189.057C1750.47 190.742 1774.35 193.552 1774.35 197.485V774.829Z" fill="#030303"/>
<path d="M1538.56 344.139C1538.56 402.576 1513.28 446.965 1462.71 477.307C1490.24 487.421 1513.28 504.84 1531.82 529.563C1550.36 554.287 1559.63 589.405 1559.63 634.918C1559.63 679.87 1542.78 715.55 1509.06 741.959C1475.91 768.367 1428.15 781.572 1365.78 781.572H1166.87C1157.32 781.572 1149.45 778.482 1143.27 772.301C1137.09 765.558 1134 756.849 1134 746.173V206.756C1134 200.576 1134.84 196.642 1136.53 194.957C1138.78 192.709 1142.99 191.585 1149.17 191.585H1341.34C1472.82 191.585 1538.56 242.437 1538.56 344.139ZM1295.82 328.125V438.537H1300.04C1353.98 438.537 1380.95 420.556 1380.95 384.595C1380.95 364.929 1375.61 350.601 1364.94 341.611C1354.82 332.62 1337.69 328.125 1313.52 328.125H1295.82ZM1295.82 537.992V647.561H1311C1335.16 647.561 1352.86 643.066 1364.09 634.075C1375.33 625.085 1380.95 610.757 1380.95 591.091C1380.95 571.424 1375.61 557.658 1364.94 549.792C1354.82 541.925 1337.69 537.992 1313.52 537.992H1295.82Z" fill="#030303"/>
<g clip-path="url(#clip0_6_653)">
<rect width="904" height="907" rx="267" fill="url(#paint0_linear_6_653)"/>
<g style="mix-blend-mode:overlay">
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.9026 3881L16.9026 -3069H17.9026L17.9026 3881H16.9026Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M49.7617 3881L49.7617 -3069H50.7617L50.7617 3881H49.7617Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M82.6208 3881L82.6208 -3069H83.6208L83.6208 3881H82.6208Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M115.48 3881L115.48 -3069H116.48L116.48 3881H115.48Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M148.339 3881L148.339 -3069H149.339L149.339 3881H148.339Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M181.198 3881L181.198 -3069H182.198L182.198 3881H181.198Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M214.057 3881L214.057 -3069H215.057L215.057 3881H214.057Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M246.917 3881L246.917 -3069H247.917L247.917 3881H246.917Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M279.776 3881L279.776 -3069H280.776L280.776 3881H279.776Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M312.635 3881L312.635 -3069H313.635L313.635 3881H312.635Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M345.494 3881L345.494 -3069H346.494L346.494 3881H345.494Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M378.353 3881L378.353 -3069H379.353L379.353 3881H378.353Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M411.212 3881L411.212 -3069H412.212L412.212 3881H411.212Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M444.071 3881L444.071 -3069H445.071L445.071 3881H444.071Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M476.93 3881L476.93 -3069H477.93L477.93 3881H476.93Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M509.789 3881L509.789 -3069H510.789L510.789 3881H509.789Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M542.649 3881L542.649 -3069H543.649L543.649 3881H542.649Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M575.508 3881L575.508 -3069H576.508L576.508 3881H575.508Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M608.367 3881L608.367 -3069H609.367L609.367 3881H608.367Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M641.226 3881L641.226 -3069H642.226L642.226 3881H641.226Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M674.085 3881L674.085 -3069H675.085L675.085 3881H674.085Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M706.944 3881L706.944 -3069H707.944L707.944 3881H706.944Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M739.804 3881L739.804 -3069H740.804L740.804 3881H739.804Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M772.663 3881L772.663 -3069H773.663L773.663 3881H772.663Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M805.522 3881L805.522 -3069H806.522L806.522 3881H805.522Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M838.381 3881L838.381 -3069H839.381L839.381 3881H838.381Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M871.24 3881L871.24 -3069H872.24L872.24 3881H871.24Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9948 10.9999L1626.96 11L1626.96 12L-15.9948 11.9999L-15.9948 10.9999Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9948 38.9999L1626.96 39L1626.96 40L-15.9948 39.9999L-15.9948 38.9999Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9946 66.9999L1626.96 67L1626.96 68L-15.9946 67.9999L-15.9946 66.9999Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9945 94.9999L1626.96 95L1626.96 96L-15.9945 95.9999L-15.9945 94.9999Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9945 123L1626.96 123L1626.96 124L-15.9945 124L-15.9945 123Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9944 151L1626.96 151L1626.96 152L-15.9944 152L-15.9944 151Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9944 179L1626.96 179L1626.96 180L-15.9944 180L-15.9944 179Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9941 207L1626.96 207L1626.96 208L-15.9941 208L-15.9941 207Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9941 235L1626.96 235L1626.96 236L-15.9941 236L-15.9941 235Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.994 263L1626.96 263L1626.96 264L-15.994 264L-15.994 263Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.994 291L1626.96 291L1626.96 292L-15.994 292L-15.994 291Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9939 319L1626.96 319L1626.96 320L-15.9939 320L-15.9939 319Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9939 347L1626.96 347L1626.96 348L-15.9939 348L-15.9939 347Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9937 375L1626.96 375L1626.96 376L-15.9937 376L-15.9937 375Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9937 403L1626.96 403L1626.96 404L-15.9937 404L-15.9937 403Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9935 431L1626.96 431L1626.96 432L-15.9935 432L-15.9935 431Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9935 459L1626.96 459L1626.96 460L-15.9935 460L-15.9935 459Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9935 487L1626.96 487L1626.96 488L-15.9935 488L-15.9935 487Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9933 515L1626.96 515L1626.96 516L-15.9933 516L-15.9933 515Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9933 543L1626.96 543L1626.96 544L-15.9933 544L-15.9933 543Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9933 571L1626.96 571L1626.96 572L-15.9933 572L-15.9933 571Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9932 599L1626.96 599L1626.96 600L-15.9932 600L-15.9932 599Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9932 627L1626.96 627L1626.96 628L-15.9932 628L-15.9932 627Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.993 655L1626.96 655L1626.96 656L-15.993 656L-15.993 655Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.993 683L1626.96 683L1626.96 684L-15.993 684L-15.993 683Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9928 711L1626.96 711L1626.96 712L-15.9928 712L-15.9928 711Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9928 739L1626.96 739L1626.96 740L-15.9928 740L-15.9928 739Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9927 767L1626.96 767L1626.96 768L-15.9927 768L-15.9927 767Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9927 795L1626.96 795L1626.96 796L-15.9927 796L-15.9927 795Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9926 823L1626.96 823L1626.96 824L-15.9926 824L-15.9926 823Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9926 851L1626.96 851L1626.96 852L-15.9926 852L-15.9926 851Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9922 879L1626.96 879L1626.96 880L-15.9922 880L-15.9922 879Z" fill="white" fill-opacity="0.2"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-15.9922 907L1626.96 907L1626.96 908L-15.9922 908L-15.9922 907Z" fill="white" fill-opacity="0.2"/>
</g>
<path d="M746 475.521C746 626.295 623.774 748.521 473 748.521C322.226 748.521 200 626.295 200 475.521C200 324.747 322.226 202.521 473 202.521C623.774 202.521 746 324.747 746 475.521Z" fill="url(#paint1_linear_6_653)"/>
<path d="M130.206 211.647C118.176 217.707 116.142 238.177 116.763 250.932C116.98 255.386 119.812 259.214 123.96 260.852C147.92 270.313 180.186 271.787 189.979 272.481C198.787 273.105 198.339 268.454 197.493 263.88C197.148 262.011 196.198 260.289 194.776 259.027L146.568 216.269C141.954 212.178 135.713 208.873 130.206 211.647Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M138.557 211.856C135.821 210.896 133.031 210.784 130.431 212.094C124.61 215.025 121.147 221.481 119.224 228.904C117.306 236.309 116.953 244.562 117.263 250.908C117.47 255.159 120.172 258.819 124.143 260.387C146.224 269.106 175.408 271.02 187.437 271.809C188.414 271.873 189.277 271.93 190.014 271.982C194.367 272.291 196.178 271.277 196.915 269.895C197.3 269.172 197.437 268.279 197.415 267.252C197.392 266.226 197.213 265.113 197.002 263.97C196.676 262.208 195.78 260.586 194.445 259.402L146.236 216.643C143.958 214.623 141.3 212.817 138.557 211.856ZM138.888 210.912C141.795 211.932 144.564 213.824 146.899 215.895L195.108 258.653C196.616 259.991 197.62 261.814 197.985 263.789C198.197 264.934 198.39 266.117 198.414 267.23C198.439 268.343 198.296 269.431 197.797 270.365C196.768 272.296 194.398 273.296 189.944 272.98C189.209 272.928 188.346 272.871 187.369 272.807C175.357 272.02 146.014 270.098 123.776 261.317C119.452 259.61 116.491 255.613 116.264 250.956C115.952 244.548 116.305 236.187 118.256 228.654C120.203 221.138 123.771 214.329 129.981 211.201C132.888 209.736 135.973 209.89 138.888 210.912Z" fill="black"/>
<path d="M239.971 104.544C227.123 98.3585 213.891 111.481 205.949 121.767C203.257 125.255 202.693 129.871 204.138 134.034C214.064 162.641 231.012 210.106 238.132 217.728C244.69 224.748 256.672 217.372 262.238 212.037C263.803 210.537 264.283 208.316 263.899 206.182L247.962 117.644C247.008 112.341 244.826 106.881 239.971 104.544Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M247.47 117.732C246.521 112.463 244.379 107.221 239.754 104.994C233.55 102.008 227.222 103.654 221.391 107.399C215.555 111.147 210.303 116.946 206.345 122.073C203.765 125.414 203.215 129.851 204.61 133.87C209.573 148.173 216.289 167.184 222.653 183.57C225.836 191.763 228.928 199.294 231.667 205.249C233.036 208.227 234.315 210.805 235.471 212.873C236.631 214.948 237.65 216.48 238.497 217.386C241.573 220.679 245.951 220.652 250.387 219.046C254.814 217.442 259.143 214.311 261.892 211.676C263.307 210.319 263.769 208.283 263.407 206.271L247.47 117.732ZM240.188 104.093C245.272 106.541 247.494 112.22 248.454 117.555L264.391 206.094C264.797 208.349 264.298 210.754 262.584 212.398C259.766 215.099 255.321 218.322 250.727 219.986C246.141 221.646 241.249 221.797 237.767 218.069C236.834 217.07 235.766 215.451 234.598 213.361C233.425 211.262 232.134 208.658 230.758 205.667C228.007 199.685 224.907 192.133 221.721 183.932C215.35 167.528 208.629 148.503 203.665 134.198C202.171 129.892 202.748 125.096 205.554 121.462C209.537 116.302 214.872 110.397 220.85 106.558C226.832 102.717 233.544 100.895 240.188 104.093Z" fill="black"/>
<path opacity="0.37" d="M372 399.021C372 432.987 344.465 460.521 310.5 460.521C276.534 460.521 249 432.987 249 399.021C249 365.055 276.534 337.521 310.5 337.521C344.465 337.521 372 365.055 372 399.021Z" fill="#FF2E3C"/>
<path opacity="0.37" d="M553 405.021C553 442.3 522.779 472.521 485.5 472.521C448.221 472.521 418 442.3 418 405.021C418 367.742 448.221 337.521 485.5 337.521C522.779 337.521 553 367.742 553 405.021Z" fill="#FF2E3C"/>
<g filter="url(#filter0_b_6_653)">
<path d="M729 498.521C729 649.295 606.774 771.521 456 771.521C305.226 771.521 183 649.295 183 498.521C183 347.747 305.226 225.521 456 225.521C606.774 225.521 729 347.747 729 498.521Z" fill="url(#paint2_linear_6_653)"/>
</g>
<g filter="url(#filter1_b_6_653)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M456 769.521C605.669 769.521 727 648.19 727 498.521C727 348.852 605.669 227.521 456 227.521C306.331 227.521 185 348.852 185 498.521C185 648.19 306.331 769.521 456 769.521ZM456 771.521C606.774 771.521 729 649.295 729 498.521C729 347.747 606.774 225.521 456 225.521C305.226 225.521 183 347.747 183 498.521C183 649.295 305.226 771.521 456 771.521Z" fill="url(#paint3_linear_6_653)"/>
</g>
<path d="M499.623 313.415L437.25 341.459C436.424 341.83 435.667 342.35 435.085 343.043C427.406 352.185 430.293 364.291 433.412 370.57C434.06 371.874 435.224 372.817 436.572 373.368L499.143 398.945C501.52 399.916 504.237 399.508 506.224 397.883L506.665 397.523C511.307 393.724 514 388.042 514 382.043V380.486C514 377.274 511.814 374.474 508.698 373.695L488 368.521L468.351 364.1C468.117 364.047 467.888 363.975 467.667 363.885C465.04 362.812 464.148 359.536 465.87 357.28L467.997 354.491C468.97 353.216 470.35 352.314 471.907 351.936L487.448 348.155C487.815 348.066 488.189 348.006 488.566 347.977L498.28 347.23C507.822 346.496 516.598 339.872 514.955 330.444C513.297 320.925 507.954 312.77 501.458 312.937C500.82 312.953 500.205 313.154 499.623 313.415Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M509.618 318.354C507.312 315.214 504.489 313.359 501.471 313.436C500.927 313.45 500.381 313.622 499.828 313.871L437.455 341.915C436.683 342.262 435.992 342.74 435.468 343.364C427.985 352.274 430.77 364.128 433.86 370.348C434.443 371.521 435.501 372.39 436.761 372.905L499.332 398.482C501.539 399.384 504.063 399.005 505.908 397.496L506.348 397.136C510.875 393.432 513.5 387.892 513.5 382.043V380.486C513.5 377.504 511.47 374.904 508.576 374.181L487.884 369.007L468.241 364.588C467.98 364.529 467.725 364.449 467.478 364.348C464.546 363.15 463.551 359.495 465.472 356.977L467.6 354.187C468.641 352.822 470.12 351.856 471.789 351.45L487.33 347.669C487.723 347.574 488.124 347.51 488.528 347.479L498.241 346.732C502.918 346.372 507.386 344.569 510.474 341.729C513.55 338.9 515.251 335.051 514.463 330.529C513.644 325.829 511.918 321.487 509.618 318.354ZM510.424 317.762C512.834 321.045 514.608 325.54 515.448 330.358C516.303 335.265 514.438 339.442 511.151 342.465C507.875 345.478 503.184 347.354 498.318 347.729L488.604 348.476C488.254 348.503 487.907 348.558 487.566 348.641L472.025 352.421C470.579 352.773 469.298 353.611 468.395 354.794L466.267 357.583C464.746 359.578 465.533 362.473 467.856 363.422C468.052 363.502 468.254 363.566 468.461 363.612L488.121 368.036L508.819 373.21C512.158 374.045 514.5 377.045 514.5 380.486V382.043C514.5 388.192 511.74 394.016 506.981 397.91L506.541 398.27C504.412 400.012 501.5 400.448 498.954 399.407L436.383 373.83C434.947 373.244 433.677 372.227 432.964 370.792C429.815 364.453 426.828 352.097 434.702 342.721C435.343 341.959 436.166 341.398 437.045 341.003L499.418 312.959C500.028 312.685 500.713 312.455 501.445 312.437C504.923 312.348 508.019 314.487 510.424 317.762Z" fill="black"/>
<path d="M360.462 348.527C359.623 367.009 346.419 381.424 330.971 380.722C315.523 380.021 303.68 364.469 304.52 345.987C305.359 327.504 318.562 313.09 334.011 313.791C349.459 314.493 361.301 330.044 360.462 348.527Z" fill="black"/>
</g>
<defs>
<filter id="filter0_b_6_653" x="130.7" y="173.221" width="650.6" height="650.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="26.15"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6_653"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6_653" result="shape"/>
</filter>
<filter id="filter1_b_6_653" x="130.7" y="173.221" width="650.6" height="650.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="26.15"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6_653"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6_653" result="shape"/>
</filter>
<linearGradient id="paint0_linear_6_653" x1="60.914" y1="42.9585" x2="804.351" y2="881.526" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#20171B" stop-opacity="0.9"/>
</linearGradient>
<linearGradient id="paint1_linear_6_653" x1="473.041" y1="770.505" x2="473.041" y2="227.356" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD000"/>
<stop offset="0.52" stop-color="#FFCA40"/>
<stop offset="1" stop-color="#FFE0F9"/>
</linearGradient>
<linearGradient id="paint2_linear_6_653" x1="213.825" y1="740.696" x2="698.175" y2="256.346" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white" stop-opacity="0.49"/>
</linearGradient>
<linearGradient id="paint3_linear_6_653" x1="221.541" y1="268.463" x2="677.946" y2="756.465" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_6_653">
<rect width="904" height="907" rx="267" fill="white"/>
</clipPath>
</defs>
</svg>
