<svg width="1024" height="1024" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1_3)">
<rect width="1024" height="1024" rx="267" fill="url(#paint0_linear_1_3)"/>
<g style="mix-blend-mode:overlay">
<line x1="17.4026" y1="-3069" x2="17.4026" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="50.2617" y1="-3069" x2="50.2617" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="83.1208" y1="-3069" x2="83.1208" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="115.98" y1="-3069" x2="115.98" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="148.839" y1="-3069" x2="148.839" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="181.698" y1="-3069" x2="181.698" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="214.557" y1="-3069" x2="214.557" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="247.417" y1="-3069" x2="247.417" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="280.276" y1="-3069" x2="280.276" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="313.135" y1="-3069" x2="313.135" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="345.994" y1="-3069" x2="345.994" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="378.853" y1="-3069" x2="378.853" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="411.712" y1="-3069" x2="411.712" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="444.571" y1="-3069" x2="444.571" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="477.43" y1="-3069" x2="477.43" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="510.289" y1="-3069" x2="510.289" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="543.149" y1="-3069" x2="543.149" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="576.008" y1="-3069" x2="576.008" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="608.867" y1="-3069" x2="608.867" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="641.726" y1="-3069" x2="641.726" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="674.585" y1="-3069" x2="674.585" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="707.444" y1="-3069" x2="707.444" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="740.304" y1="-3069" x2="740.304" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="773.163" y1="-3069" x2="773.163" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="806.022" y1="-3069" x2="806.022" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="838.881" y1="-3069" x2="838.881" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="871.74" y1="-3069" x2="871.74" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="904.599" y1="-3069" x2="904.599" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="937.458" y1="-3069" x2="937.458" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="970.317" y1="-3069" x2="970.317" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="1003.18" y1="-3069" x2="1003.18" y2="3881" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="11.5" x2="-15.9948" y2="11.4999" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="39.5" x2="-15.9948" y2="39.4999" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="67.5" x2="-15.9946" y2="67.4999" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="95.5" x2="-15.9945" y2="95.4999" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="123.5" x2="-15.9945" y2="123.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="151.5" x2="-15.9944" y2="151.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="179.5" x2="-15.9944" y2="179.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="207.5" x2="-15.9941" y2="207.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="235.5" x2="-15.9941" y2="235.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="263.5" x2="-15.994" y2="263.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="291.5" x2="-15.994" y2="291.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="319.5" x2="-15.9939" y2="319.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="347.5" x2="-15.9939" y2="347.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="375.5" x2="-15.9937" y2="375.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="403.5" x2="-15.9937" y2="403.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="431.5" x2="-15.9935" y2="431.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="459.5" x2="-15.9935" y2="459.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="487.5" x2="-15.9935" y2="487.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="515.5" x2="-15.9933" y2="515.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="543.5" x2="-15.9933" y2="543.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="571.5" x2="-15.9933" y2="571.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="599.5" x2="-15.9932" y2="599.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="627.5" x2="-15.9932" y2="627.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="655.5" x2="-15.993" y2="655.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="683.5" x2="-15.993" y2="683.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="711.5" x2="-15.9928" y2="711.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="739.5" x2="-15.9928" y2="739.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="767.5" x2="-15.9927" y2="767.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="795.5" x2="-15.9927" y2="795.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="823.5" x2="-15.9926" y2="823.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="851.5" x2="-15.9926" y2="851.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="879.5" x2="-15.9922" y2="879.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="907.5" x2="-15.9922" y2="907.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="935.5" x2="-15.9921" y2="935.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="963.5" x2="-15.9921" y2="963.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="991.5" x2="-15.9919" y2="991.5" stroke="white" stroke-opacity="0.2"/>
<line x1="1626.96" y1="1019.5" x2="-15.9919" y2="1019.5" stroke="white" stroke-opacity="0.2"/>
</g>
<circle cx="539" cy="531.521" r="273" fill="url(#paint1_linear_1_3)"/>
<path d="M196.206 267.647C184.176 273.707 182.142 294.177 182.763 306.932C182.98 311.386 185.812 315.214 189.96 316.852C213.92 326.313 246.186 327.787 255.979 328.481C264.787 329.105 264.339 324.454 263.493 319.88C263.148 318.011 262.198 316.289 260.776 315.027L212.568 272.269C207.954 268.178 201.713 264.873 196.206 267.647Z" fill="white" stroke="black"/>
<path d="M305.971 160.544C293.123 154.358 279.891 167.481 271.949 177.767C269.257 181.255 268.693 185.871 270.138 190.034C280.064 218.641 297.012 266.106 304.132 273.728C310.69 280.748 322.672 273.372 328.238 268.037C329.803 266.537 330.283 264.316 329.899 262.182L313.962 173.644C313.008 168.341 310.826 162.881 305.971 160.544Z" fill="white" stroke="black"/>
<circle opacity="0.37" cx="376.5" cy="455.021" r="61.5" fill="#FF2E3C"/>
<circle opacity="0.37" cx="551.5" cy="461.021" r="67.5" fill="#FF2E3C"/>
<g filter="url(#filter0_b_1_3)">
<circle cx="522" cy="554.521" r="273" fill="url(#paint2_linear_1_3)"/>
<circle cx="522" cy="554.521" r="272" stroke="url(#paint3_linear_1_3)" stroke-width="2"/>
</g>
<path d="M565.623 369.415L503.25 397.459C502.424 397.83 501.667 398.35 501.085 399.043C493.406 408.186 496.293 420.291 499.412 426.57C500.06 427.874 501.224 428.817 502.572 429.368L565.143 454.945C567.52 455.916 570.237 455.509 572.224 453.883L572.665 453.523C577.307 449.724 580 444.042 580 438.043V436.486C580 433.274 577.814 430.475 574.698 429.695L554 424.521L534.351 420.1C534.117 420.047 533.888 419.976 533.667 419.885C531.04 418.812 530.148 415.536 531.87 413.28L533.997 410.491C534.969 409.216 536.35 408.314 537.907 407.936L553.448 404.155C553.815 404.066 554.189 404.007 554.566 403.978L564.28 403.23C573.822 402.496 582.598 395.872 580.955 386.444C579.297 376.925 573.954 368.77 567.458 368.937C566.82 368.953 566.205 369.154 565.623 369.415Z" fill="black" stroke="black"/>
<ellipse cx="398.491" cy="403.257" rx="28" ry="33.5" transform="rotate(2.60014 398.491 403.257)" fill="black"/>
</g>
<defs>
<filter id="filter0_b_1_3" x="196.7" y="229.221" width="650.6" height="650.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="26.15"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1_3"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1_3" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1_3" x1="69" y1="48.5" x2="908" y2="998" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#20171B" stop-opacity="0.9"/>
</linearGradient>
<linearGradient id="paint1_linear_1_3" x1="539.041" y1="826.505" x2="539.041" y2="283.356" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD000"/>
<stop offset="0.52" stop-color="#FFCA40"/>
<stop offset="1" stop-color="#FFE0F9"/>
</linearGradient>
<linearGradient id="paint2_linear_1_3" x1="279.825" y1="796.696" x2="764.175" y2="312.346" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white" stop-opacity="0.49"/>
</linearGradient>
<linearGradient id="paint3_linear_1_3" x1="287.541" y1="324.463" x2="743.946" y2="812.465" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_1_3">
<rect width="1024" height="1024" rx="267" fill="white"/>
</clipPath>
</defs>
</svg>
