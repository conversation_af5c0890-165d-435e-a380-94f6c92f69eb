{"hello": "<PERSON><PERSON><PERSON><PERSON>", "blinko": "Blinko", "notes": "Notlar", "resources": "<PERSON><PERSON><PERSON><PERSON>", "archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "settings": "<PERSON><PERSON><PERSON>", "total-tags": "TOPLAM ETİKET", "search": "Ara...", "i-have-a-new-idea": "Yeni bir <PERSON> var...", "note": "Not", "multiple-select": "Çoklu seçim", "convert-to": "Dönüş<PERSON>ür", "delete": "Sil", "recovery": "<PERSON><PERSON>", "archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "items": "öğeler", "your-changes-have-been-saved": "Değişiklikleriniz kaydedildi!", "operation-failed": "İşlem başarısız oldu.", "in-progress": "Devam ediyor...", "confirm-to-delete": "<PERSON><PERSON><PERSON>!", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, il<PERSON><PERSON><PERSON><PERSON> et<PERSON>ti kaldırır ve geri alı<PERSON>, lütfen <PERSON>aylayın", "add-tag": "Etiket Ekle", "cancel": "İptal", "no-data-here-well-then-time-to-write-a-note": "<PERSON><PERSON>da veri yok~", "basic-information": "<PERSON><PERSON>", "name": "İsim", "preference": "<PERSON><PERSON><PERSON>", "theme": "<PERSON><PERSON>", "change-type": "<PERSON><PERSON><PERSON><PERSON>", "insert-hashtag": "Hashtag ekle", "bulleted-list": "Madde işaretli liste", "numbered-list": "Numaralı liste", "check-list": "<PERSON><PERSON><PERSON>", "insert-table": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "insert-codeblock": "<PERSON><PERSON> b<PERSON><PERSON>", "insert-sandpack": "Sandpack ekle", "upload-file": "<PERSON><PERSON><PERSON>", "delete-confirm": "<PERSON><PERSON><PERSON>", "this-operation-will-be-delete-resource-are-you-sure": "Bu işlem kaynağı silecek, emin mi<PERSON>?", "delete-success": "Başar<PERSON><PERSON>", "update-successfully": "Başarıyla gü<PERSON>llendi", "create-successfully": "Başarıyla oluşturuldu", "total": "Toplam", "all-notes-have-been-loaded": "Tüm {{items}} notlar yüklendi", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "<PERSON><PERSON><PERSON><PERSON> {{name}}!, <PERSON><PERSON><PERSON> sizin için arayabi<PERSON>, bugün size nasıl yardımcı olabilirim?", "ask-about-your-notes": "Notlarınız hakkında soru sorun", "use-ai": "<PERSON><PERSON><PERSON> zekayı kullan", "model-provider": "Model <PERSON>", "api-endpoint": "API Uç Noktası", "must-start-with-http-s-or-use-api-openai-as-default": "http(s):// ile başlamalı veya varsayılan olarak /api/openai kullanılmalı", "user-custom-openai-api-key": "Kullanıcıya özel OpenAI API Anahtarı", "user-custom-azureopenai-api-instance": "Azure OpenAI örnek adı", "user-custom-azureopenai-api-deployment": "Azure OpenAI dağıtım adı", "user-custom-azureopenai-api-version": "API sürümü", "ai-model": "<PERSON><PERSON><PERSON>", "logout": "Çıkış yap", "user-or-password-error": "Kullanıcı adı veya şifre hatası", "username": "Kullanıcı Adı", "enter-your-name": "Adınızı girin", "password": "Şifre", "enter-your-password": "Şifrenizi girin", "need-to-create-an-account": "<PERSON>sap oluşturmanız mı gerekiyor?", "sign-up": "<PERSON><PERSON><PERSON>", "sign-in": "<PERSON><PERSON><PERSON> yap", "nickname": "Tak<PERSON> ad", "change-user-info": "Kullanıcı bilgilerini değiştir", "rest-user-password": "Kullanıcı şifresini sıfırla", "confirm-password": "<PERSON><PERSON><PERSON><PERSON>", "confirm-your-password": "Şifrenizi onaylayın", "enter-your-username": "Kullanıcı adınızı girin", "save": "<PERSON><PERSON>", "keep-sign-in": "O<PERSON><PERSON>u açık tut", "required-items-cannot-be-empty": "Z<PERSON>unlu al<PERSON> boş o<PERSON>az", "the-two-passwords-are-inconsistent": "<PERSON><PERSON> şif<PERSON>", "create-successfully-is-about-to-jump-to-the-login": "Başarı<PERSON>, <PERSON><PERSON><PERSON>ö<PERSON>diriliyorsunuz", "already-have-an-account-direct-login": "Zaten bir hesabınız var mı? Do<PERSON>rudan giriş yapın", "no-tag-found": "Etiket bulunamadı", "new-version-detected-click-to-get-the-latest-version": "🎉 Yeni sürüm tespit edildi, en son sürü<PERSON><PERSON> almak için tıklayın", "schedule-task": "<PERSON><PERSON><PERSON><PERSON>", "schedule-back-up": "<PERSON><PERSON><PERSON><PERSON>", "every-day": "Her gün", "every-week": "Her hafta", "every-month": "Her ay", "every-three-month": "Her üç ay", "every-half-year": "Her altı ay", "import": "İçe aktar", "import-from-bko": ".bko dosyasından içe aktar", "not-a-bko-file": "Bu bir bko dosyası değil", "convert-to-note": "<PERSON><PERSON>", "convert-to-blinko": "Blinko'ya dönüştür", "reviewed": "İncelendi", "congratulations-youve-reviewed-everything-today": "<PERSON><PERSON><PERSON><PERSON>, bug<PERSON>n her <PERSON>eyi incelediniz.", "name-db": "İSİM", "schedule": "PLAN", "last-run": "SON ÇALIŞMA", "backup-file": "YEDEK DOSYA", "status": "DURUM", "running": "Çalışıyor", "stopped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "show-navigation-bar-on-mobile": "<PERSON><PERSON><PERSON> gez<PERSON> gizle", "schedule-archive-blinko": "Blink<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "there-are-no-resources-yet-go-upload-them-now": "<PERSON><PERSON><PERSON><PERSON> kaynak yok, <PERSON><PERSON><PERSON>", "confrim": "<PERSON><PERSON><PERSON>", "daily-review": "Günlük İnceleme", "detail": "Detay", "enter-send-shift-enter-for-new-line": "Enter ile g<PERSON>, <PERSON><PERSON>+Enter ile yeni satır", "show-less": "<PERSON><PERSON>", "show-more": "<PERSON><PERSON>", "top": "Üst", "cancel-top": "Üstü İptal Et", "created-in": "Oluş<PERSON><PERSON><PERSON><PERSON><PERSON> yer", "set-as-public": "Herkese açık yap", "unset-as-public": "Herkese açık olmaktan <PERSON>", "with-link": "Bağlantılı", "no-tag": "Etiket Yok", "has-file": "<PERSON><PERSON><PERSON>", "created-at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updated-at": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "role": "Rol", "user-list": "Kullanıcı Listesi", "create-user": "Kullanıcı Oluştur", "action": "<PERSON><PERSON><PERSON>", "original-password": "Orijinal Şifre", "edit-user": "Kullanıcıyı Düzenle", "import-from-memos-memos_prod-db": "Memos'tan içe aktar (memos_prod.db)", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "Memos_prod.db dos<PERSON><PERSON><PERSON><PERSON> dışa a<PERSON>, veri kaybını önlemek için lütfen Memos kapsayıcısını kapatın.", "go-to-share-page": "Paylaşım say<PERSON>ına git", "import-done": "İçe aktarma tamamlandı", "rebuilding-embedding-progress": "Yerleştirme Yeniden İnşa İlerlemesi", "rebuild-embedding-index": "Yerleştirme İndeksini Yeniden İnşa Et", "rebuild": "Yeniden İnşa Et", "notes-imported-by-other-means-may-not-have-embedded-vectors": "Başka yollarla içe aktarılan notlarda gömülü vektörler olmayabilir", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "Çok sayıda notunuz varsa belirli bir sayıda jeton tüketebilirsiniz", "order-by-create-time": "Oluşturma zamanına göre sı<PERSON>a", "time-format": "Zaman Formatı", "version": "S<PERSON>r<PERSON><PERSON>", "new-version-available": "<PERSON><PERSON> sü<PERSON>üm mevcut", "storage": "<PERSON><PERSON><PERSON>", "local-file-system": "<PERSON><PERSON>", "object-storage": "<PERSON><PERSON><PERSON>", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "GPT modeline e<PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>ğrılabilmesi gerektiği de sağlanmalıdır:", "speech-recognition-requires-the-use-of": "Konuşma tanıma şu şekilde kullanılmasını gerektirir:", "ai-expand": "Yapay Zeka ile Genişlet", "ai-polish": "Yapay Zeka ile Parlat", "accept": "Kabul Et", "reject": "<PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON>", "card-columns": "<PERSON><PERSON>", "select-a-columns": "B<PERSON> sütun seç", "width-less-than-1024px": "Genişlik 1024 pikselden az", "width-less-than": "Genişlik şundan az", "small-device-card-columns": "Küçük Cihaz Kart Sütunları", "medium-device-card-columns": "Orta Cihaz Kart Sütunları", "large-device-card-columns": "Büyük Cihaz Kart Sütunları", "device-card-columns": "Cihaz Kart Sütunları", "columns-for-different-devices": "Farklı cihazlar için <PERSON>", "mobile": "Mobil", "tablet": "Tablet", "desktop": "Masaüstü", "chars": "<PERSON><PERSON><PERSON><PERSON>", "text-fold-length": "<PERSON><PERSON>", "title-first-line-of-the-text": "Başlık (metnin ilk satırı)", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "İçerik (metin uzunluğu aşıyorsa geri kalan metin)", "ai-tag": "Yapay Zeka Etiketi", "article": "Makale", "embedding-model": "Yerle<PERSON><PERSON>rm<PERSON>i", "force-rebuild": "Zorla Yeniden İnşa Et", "force-rebuild-embedding-index": "<PERSON><PERSON>la yeniden inşa, indekslenmiş tüm verileri tamamen yeniden oluşturur", "embedding-model-description": "Yerleştirme modelleri değiştirildikten sonra indeks yeniden inşa edilmelidir", "top-k-description": "Sonuçta döndürülecek maksimum belge sayısı", "embedding-score-description": "Sorgular için ben<PERSON> eşiği genellikle Öklid toplam mesafesidir", "embedding-lambda-description": "Sorgu Sonucu Çeşitlilik Ağırlık Parametresi", "update-tag-icon": "Etiket simgesini güncelle", "delete-only-tag": "Yalnızca Etiketi Sil", "delete-tag-with-note": "Not ile birlikte etiketi sil", "update-name": "<PERSON><PERSON><PERSON>", "update-tag-name": "Etiket İsmini Güncelle", "thinking": "Düşünüyor...", "select-all": "Tümünü Seç", "deselect-all": "Hiçbirini Seçme", "ai-emoji": "Yapay Z<PERSON>ji<PERSON>", "custom-icon": "<PERSON><PERSON>", "ai-enhanced-search": "Yapay Zeka ile Geliştirilmiş Arama", "preview-mode": "<PERSON><PERSON><PERSON><PERSON>", "source-code": "<PERSON><PERSON><PERSON>", "camera": "<PERSON><PERSON><PERSON>", "reference": "Referans", "reference-note": "<PERSON><PERSON><PERSON>", "source-code-mode": "<PERSON><PERSON><PERSON>", "heading": "Başlık", "paragraph": "Paragra<PERSON>", "remove-bold": "Kalınlığı Kaldır", "remove-italic": "İtalik Yazıyı Kaldır", "underline": "Altı Çizili", "remove-underline": "Alt Çizgiyi Kaldır", "select-block-type": "Blok Türünü Seç", "block-type-select-placeholder": "Blok Türü", "trash": "<PERSON><PERSON><PERSON><PERSON>", "light-mode": "Açık Mod", "dark-mode": "<PERSON><PERSON>", "follow-system": "<PERSON><PERSON>mi <PERSON>", "custom-path": "<PERSON><PERSON>", "page-size": "<PERSON><PERSON>", "toolbar-visibility": "<PERSON><PERSON> Görünürlüğü", "select-toolbar-visibility": "<PERSON><PERSON> görünürlüğünü seç", "always-show-toolbar": "Her Zaman Göster", "hide-toolbar-on-mobile": "Mo<PERSON><PERSON> Gizle", "always-hide-toolbar": "Her Zaman Gizle", "select-a-time-format": "Bir zaman formatı seç", "enter-code-shown-on-authenticator-app": "Kimlik doğrulama uygulamasında gösterilen kodu girin", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "Üçüncü taraf kimlik doğrulama uygulamanızı açın ve ekranda gösterilen kodu girin", "two-factor-authentication": "İki Faktörlü Kimlik Doğrulama", "scan-this-qr-code-with-your-authenticator-app": "Bu QR kodunu kimlik doğrulama uygulamanızla tarayın", "or-enter-this-code-manually": "<PERSON><PERSON>a bu kodu manuel o<PERSON>ak girin:", "verify": "<PERSON><PERSON><PERSON><PERSON>", "2fa-setup-successful": "2FA kurulumu başarılı", "about": "Hakkında", "days": "<PERSON><PERSON><PERSON><PERSON>", "select-model-provider": "Model Sağlayı<PERSON><PERSON> Seç", "select-model": "<PERSON> <PERSON><PERSON>", "allow-register": "Kayıt İzin Ver", "access-token": "<PERSON><PERSON><PERSON><PERSON>", "bucket": "<PERSON><PERSON>", "region": "<PERSON><PERSON><PERSON>", "access-key-secret": "<PERSON><PERSON><PERSON><PERSON> sırrı", "access-key-id": "<PERSON><PERSON><PERSON><PERSON> kimliğ<PERSON>", "copy-share-link": "Paylaşım bağlantısını kopyala", "share-and-copy-link": "Paylaş ve bağlantıyı kopyala", "endpoint": "Uç Nokta", "export-format": "Dışa Aktarma Formatı", "time-range": "Zaman Aralığı", "all": "Tümü", "exporting": "Dışa aktarılıyor...", "tag-status": "Etiket Durumu", "all-notes": "<PERSON><PERSON><PERSON>", "with-tags": "<PERSON><PERSON><PERSON><PERSON>", "without-tags": "Etiketsiz", "select-tags": "Etiketleri Seç", "additional-conditions": "<PERSON><PERSON>", "apply-filter": "Filtre Uygula", "has-image": "<PERSON><PERSON><PERSON><PERSON>", "has-link": "Bağlantılı", "filter-settings": "Filtre Ayarları", "to": "<PERSON><PERSON>", "reset": "Sıfırla", "start-date": "Başlangıç <PERSON>", "end-date": "Bitiş Tarihi", "no-condition": "Koşul Yok", "public": "Herkese Açık", "exclude-tag-from-embedding": "Etiketli İçeriği Hariç Tut", "exclude-tag-from-embedding-tip": "Bu etikete sahip notlar yapay zeka yerleştirme işleminden hariç tutula<PERSON>k", "exclude-tag-from-embedding-desc": "Ya<PERSON>y zeka yerleştirme vektör üretiminden hariç tutulacak bir etiket seçin", "ai-model-tooltip": "Kullanılacak model adı<PERSON><PERSON> girin, örneğin gpt-3.5-turbo, gpt-4, gpt-4o, gpt-4o-mini", "user-custom-azureopenai-api-deployment-tooltip": "Kullanılacak dağıtım adını girin, örneğin gpt-4o", "ollama-ai-model-tooltip": "Kullanılacak model ad<PERSON><PERSON><PERSON> girin, <PERSON><PERSON><PERSON><PERSON> llama3.2", "ollama-default-endpoint-is-http-localhost-11434": "Ollama varsayılan uç noktası http://localhost:11434", "your-azure-openai-instance-name": "Azure OpenAI örnek adınız", "align-center": "<PERSON><PERSON><PERSON>", "align-left": "<PERSON><PERSON> Hizala", "align-right": "<PERSON><PERSON><PERSON>", "alternate-text": "<PERSON><PERSON><PERSON><PERSON> metin", "bold": "Kalı<PERSON>", "both": "<PERSON><PERSON><PERSON> birden", "check": "<PERSON><PERSON><PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "code": "<PERSON><PERSON>", "code-theme": "<PERSON><PERSON>", "column": "<PERSON><PERSON><PERSON>", "comment": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "content-theme": "İçerik Tema Önizlemesi", "copied": "Kopyalandı", "copy": "Kopyala", "delete-column": "Satırı Sil", "delete-row": "<PERSON><PERSON><PERSON><PERSON>", "devtools": "Geliştirici Araçları", "down": "Aşağı", "download-tip": "Tarayıcı indirme fonksiyonunu desteklemiyor", "edit": "<PERSON><PERSON><PERSON><PERSON>", "edit-mode": "<PERSON><PERSON>zen<PERSON><PERSON>", "emoji": "<PERSON><PERSON><PERSON>", "export": "Dışa Aktar", "file-type-error": "<PERSON><PERSON><PERSON> türü hatası", "footnote-ref": "Dipnot Referansı", "fullscreen": "<PERSON>", "generate": "Oluşturuluyor", "headings": "Başlıklar", "heading1": "Başlık 1", "heading2": "Başlık 2", "heading3": "Başlık 3", "heading4": "Başlık 4", "heading5": "Başlık 5", "heading6": "Başlık 6", "help": "Yardım", "image-url": "resim URL'si", "indent": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON>", "inline-code": "Satır İçi Kod", "insert-after": "<PERSON><PERSON><PERSON><PERSON>ı<PERSON> e<PERSON>", "insert-before": "<PERSON><PERSON><PERSON>", "insert-column-left": "Sola 1 ekle", "insert-column-right": "Sağa 1 ekle", "insert-row-above": "Yukarıya 1 ekle", "insert-row-below": "Aşağıya 1 ekle", "instant-rendering": "Anlık Görüntüleme", "italic": "İtalik", "language": "Dil", "line": "Satır", "link": "Bağlantı", "link-ref": "Bağlantı Referansı", "list": "Liste", "more": "<PERSON><PERSON>", "name-empty": "<PERSON><PERSON><PERSON> bo<PERSON>", "ordered-list": "Sıralı Liste", "outdent": "<PERSON><PERSON><PERSON><PERSON>", "outline": "<PERSON><PERSON>", "over": "üzerinde", "performance-tip": "Gerçek zamanlı önizleme ${x}ms gerektirir, kapatabilirsiniz", "preview": "<PERSON><PERSON><PERSON><PERSON>", "quote": "Alıntı", "record": "Kaydı Başlat/Kaydı Bitir", "record-tip": "Cihaz kayıt yapmayı desteklemiyor", "recording": "kaydediyor...", "redo": "<PERSON><PERSON>", "remove": "Kaldır", "row": "Satır", "spin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "split-view": "Bölünmüş Görünüm", "strike": "Üstü Çizili", "table": "<PERSON><PERSON><PERSON>", "text-is-not-empty": "metin(b<PERSON><PERSON>)", "title": "Başlık", "tooltip-text": "<PERSON><PERSON>", "undo": "<PERSON><PERSON>", "up": "Yukarı", "update": "<PERSON><PERSON><PERSON><PERSON>", "upload": "<PERSON>sim veya dosya y<PERSON>", "upload-error": "<PERSON><PERSON><PERSON><PERSON> hatası", "uploading": "yükleniyor...", "wysiwyg": "WYSIWYG", "search-tags": "Etiketleri Ara", "insert-attachment-or-note": "Eke mi yoksa nota mı eklemek istiyorsunuz?", "paste-to-note-or-attachment": "Bağlama mı yoksa eke mi yapıştırmak istediğinizden emin misiniz?", "context": "Bağlam", "attachment": "Ek", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "<PERSON><PERSON>e işleminden sonra tüm kullanıcı verileri temizlenecek ve geri alınamayacak.", "upload-completed": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>ı", "upload-cancelled": "<PERSON><PERSON><PERSON><PERSON> iptal edildi", "upload-failed": "Yükleme başarısız oldu", "import-from-bko-tip": "<PERSON><PERSON> anda kurtarma için s3'e yükleme desteklenmemektedir. Kurtarma yapmak istediğinizde lütfen s3 seçeneğini geçici olarak devre dışı bırakın.", "music-settings": "Müzik <PERSON>ları", "spotify-consumer-key": "Spotify API Anahtarı", "spotify-consumer-secret": "Spotify API Sırrı", "enter-spotify-consumer-key": "Spotify API Anahtarını Girin", "enter-spotify-consumer-secret": "Spotify API Sırrını Girin", "spotify-consumer-key-tip": "Mp3 müzik kapağını almak için kullanılır", "spotify-consumer-key-tip-2": "API Anahtarını https://developer.spotify.com/ adresinden alın", "edit-time": "Düzenleme <PERSON>", "ai-write": "Yapay Zeka ile Yaz", "download": "<PERSON><PERSON><PERSON>", "rename": "<PERSON><PERSON><PERSON>", "move-up": "Yukarı Taşı", "cut": "<PERSON><PERSON>", "paste": "Yapıştır", "confirm-delete-content": "{{name}} öğesini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "confirm-delete": "<PERSON><PERSON><PERSON>", "folder-name": "Klasör <PERSON>", "file-name": "<PERSON><PERSON><PERSON>", "operation-success": "İşlem Başarılı", "cloud-file": "Bulut Dosyası", "move-to-parent": "Üst Klasöre Taşı", "no-resources-found": "Kaynak Bulunamadı", "operation-in-progress": "İşlem devam ediyor", "new-folder": "<PERSON><PERSON>", "folder-name-required": "Klasör Adı Gerekli", "folder-name-exists": "Klasör Adı Zaten Var", "show-all": "Tümünü <PERSON>ö<PERSON>", "collapse": "Dar<PERSON><PERSON>", "sun": "Paz", "mon": "Pzt", "tue": "Sal", "thu": "Per", "fri": "Cum", "sat": "Cmt", "wed": "Çar", "heatMapTitle": "Geçen yıldaki notların ısı haritası", "heatMapDescription": "Her gün o<PERSON>n not sayısını gösterir", "select-month": "<PERSON><PERSON>", "note-count": "Not Sayısı", "total-words": "Toplam Kelime", "max-daily-words": "<PERSON><PERSON><PERSON><PERSON> Günlük Kelime", "active-days": "<PERSON><PERSON><PERSON>", "analytics": "<PERSON><PERSON><PERSON>", "tag-distribution": "Etiket Dağılımı", "other-tags": "<PERSON><PERSON><PERSON>", "offline-status": "Çevrimdışı Mod", "offline-title": "Çevrimdışısınız", "offline-description": "Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin", "retry": "<PERSON><PERSON><PERSON>", "back-to-home": "<PERSON>", "offline": "Çevrimdışı", "close-background-animation": "Arka Plan Animasyonunu Kapat", "custom-background-url": "Özel Arka Plan", "custom-bg-tip": "Kendi degrade arka planınızı oluşturmak için https://www.shadergradient.co/ adresine gidin", "share": "Paylaş", "need-password-to-access": "<PERSON><PERSON><PERSON><PERSON>", "password-error": "<PERSON><PERSON><PERSON>", "create-share": "Paylaşım Oluştur", "cancel-share": "Paylaşımı İptal Et", "share-link": "Paylaşım Bağlantısı", "set-access-password": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "protect-your-shared-content": "Paylaşılan içeriğinizi koruyun", "access-password": "<PERSON><PERSON><PERSON><PERSON>", "select-date": "<PERSON><PERSON><PERSON>", "expiry-time": "<PERSON>", "select-expiry-time": "<PERSON>", "permanent-valid": "Kalıcı Geçerli", "7days-expiry": "7 <PERSON>ün Son <PERSON>", "30days-expiry": "30 <PERSON><PERSON><PERSON>", "custom-expiry": "<PERSON><PERSON>", "share-link-expired": "Paylaşım Bağlantısı Süresi Doldu", "share-link-expired-desc": "Bu paylaşımın süresi doldu, lütfen yeniden paylaşım için yöneticiyle iletişime geçin!", "shared": "Paylaşıldı", "internal-shared": "<PERSON><PERSON><PERSON>", "edited": "D<PERSON><PERSON>lendi", "move-down": "Aşağı Taşı", "provider-id": "Sağlayıcı Kimliği", "provider-name": "Sağlayıcı Adı", "well-known-url": "Bilinen URL", "authorization-url": "Yetkilendirme URL'si", "token-url": "Jeton URL'si", "userinfo-url": "Kullanıcı Bilgi URL'si", "scope": "<PERSON><PERSON><PERSON>", "client-id": "Müşteri Kimliği", "client-secret": "Müşteri Sırrı", "sso-settings": "SSO Ayarları", "oauth2-providers": "OAuth2 Sağlayıcıları", "add-oauth2-provider": "OAuth2 Sağlayıcı Ekle", "add-provider": "Sağlayıcı Ekle", "edit-oauth2-provider": "OAuth2 Sağlayıcıyı Düzenle", "confirm-delete-provider": "Sağlayıcı Silme İşlemini Onayla", "provider-icon": "Sağlayıcı Simgesi", "please-select-icon-from-iconify": "Lütfen iconify'dan simge seçin", "provider-template": "Sağlayıcı Şablonu", "select-provider-template": "Sağlayıcı Şablonu Seç", "please-add-this-url-to-your-oauth-provider-settings": "Lütfen bu URL'yi OAuth sağlayıcı ayarlarınıza ekleyin", "redirect-url": "Yönlendirme URL'si", "sign-in-with-provider": "{{ provider }} ile giri<PERSON> yap", "community": "Topluluk", "theme-color": "<PERSON><PERSON>", "rest-user-info": "Kullanıcı bilgisini sıfırla", "link-account": "Hesabı Bağla", "select-account": "<PERSON><PERSON><PERSON>", "link-account-warning": "Hesaplarınızı bağ<PERSON>z, mevcut hesaptaki verilerin bağlı hesaba senkronize edilmeyeceğini lütfen unutmayın.", "unlink-account": "Hesap Bağlantısını Kaldır", "unlink-account-tips": "Bu hesapla tüm ilişkileri kaldırmayı onaylıyor musunuz?", "login-type": "<PERSON><PERSON><PERSON>", "close-daily-review": "Günlük İncelemeyi Kapat", "max-home-page-width": "<PERSON><PERSON><PERSON><PERSON> Ana Sayfa Genişliği", "max-home-page-width-tip": "0 olarak ayarlanırsa maksimum genişlik olur", "reply-to": "Şuna Yanıt Ver", "author": "<PERSON><PERSON>", "from": "<PERSON><PERSON>", "no-comments-yet": "<PERSON><PERSON><PERSON><PERSON> yorum yok", "hub": "<PERSON><PERSON><PERSON>", "home-site": "Ana Site", "use-blinko-hub": "<PERSON><PERSON><PERSON>", "full-screen": "<PERSON>", "exit-fullscreen": "Tam Ekrandan Çık", "no-note-associated": "İlişkili not yok", "insert-context": "Bağ<PERSON><PERSON>", "follow": "Takip Et", "follower": "Takipçi", "following": "<PERSON><PERSON><PERSON>", "admin": "Web Yöneticisi", "site-url": "Blinko Site URL'si", "unfollow": "Takibi Bırak", "join-hub": "Merkeze Katıl", "refresh": "<PERSON><PERSON><PERSON>", "comment-notification": "<PERSON><PERSON>", "follow-notification": "Ta<PERSON><PERSON>", "followed-you": "sizi takip etti", "mark-all-as-read": "Tümünü Okundu Olarak İşaretle", "no-notification": "<PERSON><PERSON><PERSON><PERSON> yok", "new-notification": "<PERSON><PERSON>", "notification": "<PERSON><PERSON><PERSON><PERSON>", "system-notification": "Sistem Bildirimi", "backup-success": "Yedekleme Başarılı🎉", "embedding-api-endpoint": "Yerleştirme API Uç Noktası", "embedding-api-key": "Yerleştirme API Anahtarı", "recommand": "<PERSON><PERSON>", "has-todo": "Yapılacaklar Var", "reference-by": "Tarafından Referans Verildi", "hide-notification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search-settings": "<PERSON><PERSON>...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "Bu işlem seçilen dosyaları silecek ve geri alınamayacak, lütfen onaylayın", "plugin-settings": "Eklenti Ayarı", "installed-plugins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marketplace": "<PERSON><PERSON>", "local-development": "<PERSON><PERSON>", "local-plugin": "<PERSON><PERSON>", "add-local-plugin": "<PERSON><PERSON>", "uninstall": "Kaldır", "install": "<PERSON><PERSON><PERSON>", "downloads": "<PERSON><PERSON>rmeler", "plugin-updated": "<PERSON><PERSON><PERSON>", "plugin-update-failed": "Eklenti Güncelleme Ba<PERSON>arısız", "plugin-connection-failed": "Eklenti Bağlantısı Başarısız", "disconnect": "Bağlantıyı Kes", "local-development-description": "<PERSON><PERSON> bir geliştirme eklentisi ekleyin ve hata ayıklaması yapın.", "ai": "<PERSON><PERSON><PERSON>", "embedding-dimensions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "embedding-dimensions-description": "Model boy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> doğru olduğundan emin o<PERSON><PERSON>z gerekir ve değişikliklerden sonra kayıtların zorla yeniden indekslenmesi gerekir", "ai-chat-box-notes": "Aşağ<PERSON>da sizin için alınan ilgili notlar yer alıyor", "add-to-blinko": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add-to-note": "<PERSON><PERSON>", "no-title": "Başlık Yok", "search-blinko-content-or-help-create": "Blinko içeriğini ara veya oluşturmaya yardım et...", "conversation-history": "Konuşma geçmişi", "new-conversation": "<PERSON><PERSON>", "knowledge-base-search": "Bilgi tabanı araması", "add-tools-to-model": "Çevrimiçi ara veya yapay zekanın Blinko API'sini çağırmasına izin ver", "clear-current-content": "Mevcut içeriği temizle", "welcome-to-blinko": "<PERSON><PERSON>, {{name}}", "content-generated-by-ai": "YAPAY ZEKA TARAFINDAN OLUŞTURULAN İÇERİK", "ai-prompt-writing": "Siz bir profesyonel yazar<PERSON>ı<PERSON>ız, lütfen kullanıcı tarafından sağlanan konu hakkında profesyonel bir makale yazın.", "ai-prompt-coding": "Siz bir profesyonel kod yazarısınız, lütfen kullanıcı tarafından sağlanan konuyla ilgili basit bir Python programı yazın.", "ai-prompt-translation": "<PERSON>z bir profesy<PERSON>l çevir<PERSON>siniz, lütfen kullanıcı tarafından sağlanan metni {{lang}} diline ç<PERSON>n", "writing": "<PERSON><PERSON><PERSON>", "coding": "Kodlama", "translation": "<PERSON>ev<PERSON>", "total-tokens": "Toplam jeton", "first-char-delay": "İlk karakter gecikmesi", "check-connect": "Bağlantıyı Kontrol Et", "check-connect-success": "Bağlantı Kontrolü Başarılı", "check-connect-error": "Bağlantı hatası, /v1 sonuna eklenebilir", "loading": "Yükleniyor", "model": "Model", "ai-tools": "Yapay Zeka Araçları", "tavily-max-results": "<PERSON><PERSON>", "tavily-api-key": "Tavily Arama API Anahtarı", "ai-prompt-writing-content": "200 kelimelik bir makale yazın ve notlarınıza kaydedin", "ai-prompt-coding-content": "https://github.com/blinko-space/blinko web içeriğini çıkarın", "rebuild-in-progress": "Ye<PERSON>den inşa devam ediyor", "processing": "İşleniyor", "stop-task": "<PERSON><PERSON><PERSON><PERSON>", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "Bir yeniden inşa görevi devam ediyor, yeniden ba<PERSON><PERSON>mak istiyor musunuz?", "hide-blog-images": "Blog resimlerini gizle", "ai-prompt-translation-content": "Son iki gün içindeki etiketsiz notları kontrol edin ve etiketleyin.", "ai-prompt-delete-content": "2 ar<PERSON><PERSON><PERSON><PERSON><PERSON> not bulun, <PERSON><PERSON><PERSON>in ve yeni notlar olarak ka<PERSON>, ardı<PERSON>n bu iki ar<PERSON><PERSON>lenmiş notu silin", "newer": "<PERSON><PERSON>", "older": "<PERSON><PERSON>", "restore-this-version": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> geri <PERSON>", "Note History": "Not Geçmişi", "View History Versions": "Geçmiş Sürümleri Görüntüle", "history-note-only": "Dikkat: <PERSON><PERSON> geçmiş yalnızca metin içeriğini içerir, dosya geçmişi da<PERSON>", "referenceResource": "Referans kaynağı", "to-ask-ai": "AI sormak için", "press-enter-to-select-first-result": "İlk Sonucu seçmek için Enter tuşuna basın", "ask-ai": "AI'ya sor", "ask-blinko-ai-about-this-query": "<PERSON><PERSON><PERSON>ye bu sorgu hakkında sorun", "search-or-ask-ai": "<PERSON><PERSON>, a<PERSON><PERSON> veya AI sorun ...", "plugin": "Eklenti", "editor-preview": "<PERSON><PERSON><PERSON>", "auto-add-tags": "Otomatik etiket ekle", "add-as-comment": "<PERSON><PERSON> o<PERSON> e<PERSON>", "choose-what-to-do-with-ai-results": "AI sonuçlarıyla ne yapacağınızı seçin", "ai-post-processing-mode": "AI İşleme Modu Post", "ai-post-processing-prompt": "Yapay Zeka ile ilgili yorum istemi sonrası işleme", "ai-generate-emoji": "", "ai-generating-emoji": "", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> not içeriğini analiz edin ve uygun etiketleri önerin ve kısa bir özet sağlayın", "api-key": "", "date-range": "", "days-ago": "", "define-custom-prompt-for-ai-to-process-notes": "AI'yı mevcut not üzerine yorum yapmak üzere çalıştırın. Örneğin: Lütfen notun içeriğini özetleyin. <PERSON>ğer not içeriği 10 kelimeden az ise, lütfen onu benim için düzenleyin.", "enter-custom-prompt-for-post-processing": "İşleme sonrası özel istemi girin", "enter-your-api-key": "", "hours-ago": "", "impoort-from-bko": "", "minutes-ago": "", "months-ago": "", "prompt-used-for-post-processing-notes": "İşleme Sonrası Notlar için <PERSON>ılan İstem", "setting": "", "superadmin": "", "user": "", "weeks-ago": "", "years-ago": "", "enable-ai-post-processing": "AI sonrası işlemeyi etkinleştirin", "automatically-process-notes-after-creation-or-update": "Oluşturulduktan sonra notları otomatik olarak işleyin", "can-generate-summaries-tags-or-perform-analysis": "<PERSON><PERSON><PERSON> g<PERSON>ekleştirmede özet etiketler oluşturabilir", "ai-post-processing": "AI Post işleme", "model-list-updated": "<PERSON> <PERSON><PERSON>", "to-search-tags": "Etiketleri aramak", "app-upgrade-required": "Uygulama g<PERSON><PERSON> gere<PERSON>yor", "current-app-version": "Mevcut APP sürümü", "required-app-version": "Gerekli APP sürümü", "upgrade": "Yükseltme", "online-search": "Çevrimiçi <PERSON>", "smart-edit": "Akıllı Düzenleme", "function-call-required": "Fonksiyon Çağrısı Gerekli", "smart-edit-prompt": "Akıllı Düzenleme İsteği", "define-instructions-for-ai-to-edit-your-notes": "Notlarda değişiklik yapmak için komutları kullanabilirsiniz, örneğin: Bir not içinde bir bağlantı varsa, orijinal notun altına bağlantı içeriğini özetleyin ve bir etiket oluşturun.", "rebuild-started": "Yeniden İnşa Başladı", "rebuild-stopped-by-user": "Kullanıcı tarafından yeniden yapılandırma durduruldu.", "random-mode": "<PERSON><PERSON><PERSON><PERSON>", "related-notes": "İl<PERSON><PERSON> notlar", "no-related-notes-found": "<PERSON><PERSON> ilgili not bulunamadı", "advanced": "İleri", "rerank-model-description": "Arama doğruluğunu artırmak için vektör sonuçlarını yeniden sıralamak için bir model beli<PERSON>in.", "rerank-model": "<PERSON><PERSON><PERSON> s<PERSON> modeli", "rerank": "<PERSON><PERSON><PERSON>", "use-custom-rerank-endpoint-description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gömülü modelin uç noktaları ve API anahtarları yeniden sıralanacaktır.", "use-embedding-endpoint": "Katıştırma uç noktasını kullanın", "rerank-score-description": "Yeniden sıralama modeli için bir puan eşiği beli<PERSON>, bunun altındaki sonuçlar filtrelenecektir.", "public-share": "Halka Açık Pay", "internal-share": "<PERSON><PERSON>", "no-team-members-found": "Takım üyesi bulunamadı", "selected-users": "Seçilen k<PERSON>anı<PERSON>ı<PERSON>", "tags-prompt": "Etiketler İstemi", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "Otomatik etiket oluşturmak için kullanılan komut. <PERSON><PERSON><PERSON> bo<PERSON> bı<PERSON>ılırsa, varsayılan komut kullanılacaktır.", "generate-low-permission-token": "Düşük İzinli Token Oluştur", "low-permission-token-desc": "Düşük izinli belirteçler​ sadece ​upsertNote uç noktasına​ ve ​AI sohbet uç noktasına​ erişebilir. Hesap bilgilerinize veya diğer notlara ​erişemezler. Bu, ​Telegram botları​ veya ​WeChat botları gibi durumlar için idealdir, burada onların başka hiçbir notlara ​erişememesini garanti etmek istersiniz.", "this-token-is-only-displayed-once-please-save-it-properly": "<PERSON>u beli<PERSON>ç sadece bir kez gö<PERSON><PERSON><PERSON><PERSON><PERSON>ir, lütfen düzgün bir <PERSON><PERSON><PERSON> ka<PERSON>.", "refresh-model-list": "Model list<PERSON> alın", "please-set-the-embedding-model": "Lütfen yerleşik modeli ayarlayın", "blinko-endpoint": "Blinko uç noktası", "enter-blinko-endpoint": "Blinko'nun dağıtıldığı URL'niz", "login-failed": "Oturum açma başarısız oldu", "verification-failed": "Kimlik Doğrulama Başarısız", "download-success": "İndirme başarılı", "download-failed": "İndirme başarısız oldu", "downloading": "İndiriliyor", "hide-pc-editor": "Gizli PC tarafı düzenleyicisi", "import-from-markdown": "Markdown dosyasından içe aktar", "import-from-markdown-tip": ".md dosyasından veya .md dosyalarını içeren .zip arşivinden içe aktar", "not-a-markdown-or-zip-file": "Markdown veya zip dosyası değil. Lütfen .md veya .zip dosyalarını seçin.", "todo": "<PERSON><PERSON><PERSON><PERSON>", "restore": "<PERSON><PERSON>", "complete": "完成 - <PERSON><PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON>", "yesterday": "d<PERSON>n", "common.refreshing": "Yenileniyor", "common.releaseToRefresh": "Yenilemek iç<PERSON> bı<PERSON>n", "common.pullToRefresh": "Aşağı çekerek yenile", "edit-message-warning": "<PERSON>u <PERSON><PERSON><PERSON><PERSON>, tüm sonraki sohbet kayıtlarını temizleyecek ve AI yanıtlarını yeniden oluşturacaktır.", "enter-your-message": "Mesajınızı girin", "set-deadline": "Son tes<PERSON> ta<PERSON><PERSON>i a<PERSON>.", "expired": "S<PERSON><PERSON>i doldu", "expired-days": "<PERSON><PERSON><PERSON><PERSON> {{count}} gü<PERSON> g<PERSON>.", "expired-hours": "<PERSON><PERSON><PERSON>i doldu {{count}} saat.", "expired-minutes": "<PERSON><PERSON><PERSON><PERSON> {{count}} da<PERSON><PERSON> g<PERSON>.", "days-left": "{{count}} gün sonra", "hours-left": "{{count}} saat sonra", "minutes-left": "{{count}} da<PERSON>ka sonra", "about-to-expire": "Süresi dolmak üzere", "1-day": "1 gün", "1-week": "Bir Hafta", "1-month": "bir ay", "quick-select": "Hızlı Seçim", "import-ai-configuration": "AI yapılandırmasını içe aktar", "would-you-like-to-import-this-configuration": "Bu AI yapılandırmasını içe aktarmak ister misiniz?", "detected-ai-configuration-to-import": "AI yapılandırmanın ithal edilmeye hazır olduğu tespit edildi", "importing": "İçe aktarıyor", "cache-cleared-successfully": "Önbellek başarıyla temizlendi! Sayfa otomatik olarak yeniden yüklenecek.", "failed-to-clear-cache": "Tarayıcı önbelleğini temizleme başarısız oldu. Lütfen manuel olarak yenilemeyi deneyin (Ctrl+Shift+R).", "select-deployment": "Dağıtımı seç", "deployment-name": "Dağı<PERSON>ım adı", "please-set-the-api-endpoint": "Lütfen API uç noktasını ayarlayın", "please-set-the-api-key": "Lütfen API anahtarını ayarlayın"}