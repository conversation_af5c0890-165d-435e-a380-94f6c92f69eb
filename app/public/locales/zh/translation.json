{"blinko": "闪念", "notes": "笔记", "resources": "资源", "archived": "归档", "settings": "设置", "total-tags": "所有标签", "search": "搜索...", "i-have-a-new-idea": "我有一个新想法...", "note": "笔记", "archive": "归档", "convert-to": "转换为", "delete": "删除", "edit": "编辑", "hello": "你好", "multiple-select": "多选", "recovery": "恢复", "items": "项目", "confirm-to-delete": "确认删除！", "in-progress": "进行中...", "operation-failed": "操作失败。", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "此操作会删除相关标签，且无法恢复，请确认", "your-changes-have-been-saved": "您的更改已保存！", "add-tag": "添加标签", "cancel": "取消", "confrim": "确认", "no-data-here-well-then-time-to-write-a-note": "暂时没有数据哦~", "basic-information": "基本信息", "language": "语言", "name": "名称", "preference": "偏好", "theme": "主题", "change-type": "更改类型", "insert-hashtag": "插入标签", "bulleted-list": "列表", "insert-codeblock": "插入代码块", "insert-sandpack": "插入沙袋", "insert-table": "插入表格", "numbered-list": "编号列表", "check-list": "选择列表", "upload-file": "上传文件", "delete-confirm": "删除确认", "delete-success": "删除成功", "this-operation-will-be-delete-resource-are-you-sure": "该操作将删除资源，你确定吗？", "confirm": "确认", "update-successfully": "更新成功", "create-successfully": "成功创建", "all-notes-have-been-loaded": "已全部加载{{items}}条笔记", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "嗨，{{name}}！我可以为您搜索笔记，今天有什么可以帮您？", "ask-about-your-notes": "输入关于笔记的问题", "use-ai": "使用Blinko ai", "model-provider": "AI服务商", "api-endpoint": "接口地址", "must-start-with-http-s-or-use-api-openai-as-default": "必须以 http(s):// 开头，或默认使用 /api/openai", "user-custom-openai-api-key": "用户自定义 OpenAI Api 密钥", "user-custom-azureopenai-api-instance": "Azure OpenAI 虚拟实例名称", "user-custom-azureopenai-api-deployment": "Azure OpenAI 部署名称", "user-custom-azureopenai-api-version": "API 版本", "ai-model": "人工智能模型", "logout": "注销", "user-or-password-error": "用户或密码错误", "username": "用户名", "enter-your-name": "输入您的姓名", "password": "密码", "need-to-create-an-account": "需要创建账户？", "sign-up": "注册", "sign-in": "登录", "enter-your-password": "输入密码", "nickname": "昵称", "change-user-info": "更改用户信息", "rest-user-password": "重置用户密码", "confirm-password": "确认密码", "confirm-your-password": "确认密码", "enter-your-username": "输入您的用户名", "keep-sign-in": "保持登录", "recording": "录音", "required-items-cannot-be-empty": "所需项目不能为空", "the-two-passwords-are-inconsistent": "两个密码不一致", "create-successfully-is-about-to-jump-to-the-login": "创建成功，即将跳转到登录页面", "already-have-an-account-direct-login": "已有账户？直接登录", "no-tag-found": "未找到标签", "new-version-detected-click-to-get-the-latest-version": "🎉检测到新版本，点击获取最新版本", "schedule-task": "定时任务", "schedule-back-up": "定时备份", "every-half-year": "每半年", "every-three-month": "每三个月", "every-month": "每月", "every-week": "每周", "every-day": "每天", "import": "导入", "import-from-bko": "从.bko文件导入", "not-a-bko-file": "不是 bko 文件", "convert-to-note": "转换为笔记", "convert-to-blinko": "转换为闪念", "reviewed": "已回顾", "congratulations-youve-reviewed-everything-today": "恭喜你，今天你已经回顾了所有内容", "name-db": "名称", "schedule": "计划", "last-run": "最后一次运行", "backup-file": "备份文件", "status": "状态", "running": "运行中", "stopped": "已停止", "show-navigation-bar-on-mobile": "移动端隐藏导航栏", "schedule-archive-blinko": "定时归档闪念", "there-are-no-resources-yet-go-upload-them-now": "还没有资源，现在就去上传吧", "daily-review": "每日回顾", "detail": "详情", "total": "全部", "enter-send-shift-enter-for-new-line": "Enter发送，Shift+Enter 换行", "show-less": "收起", "show-more": "展开", "top": "置顶", "cancel-top": "取消置顶", "save": "保存", "created-in": "创建于", "set-as-public": "设置为公开", "unset-as-public": "取消公开", "with-link": "带链接", "has-file": "包含文件", "no-tag": "无标签", "created-at": "创建时间", "user-list": "用户列表", "action": "操作", "original-password": "原始密码", "edit-user": "编辑用户", "import-from-memos-memos_prod-db": "从备忘录导入（memos_prod.db）", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "导出 memos_prod.db 时，请关闭备忘录容器，以免丢失部分数据。", "go-to-share-page": "转到分享页面", "import-done": "导入完成", "rebuilding-embedding-progress": "重建嵌入向量", "rebuild-embedding-index": "重建嵌入索引", "rebuild": "重建", "notes-imported-by-other-means-may-not-have-embedded-vectors": "通过其他方式导入的票据可能没有嵌入向量", "order-by-create-time": "按创建时间排序", "time-format": "时间格式", "version": "版本", "new-version-available": "新版本可用", "storage": "存储", "local-file-system": "本地文件系统", "object-storage": "对象存储", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "除 GPT 模型外，还需要确保可以调用", "speech-recognition-requires-the-use-of": "语音识别需要使用", "ai-expand": "AI扩写", "ai-polish": "AI润色", "accept": "接受", "reject": "拒绝", "stop": "停止", "card-columns": "卡片列", "select-a-columns": "选择列", "width-less-than-1024px": "宽度小于 1024px", "width-less-than": "宽度小于", "small-device-card-columns": "小型设备卡列", "medium-device-card-columns": "中尺寸设备卡列", "large-device-card-columns": "大尺寸设备卡列", "device-card-columns": "设备卡列", "columns-for-different-devices": "适用于不同设备的栏目", "mobile": "移动电话", "tablet": "平板电脑", "desktop": "台式机", "chars": "字符数", "text-fold-length": "文本折叠长度", "title-first-line-of-the-text": "标题（文本第一行）", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "内容（文本的其余部分，如果文本长度超过规定长度）", "ai-tag": "AI标签", "article": "文章", "embedding-model": "嵌入式模型", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "如果您有大量笔记，可能会消耗一定数量的代币", "force-rebuild": "强制重建", "force-rebuild-embedding-index": "强制重建将完全重建已索引的所有数据", "embedding-model-description": "切换嵌入式模型后必须重建索引", "top-k-description": "最终返回文件的最大数量", "embedding-score-description": "查询的相似性阈值通常是欧氏总和距离", "embedding-lambda-description": "查询结果多样性加权参数", "update-tag-icon": "更新标签图标", "delete-only-tag": "只删除标签", "delete-tag-with-note": "删除标签和笔记", "update-tag-name": "更新标签名称", "thinking": "思考中...", "select-all": "全部选择", "deselect-all": "取消全选", "insert-before": "插入内容前", "insert-after": "插入内容后", "update-name": "更新名称", "ai-emoji": "AI表情", "ai-generate-emoji": "AI生成表情", "ai-generating-emoji": "AI正在根据标签生成相关表情", "custom-icon": "自定义图标", "ai-enhanced-search": "人工智能增强搜索", "preview-mode": "预览模式", "source-code": "源码", "camera": "照相机", "reference": "引用", "reference-note": "参考说明", "source-code-mode": "源码模式", "heading": "标题", "paragraph": "段落", "quote": "引用", "bold": "粗体", "remove-italic": "删除斜体", "underline": "下划线", "remove-bold": "删除粗体", "italic": "斜体", "remove-underline": "删除下划线", "select-block-type": "选择区块类型", "block-type-select-placeholder": "区块类型", "trash": "回收站", "custom-path": "自定义路径", "page-size": "每页加载数", "toolbar-visibility": "工具栏可见性", "always-hide-toolbar": "始终隐藏", "always-show-toolbar": "始终显示", "hide-toolbar-on-mobile": "在移动设备上隐藏", "select-toolbar-visibility": "选择工具栏可见性", "select-a-time-format": "选择时间格式", "enter-code-shown-on-authenticator-app": "输入验证器应用程序上显示的代码", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "打开第三方身份验证应用程序，输入屏幕上显示的密码", "two-factor-authentication": "双因素验证", "scan-this-qr-code-with-your-authenticator-app": "使用身份验证程序扫描此 QR 码", "or-enter-this-code-manually": "或手动输入此代码：", "verify": "验证", "about": "关于", "upload": "上传", "days": "天", "select-model": "选择型号", "select-deployment": "选择部署", "select-model-provider": "选择模型提供商", "allow-register": "允许注册", "access-token": "访问令牌", "bucket": "桶", "region": "地区", "access-key-secret": "访问密钥", "access-key-id": "访问密钥ID", "share-and-copy-link": "共享和复制链接", "copy-share-link": "复制共享链接", "endpoint": "端点", "create-user": "创建用户", "export-format": "导出格式", "export": "导出", "time-range": "时间范围", "all": "全部", "exporting": "正在导出...", "filter-settings": "筛选设置", "date-range": "时间范围", "start-date": "开始日期", "end-date": "结束日期", "tag-status": "标签状态", "all-notes": "所有笔记", "with-tags": "包含标签", "without-tags": "不包含标签", "select-tags": "选择标签", "additional-conditions": "附加条件", "has-image": "包含图片", "has-link": "包含链接", "apply-filter": "应用筛选", "to": "至", "reset": "重置", "no-condition": "无条件", "public": "公开的", "exclude-tag-from-embedding": "排除标签内容", "exclude-tag-from-embedding-tip": "带有此标签的笔记内容将不会被用于AI嵌入式处理", "exclude-tag-from-embedding-desc": "选择一个标签来排除其关联笔记的内容，这些内容将不会被用于生成AI嵌入向量", "ai-model-tooltip": "输入要使用的型号名称，例如 gpt-3.5-turbo、gpt-4、gpt-4o、gpt-4o-mini", "user-custom-azureopenai-api-deployment-tooltip": "输入要使用的部署名称，如 gpt-4o", "ollama-ai-model-tooltip": "输入要使用的模型名称，如 llama3.2", "ollama-default-endpoint-is-http-localhost-11434": "Ollama 默认端点为 http://localhost:11434", "your-azure-openai-instance-name": "您的 Azure OpenAI 实例名称", "2fa-setup-successful": "2FA设置成功", "align-center": "中心", "align-left": "左", "align-right": "对", "alternate-text": "备用文本", "both": "全部", "check": "任务清单", "close": "关闭", "code": "代码块", "code-theme": "代码块主题预览", "content-theme": "内容主题预览", "dark-mode": "深色模式", "delete-column": "删除行", "download-tip": "浏览器不支持下载功能", "edit-mode": "切换编辑模式", "emoji": "表情符号", "file-type-error": "文件类型错误。", "follow-system": "遵循系统", "footnote-ref": "脚注参考", "heading4": "标题4", "heading5": "标题5", "headings": "标题", "image-url": "图像网址", "info": "信息", "insert-column-left": "插入1个左边", "column": "专栏", "comment": "评论", "copied": "复制的", "copy": "复制", "delete-row": "删除栏", "devtools": "开发工具", "down": "向下", "fullscreen": "切换全屏", "generate": "生成", "heading1": "标题 1", "heading2": "标题 2", "heading3": "标题 3", "heading6": "标题 6", "help": "帮助", "indent": "缩进", "inline-code": "内联代码", "insert-column-right": "插入 1 个右", "insert-row-above": "插入上文 1", "insert-row-below": "下面插入 1", "instant-rendering": "即时渲染", "light-mode": "浅色模式", "line": "线路", "link": "链接", "link-ref": "链接参考", "list": "列表", "more": "更多信息", "name-empty": "名字为空", "ordered-list": "订单列表", "outdent": "凹排", "outline": "大纲", "over": "结束", "performance-tip": "实时预览需要${x}毫秒，您可以关闭它。", "preview": "预览", "record": "开始记录/结束记录", "record-tip": "该设备不支持录音", "redo": "重做", "remove": "移除", "role": "角色", "row": "行", "spin": "旋转", "split-view": "分屏显示", "strike": "下划线", "table": "表格", "text-is-not-empty": "文本（非空）", "title": "标题", "tooltip-text": "工具提示文本", "undo": "撤销", "up": "向上", "update": "更新", "updated-at": "更新时间", "upload-error": "上传错误", "uploading": "上传中...", "wysiwyg": "所见即所得", "search-tags": "搜索标签", "insert-attachment-or-note": "插入附件还是笔记中？", "paste-to-note-or-attachment": "您确定要粘贴到上下文或附件吗？", "context": "正文", "attachment": "附件", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "删除后，所有用户数据将被清除且无法恢复。", "upload-completed": "上传完成", "upload-cancelled": "上传已取消", "upload-failed": "上传失败", "import-from-bko-tip": "目前不支持将上传到S3进行恢复。当您想要恢复时，请暂时禁用S3选项。", "music-settings": "音乐设置", "spotify-consumer-key": "Spotify接口Key", "spotify-consumer-secret": "Spotify接口密钥", "enter-spotify-consumer-key": "输入 Spotify 接口 Key", "enter-spotify-consumer-secret": "输入Spotify接口密钥", "spotify-consumer-key-tip": "用来获得mp3音乐封面", "spotify-consumer-key-tip-2": "从https://developer.spotify.com/获取API密钥。", "edit-time": "编辑时间", "ai-write": "AI写作", "download": "下载", "rename": "重命名", "move-up": "向上移动", "cut": "剪切", "paste": "粘贴", "confirm-delete": "确认删除", "confirm-delete-content": "您确定要删除{{name}}吗？此操作无法撤销。", "folder-name": "文件夹名称", "file-name": "文件名", "operation-success": "操作成功", "cloud-file": "云文件", "move-to-parent": "移至上级", "no-resources-found": "未找到资源", "operation-in-progress": "操作进行中", "new-folder": "新建文件夹", "folder-name-exists": "文件夹名称已存在", "folder-name-required": "文件夹名称为必填项", "collapse": "收起", "show-all": "显示所有", "sun": "日", "mon": "一", "thu": "四", "wed": "三", "fri": "五", "sat": "六", "heatMapTitle": "过去一年的笔记热力图", "heatMapDescription": "显示每天创建的笔记数量", "select-month": "选择月份", "note-count": "笔记数", "total-words": "总字数", "max-daily-words": "单日最多字数", "active-days": "坚持记录天数", "analytics": "统计", "tag-distribution": "标签分布", "other-tags": "其他标签", "tue": "星期二", "offline-status": "离线访问中", "offline-title": "你处于离线状态", "offline-description": "请检查您的互联网连接，然后重试。", "retry": "重试", "back-to-home": "返回主页", "offline": "离线", "close-background-animation": "关闭背景动画", "custom-bg-tip": "访问 https://www.shadergradient.co/ 创建您自己的渐变背景", "custom-background-url": "自定义背景", "share": "分享", "need-password-to-access": "需要访问密码", "password-error": "密码错误", "create-share": "创建分享", "cancel-share": "取消分享", "share-link": "分享链接", "set-access-password": "设置访问密码", "access-password": "访问密码", "protect-your-shared-content": "保护您分享的内容", "select-date": "选择日期", "expiry-time": "过期时间", "select-expiry-time": "选择到期时间", "permanent-valid": "永久有效", "7days-expiry": "7天到期", "custom-expiry": "自定义到期", "30days-expiry": "30天有效期", "share-link-expired": "分享链接已过期", "share-link-expired-desc": "这份共享已过期，请联系管理员重新共享！", "shared": "共享", "internal-shared": "内部共享", "edited": "已编辑", "move-down": "向下移动", "provider-id": "提供者ID", "provider-name": "提供者名称", "well-known-url": "WellKnown URL\n\n著名的URL", "authorization-url": "授权URL", "token-url": "令牌网址", "userinfo-url": "用户信息URL", "scope": "范围", "client-id": "客户ID", "client-secret": "客户端密码", "sso-settings": "SSO 设置", "oauth2-providers": "Oauth2供应商", "add-oauth2-provider": "添加Oauth2提供者", "add-provider": "添加提供商", "edit-oauth2-provider": "编辑OAuth2提供程序", "confirm-delete-provider": "确认删除提供商", "please-select-icon-from-iconify": "请从Iconify中选择图标", "provider-icon": "供应商图标", "select-provider-template": "选择服务提供者模板", "provider-template": "提供程序模板", "please-add-this-url-to-your-oauth-provider-settings": "请将此网址添加到您的OAuth供应商设置中。", "redirect-url": "重定向URL", "sign-in-with-provider": "使用{{ provider }}登录", "community": "社区", "theme-color": "主题颜色", "link-account": "链接账户", "select-account": "选择账户", "link-account-warning": "请注意，如果您将您的账户链接起来，当前账户中的任何数据将不会同步到已链接的账户。", "unlink-account": "取消链接账户", "unlink-account-tips": "您确认通过该账户可以访问所有关联的协会吗？", "login-type": "登录类型", "close-daily-review": "关闭每日回顾", "max-home-page-width": "首页最大宽度", "max-home-page-width-tip": "如果设置为 0，则为最大宽度。", "no-comments-yet": "暂无评论", "author": "作者", "from": "从", "reply-to": "回复", "hub": "广场", "home-site": "主页", "use-blinko-hub": "使用 <PERSON><PERSON><PERSON>", "full-screen": "全屏", "exit-fullscreen": "退出全屏", "no-note-associated": "没有笔记关联", "insert-context": "插入到正文", "follow": "关注", "follower": "关注者", "following": "正在关注", "admin": "站长", "site-url": "Blinko 站点网址", "unfollow": "取消关注", "join-hub": "加入 Hub", "refresh": "刷新", "comment-notification": "评论通知", "follow-notification": "跟随通知", "followed-you": "关注了你", "mark-all-as-read": "将所有标记为已读", "no-notification": "没有通知", "new-notification": "新通知", "notification": "通知", "backup-success": "备份成功🎉", "system-notification": "系统通知", "embedding-api-endpoint": "嵌入式API端点", "embedding-api-key": "嵌入式 API 密钥", "recommand": "推荐", "has-todo": "有待完成", "reference-by": "被引用", "hide-notification": "隐藏通知", "search-settings": "搜索设置...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "此操作将删除所选文件，且无法恢复，请确认。", "plugin-settings": "插件设置", "installed-plugins": "已安装", "marketplace": "市场", "local-development": "本地开发", "add-local-plugin": "添加本地插件", "local-plugin": "本地插件", "uninstall": "卸载", "install": "安装", "downloads": "下载", "plugin-updated": "插件已更新", "plugin-update-failed": "插件更新失败。", "plugin-connection-failed": "插件连接失败", "disconnect": "断开连接", "local-development-description": "添加本地开发插件并对其进行调试。", "ai": "人工智能", "ai-chat-box-notes": "以下是为您检索到的相关笔记", "add-to-note": "添加到笔记", "add-to-blinko": "添加到Blinko", "no-title": "无标题", "search-blinko-content-or-help-create": "搜索blinko内容或帮助创建...", "conversation-history": "会话历史", "new-conversation": "新聊天", "knowledge-base-search": "知识库搜索", "add-tools-to-model": "在线搜索或允许AI调用blinko api。", "clear-current-content": "清除当前内容", "welcome-to-blinko": "欢迎您，{{name}}", "coding": "编码", "ai-prompt-writing": "您是一名专业作家，请根据用户提供的主题撰写一篇专业文章。", "writing": "写作", "ai-prompt-translation": "您是一名专业的翻译员，请将用户提供的文本翻译成{{lang}}", "ai-prompt-coding": "你是一名专业程序员，请根据用户提供的主题编写一个简单的Python程序。", "translation": "翻译", "first-char-delay": "首字符延迟", "total-tokens": "总代币", "check-connect": "核对", "check-connect-error": "连接失败!", "check-connect-success": "检查连接成功", "loading": "加载中", "embedding-dimensions": "嵌入维度", "embedding-dimensions-description": "您需要确保模型尺寸正确无误，并且在更改后需要强制重建索引记录。", "model": "模型", "deployment-name": "部署名稱", "ai-tools": "AI工具", "tavily-api-key": "Tavily搜索API密钥", "tavily-max-results": "<PERSON><PERSON> <PERSON>结果", "ai-prompt-writing-content": "撰写一篇200字的文章并将其保存在你的笔记中。", "ai-prompt-coding-content": "提取 https://github.com/blinko-space/blinko 网页内容", "stop-task": "停止任务", "processing": "处理", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "正在进行重建任务，您是否想要重新开始？", "hide-blog-images": "隐藏博客图像", "ai-prompt-translation-content": "在过去两天中检查无标签笔记并标记它们。", "ai-prompt-delete-content": "查找2个归档的笔记，总结并保存为新笔记，然后删除这两个存档的笔记", "older": "较旧", "newer": "较新", "restore-this-version": "还原此版本", "Note History": "历史记录", "View History Versions": "查看历史版本", "history-note-only": "注意：该历史记录仅包含文本内容，不包含文件历史", "referenceResource": "引用资源", "to-ask-ai": "问AI", "press-enter-to-select-first-result": "按Enter选择第一个结果", "ask-ai": "问AI", "ask-blinko-ai-about-this-query": "向Blinko AI询问此查询", "search-or-ask-ai": "搜索说明，设置或询问AI ...", "plugin": "插件", "editor-preview": "编辑", "auto-add-tags": "自动添加标签", "add-as-comment": "添加为评论", "choose-what-to-do-with-ai-results": "选择如何使用AI结果", "ai-post-processing-mode": "AI后处理模式", "ai-post-processing-prompt": "AI后期处理评论提示", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "分析以下笔记内容，推荐合适的标签并提供简短摘要", "enter-custom-prompt-for-post-processing": "输入用于后处理的自定义提示", "enable-ai-post-processing": "启用AI后处理", "automatically-process-notes-after-creation-or-update": "笔记创建后自动处理", "can-generate-summaries-tags-or-perform-analysis": "可以生成摘要标签或执行分析", "http-proxy": "HTTP代理", "use-http-proxy": "使用HTTP代理", "enable-http-proxy-for-accessing-github-or-ai-api-endpoints": "为访问GitHub或AI API端点启用HTTP代理", "proxy-host": "代理主机", "proxy-port": "代理端口", "proxy-username": "代理用户名", "proxy-password": "代理密码", "ip-address-or-hostname-for-proxy-server": "代理服务器的IP地址或主机名", "port-number-for-proxy-server": "代理服务器的端口号", "optional-username-for-authenticated-proxy": "可选的认证代理用户名", "optional-password-for-authenticated-proxy": "可选的认证代理密码", "optional": "可选", "api-key": "", "content-generated-by-ai": "AI产生的内容", "days-ago": "", "define-custom-prompt-for-ai-to-process-notes": "操作AI评论当前笔记。例如：请总结笔记内容。如果笔记内容少于10个字则帮我润色一下。", "enter-your-api-key": "", "hours-ago": "", "impoort-from-bko": "", "minutes-ago": "", "months-ago": "", "prompt-used-for-post-processing-notes": "用于后处理笔记的提示词", "rebuild-in-progress": "重建正在进行中", "rest-user-info": "修改用户信息", "setting": "", "superadmin": "", "user": "", "weeks-ago": "", "years-ago": "", "ai-post-processing": "AI后处理", "model-list-updated": "模型列表已更新", "test-proxy-connection": "测试代理连接", "test-if-the-proxy-is-working-correctly": "测试当前代理设置是否正常工作", "test": "测试", "response-time": "响应时间", "status-code": "状态码", "error": "错误", "error-code": "错误代码", "error-details": "错误详情", "check-proxy-settings-and-ensure-server-is-accessible": "请检查代理设置并确保服务器可访问", "do-not-include-http-or-https-prefix": "请勿包含 http:// 或 https:// 前缀", "to-search-tags": "搜索标签", "app-upgrade-required": "需要升级", "current-app-version": "当前APP版本", "required-app-version": "需要的APP版本", "upgrade": "升级", "online-search": "在线搜索", "smart-edit": "智能编辑", "function-call-required": "需要函数调用", "smart-edit-prompt": "智能编辑提示", "define-instructions-for-ai-to-edit-your-notes": "可以使用提示词操作笔记，例如：如果笔记中包含链接请在原笔记下方总结链接内容，并生成标签。", "rebuild-started": "重建已开始", "rebuild-stopped-by-user": "用户已停止重建", "random-mode": "随机漫步", "related-notes": "相关笔记", "no-related-notes-found": "没有找到相关笔记", "advanced": "高级", "rerank-model-description": "指定一个模型来重新排序向量结果以提高搜索精度", "rerank-model": "重新排名模型", "rerank": "重新排名", "use-custom-rerank-endpoint-description": "启用后将使用嵌入模型的端点和API密钥进行重排序", "use-embedding-endpoint": "使用嵌入端点", "rerank-score-description": "设置重排序模型的分数阈值，低于此值的结果将被过滤", "public-share": "公共分享", "internal-share": "内部分享", "no-team-members-found": "找不到团队成员", "selected-users": "选定的用户", "tags-prompt": "标签提示", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "用于自动生成标签的提示。如果设置为空，则会使用默认提示。", "low-permission-token": "低权限令牌", "low-permission-token-desc": "低权限令牌只能访问upsertNote端点和AI聊天端点。它们无法访问您的帐户信息或其他笔记。这非常适合于Telegram机器人或WeChat机器人等使用场景，您可以确保它们不能访问任何其他笔记。", "generate-low-permission-token": "生成低权限令牌", "this-token-is-only-displayed-once-please-save-it-properly": "此令牌只显示一次，请妥善保存。", "refresh-model-list": "获取模型列表", "please-set-the-embedding-model": "请设置嵌入模型", "please-set-the-api-endpoint": "请设置API端点", "please-set-the-api-key": "请设置API密钥", "blinko-endpoint": "Blinko端点", "enter-blinko-endpoint": "你的Blinko部署的URL", "login-failed": "登录失败", "verification-failed": "认证失败", "download-success": "下载成功", "download-failed": "下载失败", "downloading": "下载中", "hide-pc-editor": "隐藏PC端编辑器", "import-from-markdown": "从Markdown文件导入", "import-from-markdown-tip": "从单个 .md 文件或包含 .md 文件的 .zip 存档导入", "not-a-markdown-or-zip-file": "不是Markdown或zip文件。请选择.md或.zip文件。", "server": "服务器", "client": "客户端", "new-server-version-available": "服务器有新版本可用", "new-client-version-available": "客户端有新版本可用", "todo": "待办", "restore": "恢复", "complete": "完成", "today": "今天", "yesterday": "昨天", "common.refreshing": "刷新中", "common.releaseToRefresh": "松开刷新", "common.pullToRefresh": "下拉刷新", "edit-message": "编辑消息", "edit-message-warning": "编辑此消息将清除后续的所有对话记录，并重新生成AI回复。", "enter-your-message": "输入您的消息", "confirm-and-regenerate": "确认并重新生成", "share-conversation": "分享对话", "share-link-copied": "分享链接已复制到剪贴板", "ai-conversation-share": "Blinko AI 对话分享", "shared-at": "分享于", "loading-shared-conversation": "加载分享对话中...", "invalid-share-link": "无效的分享链接", "conversation-not-found": "分享的对话不存在或未公开", "load-conversation-failed": "加载分享对话失败", "conversation-not-exist": "分享的对话不存在", "load-failed": "加载失败", "powered-by-blinko": "由 Blinko 提供支持", "pull-to-refresh": "下拉刷新", "conversation-id-missing": "对话ID缺失", "shared-by": "分享者", "set-deadline": "设置截止日期", "expired": "已过期", "days-left": "{{count}}天后", "hours-left": "{{count}}小时后", "minutes-left": "{{count}}分钟后", "about-to-expire": "即将到期", "just-expired": "刚过期", "expired-days": "过期{{count}}天", "expired-hours": "过期{{count}}小时", "expired-minutes": "过期{{count}}分钟", "1-day": "1天", "1-week": "一周", "1-month": "一个月", "quick-select": "快捷选择", "import-ai-configuration": "导入AI配置", "would-you-like-to-import-this-configuration": "你是否想要导入该AI配置？", "detected-ai-configuration-to-import": "检测到AI配置待导入", "importing": "导入中", "maintenance": "维护", "clear-browser-cache": "清除浏览器缓存", "clear-cache": "清除缓存", "cache-cleared-successfully": "缓存已清除成功！页面将自动重新加载。", "failed-to-clear-cache": "清除浏览器缓存失败。请尝试手动刷新 (Ctrl+Shift+R)。"}