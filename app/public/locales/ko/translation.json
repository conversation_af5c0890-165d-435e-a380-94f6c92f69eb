{"enter-spotify-consumer-key": "Spotify 소비자 키를 입력하세요.", "spotify-consumer-key-tip": "mp3 음악 커버를 얻었던 것", "spotify-consumer-key-tip-2": "https://developer.spotify.com/에서 API 키를 받으세요.", "blinko": "Blinko", "notes": "참고", "resources": "리소스", "archived": "아카이브", "settings": "설정", "total-tags": "총 태그", "search": "검색...", "i-have-a-new-idea": "새로운 아이디어가 떠올랐어요...", "add-tag": "태그 추가", "ai-model": "AI 모델", "all-notes-have-been-loaded": "모든 {{아이템}} 노트가 로드되었습니다.", "already-have-an-account-direct-login": "이미 계정이 있으신가요? 직접 로그인", "archive": "아카이브", "ask-about-your-notes": "노트에 대해 질문하기", "basic-information": "기본 정보", "bulleted-list": "글머리 기호 목록", "cancel": "취소", "change-type": "유형 변경", "change-user-info": "사용자 정보 변경", "check-list": "확인 목록", "confirm": "확인", "confirm-password": "비밀번호 확인", "confirm-to-delete": "삭제를 확인합니다!", "confirm-your-password": "비밀번호 확인", "confrim": "확인", "convert-to": "다음으로 변환", "convert-to-blinko": "Blinko로 전환", "convert-to-note": "노트로 변환", "create-successfully": "성공적으로 만들기", "create-successfully-is-about-to-jump-to-the-login": "생성 성공, 로그인으로 이동하려고 합니다.", "daily-review": "일일 검토", "delete": "삭제", "delete-confirm": "삭제 확인", "detail": "세부 정보", "edit": "편집", "enter-send-shift-enter-for-new-line": "보내기 입력, 새 줄을 위한 Shift+Enter 입력", "enter-your-name": "이름 입력", "enter-your-password": "비밀번호 입력", "every-month": "매월", "every-three-month": "3개월마다", "every-week": "매주", "import": "가져오기", "in-progress": "진행 중...", "insert-hashtag": "해시태그 삽입", "insert-sandpack": "샌드팩 삽입", "insert-table": "표 삽입", "items": "항목", "language": "언어", "last-run": "마지막 실행", "logout": "로그아웃", "model-provider": "모델 제공자", "must-start-with-http-s-or-use-api-openai-as-default": "http(s)://로 시작하거나 기본값으로 /api/openai를 사용해야 합니다.", "name": "이름", "name-db": "이름", "need-to-create-an-account": "계정을 만들어야 하나요?", "nickname": "닉네임", "no-tag-found": "태그를 찾을 수 없습니다.", "not-a-bko-file": "BKO 파일이 아닙니다.", "numbered-list": "번호가 매겨진 목록", "operation-failed": "작업이 실패했습니다.", "password": "비밀번호", "preference": "기본 설정", "recording": "녹음", "required-items-cannot-be-empty": "필수 항목은 비워둘 수 없습니다.", "rest-user-password": "나머지 사용자 비밀번호", "reviewed": "검토 완료", "running": "실행 중", "save": "저장", "schedule": "일정", "schedule-archive-blinko": "일정 아카이브 블링코", "schedule-back-up": "백업 예약", "schedule-task": "작업 예약", "show-less": "덜 보기", "show-more": "자세히 보기", "show-navigation-bar-on-mobile": "모바일의 숨겨진 탐색 모음", "status": "상태", "the-two-passwords-are-inconsistent": "두 비밀번호가 일치하지 않습니다.", "theme": "테마", "there-are-no-resources-yet-go-upload-them-now": "아직 리소스가 없으니 지금 업로드하세요.", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "이 작업은 관련 레이블을 제거하며 복원할 수 없음을 확인하시기 바랍니다.", "this-operation-will-be-delete-resource-are-you-sure": "이 작업은 리소스 삭제가 확실합니까?", "total": "합계", "update-successfully": "업데이트 성공", "use-ai": "AI 사용", "user-custom-openai-api-key": "사용자 지정 OpenAI API 키", "user-custom-azureopenai-api-instance": "Azure OpenAI 인스턴스 이름", "user-custom-azureopenai-api-deployment": "Azure OpenAI 배포 이름", "user-custom-azureopenai-api-version": "API 버전", "user-or-password-error": "사용자 또는 비밀번호 오류", "username": "사용자 이름", "api-endpoint": "API 엔드포인트", "backup-file": "백업 파일", "congratulations-youve-reviewed-everything-today": "오늘 모든 내용을 검토하셨습니다.", "delete-success": "삭제 성공", "every-day": "매일", "every-half-year": "반기마다", "import-from-bko": ".bko에서 가져오기", "insert-codeblock": "코드 블록 삽입", "multiple-select": "다중 선택", "new-version-detected-click-to-get-the-latest-version": "🎉새 버전이 감지되었습니다, 최신 버전을 받으려면 클릭하세요.", "recovery": "복구", "sign-in": "로그인", "cancel-top": "상단 취소", "stopped": "중지됨", "top": "Top", "no-data-here-well-then-time-to-write-a-note": "여기에 데이터가 없나요? 그렇다면 메모를 작성할 시간입니다!", "keep-sign-in": "로그인 유지", "enter-your-username": "사용자 이름 입력", "hello": "hello", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "안녕하세요 {{이름}}!,노트를 검색해 드릴 수 있는데요,오늘은 무엇을 도와드릴까요?", "note": "참고", "sign-up": "가입하기", "upload-file": "파일 업로드", "your-changes-have-been-saved": "변경 사항이 저장되었습니다!", "created-in": "에 생성됨", "unset-as-public": "공개로 설정 해제", "no-tag": "태그 없음", "with-link": "링크 사용", "has-file": "파일 있음", "created-at": "다음에서 만들기", "role": "역할", "create-user": "사용자 만들기", "action": "액션", "original-password": "원래 비밀번호", "edit-user": "사용자 편집", "import-from-memos-memos_prod-db": "메모(memos_prod.db)에서 가져오기", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "memos_prod.db를 내보낼 때는 데이터의 일부가 손실되지 않도록 메모 컨테이너를 닫아 주세요.", "go-to-share-page": "공유 페이지로 이동", "import-done": "가져오기 완료", "rebuild-embedding-index": "임베딩 인덱스 재구축", "notes-imported-by-other-means-may-not-have-embedded-vectors": "다른 방법으로 가져온 노트에는 벡터가 내장되어 있지 않을 수 있습니다.", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "노트가 많으면 특정 수의 토큰을 소모할 수 있습니다.", "order-by-create-time": "생성 시간별 주문", "time-format": "시간 형식", "version": "버전", "new-version-available": "새 버전 사용 가능", "storage": "스토리지", "local-file-system": "로컬 파일 시스템", "object-storage": "개체 스토리지", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "GPT 모델 외에도 다음과 같이", "speech-recognition-requires-the-use-of": "음성 인식은 다음을 사용해야 합니다.", "ai-expand": "AI 확장", "ai-polish": "AI 폴란드어", "accept": "수락", "reject": "거부", "stop": "중지", "card-columns": "카드 열", "select-a-columns": "열 선택", "width-less-than-1024px": "너비 1024px 미만", "width-less-than": "너비 미만", "small-device-card-columns": "소형 장치 카드 열", "medium-device-card-columns": "중간 장치 카드 열", "large-device-card-columns": "대형 장치 카드 열", "device-card-columns": "장치 카드 열", "columns-for-different-devices": "다양한 장치용 열", "mobile": "모바일", "tablet": "태블릿", "desktop": "데스크톱", "chars": "문자", "text-fold-length": "텍스트 접기 길이", "title-first-line-of-the-text": "제목(텍스트의 첫 줄)", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "콘텐츠(텍스트가 길이보다 긴 경우 나머지 텍스트)", "ai-tag": "AI 태그", "article": "기사", "embedding-model": "임베딩 모델", "force-rebuild": "강제 재구축", "force-rebuild-embedding-index": "강제 재구축은 인덱싱된 모든 데이터를 완전히 재구축합니다.", "embedding-model-description": "임베디드 모델을 전환한 후 인덱스를 다시 작성해야 합니다.", "top-k-description": "최종적으로 반환되는 최대 문서 수", "embedding-score-description": "쿼리의 유사성 임계값은 일반적으로 유클리드 합 거리입니다.", "embedding-lambda-description": "쿼리 결과 다양성 가중치 매개변수", "update-tag-icon": "태그 업데이트 아이콘", "delete-only-tag": "태그만 삭제", "delete-tag-with-note": "메모와 함께 태그 삭제", "update-tag-name": "태그 이름 업데이트", "thinking": "생각...", "select-all": "모두 선택", "deselect-all": "모두 선택 해제", "insert-before": "앞에 삽입", "insert-after": "다음에 삽입", "update-name": "업데이트 이름", "ai-emoji": "<PERSON> 이모티콘", "custom-icon": "사용자 지정 아이콘", "ai-enhanced-search": "AI 강화 검색", "preview-mode": "미리보기 모드", "source-code": "소스 코드", "camera": "카메라", "reference": "참조", "reference-note": "참고 참고 사항", "source-code-mode": "소스 코드 모드", "heading": "제목", "paragraph": "단락", "quote": "견적", "bold": "Bold", "remove-italic": "이탤릭체 제거", "underline": "밑줄", "remove-bold": "굵게 제거", "italic": "이탤릭체", "remove-underline": "밑줄 제거", "select-block-type": "블록 유형 선택", "block-type-select-placeholder": "블록 유형", "trash": "쓰레기", "custom-path": "사용자 지정 경로", "page-size": "페이지 크기", "toolbar-visibility": "툴바 가시성", "always-hide-toolbar": "항상 숨기기", "always-show-toolbar": "항상 표시", "hide-toolbar-on-mobile": "모바일에서 숨기기", "select-toolbar-visibility": "도구 모음 표시 여부 선택", "select-a-time-format": "시간 형식 선택", "enter-code-shown-on-authenticator-app": "인증 앱에 표시된 코드를 입력합니다.", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "타사 인증 앱을 열고 화면에 표시된 코드를 입력합니다.", "two-factor-authentication": "2단계 인증", "scan-this-qr-code-with-your-authenticator-app": "인증 앱으로 이 QR 코드를 스캔하세요.", "or-enter-this-code-manually": "또는 이 코드를 수동으로 입력하세요:", "verify": "확인", "about": "정보", "upload": "업로드", "days": "일수", "select-model-provider": "모델 공급자 선택", "allow-register": "등록 허용", "access-token": "액세스 토큰", "bucket": "버킷", "region": "지역", "access-key-secret": "액세스 키 비밀", "access-key-id": "액세스 키 ID", "share-and-copy-link": "링크 공유 및 복사", "copy-share-link": "공유 링크 복사", "endpoint": "엔드포인트", "export-format": "내보내기 형식", "export": "내보내기", "time-range": "시간 범위", "all": "모두", "exporting": "내보내기...", "has-image": "이미지 있음", "has-link": "링크 있음", "filter-settings": "필터 설정", "tag-status": "태그 상태", "all-notes": "모든 메모", "with-tags": "태그 포함", "without-tags": "태그 없음", "select-tags": "태그 선택", "additional-conditions": "추가 조건", "apply-filter": "필터 적용", "to": "To", "start-date": "시작 날짜", "end-date": "종료 날짜", "reset": "초기화", "no-condition": "조건 없음", "public": "공개", "ai-model-tooltip": "사용할 모델명(예: gpt-3.5-turbo, gpt-4, gpt-4o, gpt-4o-mini)을 입력합니다.", "user-custom-azureopenai-api-deployment-tooltip": " 사용할 배포 이름(예: gpt-4o)을 입력합니다.", "ollama-ai-model-tooltip": "사용할 모델 이름(예: llama3.2)을 입력합니다.", "ollama-default-endpoint-is-http-localhost-11434": "Ollama 기본 엔드포인트는 http://localhost:11434", "your-azure-openai-instance-name": "Azure OpenAI 인스턴스 이름", "2fa-setup-successful": "2단계 인증 설정이 성공적으로 완료되었습니다.", "ai-generate-emoji": "", "ai-generating-emoji": "", "align-center": "중앙", "align-left": "왼쪽", "align-right": "옳다", "alternate-text": "대체 텍스트", "api-key": "", "both": "둘 다", "check": "작업 목록", "close": "가까운", "code": "코드 블록", "code-theme": "코드 블록 테마 미리보기", "comment": "의견", "content-theme": "콘텐츠 테마 미리보기", "copied": "복사된", "copy": "복사하기", "dark-mode": "다크 모드", "date-range": "", "days-ago": "", "delete-column": "행 삭제", "delete-row": "열 삭제", "devtools": "개발자 도구", "down": "아래", "download-tip": "브라우저는 다운로드 기능을 지원하지 않습니다.", "edit-mode": "편집 모드 전환", "emoji": "이모지", "enter-your-api-key": "", "exclude-tag-from-embedding": "태그가 지정된 콘텐츠 제외", "exclude-tag-from-embedding-desc": "AI 임베딩 벡터 생성에서 해당 노트를 제외하려는 태그를 선택하세요.", "exclude-tag-from-embedding-tip": "이 태그가 있는 노트는 AI 임베딩 처리에서 제외됩니다.", "file-type-error": "파일 유형이 오류입니다.", "follow-system": "시스템 따르기", "footnote-ref": "각주 참조", "fullscreen": "전체 화면 전환", "generate": "생성하기", "heading1": "제목 1", "heading2": "제목 2", "heading3": "제목 3", "heading4": "4단계 제목", "heading5": "제목 5", "heading6": "6단계", "headings": "제목", "help": "도와주세요", "hours-ago": "", "image-url": "이미지 URL", "impoort-from-bko": "", "indent": "들여쓰기", "info": "정보", "inline-code": "인라인 코드", "insert-column-left": "1개를 왼쪽에 삽입하세요.", "insert-column-right": "1을 삽입하십시오.", "insert-row-above": "위에 1을 삽입하세요.", "insert-row-below": "아래에 1을 삽입하세요.", "instant-rendering": "즉각적 렌더링", "light-mode": "라이트 모드", "line": "라인", "link": "링크", "link-ref": "링크 참조", "list": "목록", "minutes-ago": "", "months-ago": "", "more": "더 많은", "name-empty": "이름이 비어 있습니다.", "ordered-list": "주문 목록", "outdent": "내어쓰다", "outline": "개요", "over": "넘어서다", "performance-tip": "실시간 미리보기에는 ${x}ms가 필요합니다. 닫을 수 있습니다.", "search-tags": "검색 태그", "insert-attachment-or-note": "첨부 파일이나 메모로 삽입하시겠습니까?", "context": "문맥", "paste-to-note-or-attachment": "컨텍스트나 첨부 파일에 붙여 넣을 확신이 있습니까?", "attachment": "첨부 파일", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "삭제 후 모든 사용자 데이터가 지워지고 복구할 수 없게 됩니다.", "upload-completed": "업로드가 완료되었습니다.", "upload-cancelled": "업로드가 취소되었습니다.", "upload-failed": "업로드 실패", "import-from-bko-tip": "현재 s3로의 업로드는 복구되지 않습니다. 복구를 원할 경우 s3 옵션을 일시적으로 비활성화하십시오.", "edit-time": "편집 시간", "ai-write": "인공지능 작성", "download": "다운로드", "rename": "이름 바꾸기", "move-up": "위로 이동", "cut": "잘라 내다", "paste": "붙여 넣다", "confirm-delete": "삭제 확인", "confirm-delete-content": "{{name}}을(를) 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "folder-name": "폴더 이름", "file-name": "파일 이름", "operation-success": "작업 성공적입니다.", "cloud-file": "클라우드 파일", "move-to-parent": "부모로 이동", "no-resources-found": "자원을 찾을 수 없습니다", "operation-in-progress": "진행 중인 작업", "new-folder": "새 폴더", "folder-name-exists": "폴더 이름이 이미 있습니다.", "folder-name-required": "폴더 이름 필요함", "collapse": "붕괴", "show-all": "모두 보기", "sun": "태양", "mon": "월", "thu": "목요일", "wed": "수요일", "fri": "금요일", "sat": "토요일", "heatMapTitle": "지난 일년간의 노트 히트 맵", "heatMapDescription": "하루에 만들어진 노트 수를 보여줍니다.", "select-month": "월 선택", "note-count": "노트 수", "total-words": "총 단어", "active-days": "활발한 날들", "max-daily-words": "최대 일일 단어", "analytics": "분석 결과", "tag-distribution": "태그 분배", "other-tags": "기타 태그", "tue": "화요일", "offline-status": "오프라인 모드", "offline-title": "오프라인 상태입니다.", "offline-description": "인터넷 연결을 확인하고 다시 시도해주세요.", "retry": "다시 시도하세요.", "back-to-home": "집으로 돌아가기", "offline": "오프라인.", "close-background-animation": "배경 애니메이션 닫기", "custom-bg-tip": "https://www.shadergradient.co/ 에 방문하여 자체 그라데이션 배경을 만드세요.", "custom-background-url": "사용자 정의 배경", "share": "공유하기", "need-password-to-access": "비밀번호로 접근이 필요합니다.", "password-error": "비밀번호 오류", "cancel-share": "공유 취소", "create-share": "공유 만들기", "share-link": "공유 링크", "set-access-password": "액세스 암호 설정", "protect-your-shared-content": "공유된 콘텐츠를 보호하세요.", "access-password": "액세스 비밀번호", "select-date": "날짜 선택", "expiry-time": "만료 시간", "select-expiry-time": "만료 시간 선택", "permanent-valid": "영구적 유효", "7days-expiry": "7일 만료", "custom-expiry": "사용자 정의 만료", "30days-expiry": "30일 만료", "share-link-expired": "공유 링크가 만료되었습니다.", "share-link-expired-desc": "이 공유가 만료되었습니다. 다시 공유하려면 관리자에게 문의하세요!", "shared": "공유", "internal-shared": "내부 공유", "edited": "편집된", "move-down": "이동하다", "provider-id": "제공자 ID", "provider-name": "제공업체 이름", "well-known-url": "잘 알려진 URL", "authorization-url": "인증 URL", "token-url": "토큰 URL", "userinfo-url": "사용자 정보 URL", "scope": "범위", "client-id": "고객 ID", "client-secret": "클라이언트 비밀번호", "sso-settings": "SSO 설정", "oauth2-providers": "Oauth2 제공업체", "add-oauth2-provider": "Oauth2 공급자 추가", "add-provider": "제공 업체 추가", "edit-oauth2-provider": "Oauth2 제공자 편집", "confirm-delete-provider": "제공자 삭제 확인", "please-select-icon-from-iconify": "iconify에서 아이콘을 선택해주세요.", "provider-icon": "제공자 아이콘", "select-provider-template": "제공자 템플릿 선택하기", "provider-template": "제공업체 템플릿", "please-add-this-url-to-your-oauth-provider-settings": "이 URL을 OAuth 제공업체 설정에 추가해 주세요.", "redirect-url": "리디렉션 URL", "sign-in-with-provider": "{{ provider }}를 사용하여 로그인하세요.", "community": "공동체", "theme-color": "테마 색상", "link-account": "계정 연결", "select-account": "계정 선택", "link-account-warning": "계정을 연결하는 경우 현재 계정에서의 데이터가 연결된 계정으로 동기화되지 않음을 주의해 주세요.", "unlink-account": "연결 끊기", "unlink-account-tips": "이 계정으로 모든 연관 항목에 액세스를 확인합니까?", "login-type": "로그인 유형", "close-daily-review": "일일 리뷰 마감", "max-home-page-width": "홈페이지 최대 너비", "max-home-page-width-tip": "0으로 설정하면 최대 폭입니다.", "no-comments-yet": "아직 댓글이 없습니다.", "author": "저자", "from": "에서", "reply-to": "답장하기", "hub": "허브", "home-site": "홈 사이트", "use-blinko-hub": "Blinko 허브 사용", "full-screen": "전체 화면", "exit-fullscreen": "전체 화면 종료", "no-note-associated": "노트가 연관되지 않았습니다.", "insert-context": "맥락에 삽입하세요.", "follow": "따르다", "follower": "추종자", "following": "다음", "admin": "웹마스터", "site-url": "블링코 사이트 URL", "unfollow": "차단하기", "join-hub": "허브에 참여하기", "refresh": "새로 고침", "comment-notification": "댓글 알림", "follow-notification": "알림 팔로우", "followed-you": "당신을 따라왔습니다.", "mark-all-as-read": "모두 읽음으로 표시하기", "no-notification": "알림 없음", "new-notification": "새로운 알림", "notification": "통지", "backup-success": "백업 성공🎉", "system-notification": "시스템 알림", "embedding-api-endpoint": "API 엔드포인트 임베딩", "embedding-api-key": "API 키 포함하기", "recommand": "추천하기", "has-todo": "할 일", "reference-by": "참조자", "hide-notification": "알림 숨기기", "search-settings": "검색 설정...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "이 작업은 선택한 파일을 삭제하며 복원할 수 없습니다. 확인해 주십시오.", "plugin-settings": "플러그인 설정", "installed-plugins": "설치됨", "marketplace": "시장", "local-development": "현지 개발", "add-local-plugin": "로컬 플러그인 추가", "local-plugin": "로컬 플러그인", "uninstall": "제거하기", "install": "설치하다", "downloads": "다운로드", "plugin-updated": "플러그인이 업데이트되었습니다.", "plugin-update-failed": "플러그인 업데이트 실패", "plugin-connection-failed": "플러그인 연결 실패", "disconnect": "연결 끊기", "local-development-description": "로컬 개발 플러그인을 추가하고 디버깅하세요.", "ai": "인공지능", "ai-chat-box-notes": "아래는 관련 노트가 검색되어 제공된 것입니다.", "add-to-note": "노트에 추가하기", "add-to-blinko": "Blinko에 추가하세요.", "no-title": "제목 없음", "search-blinko-content-or-help-create": "블링코 콘텐츠 검색 또는 함께 만들기...", "conversation-history": "대화 기록", "new-conversation": "새 채팅", "knowledge-base-search": "지식 베이스 검색", "add-tools-to-model": "온라인으로 검색하거나 AI가 blinko API를 호출하도록 허용하세요.", "clear-current-content": "현재 내용 지우기", "welcome-to-blinko": "환영합니다, {{name}}", "coding": "코딩", "ai-prompt-writing": "당신은 프로 작가이니, 사용자가 제공한 주제에 관해 전문적인 기사를 작성해 주세요.", "writing": "글쓰기", "ai-prompt-translation": "사용자가 제공한 텍스트를 {{lang}}(으)로 번역해 주세요. 당신은 전문 번역가입니다.", "ai-prompt-coding": "당신은 전문적인 코더입니다, 사용자가 제공하는 주제에 기반한 단순한 파이썬 프로그램을 작성해 주세요.", "translation": "번역", "first-char-delay": "처음 글자 지연", "total-tokens": "총 토큰", "check-connect": "확인", "check-connect-error": "연결 실패는 /v1의 끝에 추가될 수 있습니다.", "check-connect-success": "연결 확인 성공", "loading": "로딩", "embedding-dimensions": "임베딩 차원", "embedding-dimensions-description": "모델 치수가 정확한지 확인해야 하며, 변경 사항이 있을 때 색인 기록을 다시 빌드하도록 강제해야 합니다.", "model": "모델", "ai-tools": "AI 도구", "tavily-api-key": "Tavily 검색 API 키", "tavily-max-results": "타빌리 맥스 결과", "ai-prompt-writing-content": "200단어의 기사를 작성하고 메모에 저장하십시오.", "ai-prompt-coding-content": "https://github.com/blinko-space/blinko 웹 콘텐츠 추출", "stop-task": "작업 중지", "processing": "처리 중", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "진행 중인 재구축 작업이 있습니다, 다시 시작하시겠습니까?", "hide-blog-images": "블로그 이미지를 숨기십시오", "ai-prompt-translation-content": "지난 이틀 동안 NO 태그 노트를 확인하고 태그를 붙입니다.", "ai-prompt-delete-content": "보관 된 메모 2 개를 찾고 새 메모로 요약하고 저장 한 다음이 두 개의 보관 된 음표를 삭제하십시오.", "older": "더 오래", "newer": "최신", "restore-this-version": "이 버전을 복원하십시오", "Note History": "기록 히스토리", "View History Versions": "히스토리 버전을보십시오", "history-note-only": "주의 :이 역사에는 파일 기록이 아닌 텍스트 내용 만 포함됩니다.", "referenceResource": "참조 리소스", "to-ask-ai": "AI에게 물어보십시오", "press-enter-to-select-first-result": "Enter를 누르면 첫 번째 결과를 선택하십시오", "ask-ai": "AI에게 물어보세요", "ask-blinko-ai-about-this-query": "이 쿼리에 대해 Blinko AI에게 문의하십시오", "search-or-ask-ai": "검색 메모, 설정 또는 ai ...", "plugin": "플러그인", "editor-preview": "편집자", "auto-add-tags": "자동 추가 태그", "add-as-comment": "주석으로 추가하십시오", "choose-what-to-do-with-ai-results": "AI 결과로 무엇을 해야하는지 선택하십시오", "ai-post-processing-mode": "AI 게시물 처리 모드", "ai-post-processing-prompt": "AI 포스트 프로세싱 코멘트 프롬프트", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "다음 주 내용을 분석하고 적절한 태그를 제안하고 간단한 요약을 제공하십시오.", "column": "열", "content-generated-by-ai": "AI에 의해 생성 된 내용", "define-custom-prompt-for-ai-to-process-notes": "현재 노트에 대해 AI가 의견을 내도록 운영하십시오. 예를 들어: 노트의 내용을 요약해 주세요. 만약 노트의 내용이 10단어 이하라면, 그것을 다듬어 주세요.", "enter-custom-prompt-for-post-processing": "포스트 처리를 위해 사용자 정의 프롬프트를 입력하십시오", "enter-spotify-consumer-secret": "Spotify Consumer Secret을 입력하십시오", "music-settings": "음악 설정", "preview": "시사", "prompt-used-for-post-processing-notes": "포스트 처리 노트에 사용되는 프롬프트", "rebuild": "재건", "rebuild-in-progress": "진행중인 재건", "rebuilding-embedding-progress": "임베드 진행 상황 재건", "record": "레코드/엔드 레코드를 시작하십시오", "record-tip": "장치는 녹음을 지원하지 않습니다", "redo": "다시 하다", "remove": "제거하다", "rest-user-info": "휴식 사용자 정보", "row": "열", "select-model": "모델을 선택하십시오", "set-as-public": "대중으로 설정합니다", "setting": "", "spin": "회전", "split-view": "분할보기", "spotify-consumer-key": "Spotify API 키", "spotify-consumer-secret": "Spotify API Secret", "strike": "스트라이크", "superadmin": "", "table": "테이블", "text-is-not-empty": "텍스트 (빈 공간 없음)", "title": "제목", "tooltip-text": "툴팁 텍스트", "undo": "끄르다", "up": "위로", "update": "업데이트", "updated-at": "업데이트", "upload-error": "오류를 업로드하십시오", "uploading": "업로드 ...", "user": "", "user-list": "사용자 목록", "weeks-ago": "", "wysiwyg": "Wysiwyg", "years-ago": "", "to-search-tags": "태그 검색", "app-upgrade-required": "앱 업그레이드가 필요합니다", "current-app-version": "현재 APP 버전", "required-app-version": "필요한 APP 버전", "upgrade": "업그레이드", "online-search": "온라인 검색", "smart-edit": "스마트 에딧", "function-call-required": "필요한 함수 호출", "smart-edit-prompt": "스마트 에디트 프롬프트", "define-instructions-for-ai-to-edit-your-notes": "노트를 조작하기 위한 프롬프트를 사용할 수 있습니다. 예를 들어, 노트에 링크가 포함된 경우 원래 노트 아래에 링크 내용을 요약하고 라벨을 생성하세요.", "rebuild-started": "재구축 시작됨", "rebuild-stopped-by-user": "사용자에 의해 재구축이 중단되었습니다.", "random-mode": "무작위 보행", "related-notes": "관련 노트", "no-related-notes-found": "관련된 노트를 찾을 수 없습니다.", "advanced": "고급", "rerank-model-description": "검색 정확도를 향상시키기 위해 벡터 결과 재정렬을 위한 모델을 지정하십시오.", "rerank-model": "모델 재순위", "rerank": "재순위 매기기", "use-custom-rerank-endpoint-description": "활성화되면, 내장된 모델의 엔드포인트와 API 키가 재정렬됩니다.", "use-embedding-endpoint": "임베딩 엔드포인트 사용", "rerank-score-description": "재정렬 모델을 위한 점수 임계값을 설정하십시오. 이 값 미만의 결과는 필터링됩니다.", "public-share": "공유 공개", "internal-share": "내부 공유", "no-team-members-found": "팀 멤버가 발견되지 않았습니다.", "selected-users": "선택된 사용자들", "tags-prompt": "태그 프롬프트", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "자동으로 태그를 생성하는 데 사용되는 프롬프트입니다. 비어 있게 설정하면 기본 프롬프트가 사용됩니다.", "generate-low-permission-token": "낮은 권한 토큰 생성", "low-permission-token-desc": "낮은 권한 토큰은 upsertNote 엔드포인트 및 AI 채팅 엔드포인트에만 접근할 수 있습니다. 계정 정보나 다른 노트에는 접근할 수 없습니다. 이는 Telegram 봇이나 WeChat 봇과 같이 다른 어떤 노트에도 접근하지 못하게 하고 싶은 사용 사례에 이상적입니다.", "this-token-is-only-displayed-once-please-save-it-properly": "이 토큰은 한 번만 표시되므로 제대로 저장해 주세요.", "refresh-model-list": "모델 목록 가져오기", "please-set-the-embedding-model": "내장 모델을 설정해 주세요.", "blinko-endpoint": "Blinko 엔드포인트", "enter-blinko-endpoint": "당신의 Blinko 배포 URL", "login-failed": "로그인 실패", "verification-failed": "인증 실패", "download-success": "다운로드 성공", "download-failed": "다운로드 실패", "downloading": "다운로드 중", "hide-pc-editor": "PC 버전 편집기 숨기기", "import-from-markdown": "Markdown 파일에서 가져오기", "import-from-markdown-tip": "단일 .md 파일 또는 .md 파일이 포함된 .zip 아카이브에서 가져오기", "not-a-markdown-or-zip-file": "Markdown이나 zip 파일이 아닙니다. .md 또는 .zip 파일을 선택하세요.", "todo": "대행", "restore": "복구", "complete": "완료", "today": "오늘", "yesterday": "어제", "common.refreshing": "새로 고침 중", "common.releaseToRefresh": "새로 고침을 위해 놓으십시오", "common.pullToRefresh": "드롭 다운으로 새로 고침", "edit-message-warning": "이 메시지를 편집하면 모든 후속 대화 기록이 지워지고 AI 응답이 다시 생성됩니다.", "enter-your-message": "당신의 메시지를 입력하십시오.", "set-deadline": "마감일 설정", "expired": "만료되었습니다.", "expired-days": "만료 {{count}}일 경과", "expired-hours": "만료 {{count}}시간", "expired-minutes": "만료 {{count}}분", "days-left": "{{count}}일 후", "hours-left": "{{count}}시간 후에", "minutes-left": "{{count}}분 후", "about-to-expire": "곧 만료됨", "1-day": "1일", "1-week": "일주", "1-month": "한 달", "quick-select": "빠른 선택", "import-ai-configuration": "AI 구성 가져오기", "would-you-like-to-import-this-configuration": "이 AI 설정을 가져오시겠습니까?", "detected-ai-configuration-to-import": "AI 구성을 가져올 준비가 완료되었습니다.", "importing": "가져오는 중", "cache-cleared-successfully": "캐시가 성공적으로 삭제되었습니다! 페이지가 자동으로 새로고침됩니다.", "failed-to-clear-cache": "브라우저 캐시를 삭제하지 못했습니다. 수동 새로고침(Ctrl+Shift+R)을 시도해 주세요.", "select-deployment": "배포 선택", "deployment-name": "배포 이름", "please-set-the-api-endpoint": "API 엔드포인트를 설정하세요", "please-set-the-api-key": "API 키를 설정하세요"}