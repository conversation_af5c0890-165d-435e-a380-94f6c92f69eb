{"spotify-consumer-key-tip": "Solía obtener portadas de música mp3.", "spotify-consumer-key-tip-2": "Obtén la clave de API en https://developer.spotify.com/", "blinko": "Blinko", "notes": "Notas", "resources": "Recursos", "archived": "Archivado", "settings": "<PERSON><PERSON><PERSON><PERSON>", "total-tags": "TOTAL TAGS", "search": "Busca...", "i-have-a-new-idea": "Tengo una nueva idea...", "add-tag": "Añadir etiqueta", "ai-model": "Modelo de IA", "already-have-an-account-direct-login": "¿Ya tiene una cuenta? Acceso directo", "api-endpoint": "Punto final de la API", "archive": "Archivo", "backup-file": "ARCHIVO DE COPIA DE SEGURIDAD", "change-type": "Tipo de cambio", "confirm": "Confirme", "confirm-password": "Confirmar con<PERSON>", "confirm-to-delete": "¡Confirmar para borrar!", "confirm-your-password": "Confirme su contraseña", "confrim": "Confirme", "create-successfully-is-about-to-jump-to-the-login": "<PERSON><PERSON><PERSON> con éxito, está a punto de saltar a la entrada", "daily-review": "Análisis diario", "delete": "Bo<PERSON>r", "delete-confirm": "<PERSON><PERSON><PERSON>", "delete-success": "Borrar con éxito", "detail": "Detalle", "edit": "<PERSON><PERSON>", "enter-your-name": "Introduzca su nombre", "enter-your-password": "Introduzca su contraseña", "enter-your-username": "Introduzca su nombre de usuario", "every-day": "Todos los días", "every-half-year": "Cada semestre", "every-month": "Todos los meses", "every-three-month": "Cada tres meses", "every-week": "Cada semana", "hello": "hola", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "<PERSON><PERSON> {{nombre}}, puedo buscarte las notas, ¿en qué puedo ayudarte hoy?", "import-from-bko": "Importar desde .bko", "in-progress": "En curso...", "insert-codeblock": "Insertar codeBlock", "insert-hashtag": "Insertar hashtag", "insert-table": "Insertar tabla", "items": "<PERSON><PERSON><PERSON><PERSON>", "keep-sign-in": "Mantener registro", "multiple-select": "Sele<PERSON><PERSON> múl<PERSON>", "must-start-with-http-s-or-use-api-openai-as-default": "Debe empezar por http(s):// o utilizar /api/openai por defecto", "need-to-create-an-account": "¿Necesita crear una cuenta?", "new-version-detected-click-to-get-the-latest-version": "🎉Nueva versión detectada, haga clic para obtener la última versión", "nickname": "apodo", "no-data-here-well-then-time-to-write-a-note": "¿No hay datos aquí? Enton<PERSON>, ¡es hora de escribir una nota!", "not-a-bko-file": "no es un archivo bko", "note": "<PERSON>a", "numbered-list": "Lista numerada", "operation-failed": "Operación fallida.", "password": "contraseña", "recovery": "Recuperación", "required-items-cannot-be-empty": "Los elementos obligatorios no pueden estar vacíos", "rest-user-password": "Resto de la contraseña de usuario", "reviewed": "<PERSON><PERSON><PERSON>", "running": "<PERSON><PERSON><PERSON><PERSON>", "save": "Guardar", "schedule": "PROGRAMA", "schedule-archive-blinko": "Calendario Archivo Blinko", "schedule-back-up": "Programar copia de seguridad", "schedule-task": "Programar tarea", "show-less": "<PERSON><PERSON> menos", "show-navigation-bar-on-mobile": "Barra de navegación oculta en el móvil", "sign-in": "In<PERSON><PERSON>", "sign-up": "Inscribirse", "status": "ESTADO", "stopped": "Detenido", "the-two-passwords-are-inconsistent": "Las dos contraseñas son incoherentes", "theme": "<PERSON><PERSON>", "there-are-no-resources-yet-go-upload-them-now": "Todavía no hay recursos, súbelos ahora", "this-operation-will-be-delete-resource-are-you-sure": "Esta operación será borrar recurso ¿estás seguro?", "top": "Top", "total": "Total", "update-successfully": "Actualizar con éxito", "upload-file": "Cargar archivo", "use-ai": "Util<PERSON>r ai", "user-custom-openai-api-key": "Clave Api OpenAI personalizada por el usuario", "user-custom-azureopenai-api-instance": "Nombre de la instancia de Azure OpenAI", "user-custom-azureopenai-api-deployment": "Nombre de implementación de Azure OpenAI", "user-custom-azureopenai-api-version": "Versión API", "username": "nombre de usuario", "your-changes-have-been-saved": "Tus cambios se han guardado.", "all-notes-have-been-loaded": "Todas las notas {{items}} han sido cargadas", "ask-about-your-notes": "Pregunte por sus notas", "basic-information": "Información básica", "bulleted-list": "Lista con viñetas", "cancel": "<PERSON><PERSON><PERSON>", "cancel-top": "<PERSON><PERSON><PERSON>", "change-user-info": "Cambiar la información de usuario", "check-list": "Lista de control", "congratulations-youve-reviewed-everything-today": "lo has revisado todo hoy.", "convert-to": "Convertir a", "convert-to-blinko": "Convertir a Blinko", "enter-send-shift-enter-for-new-line": "<PERSON>tro enviar, <PERSON><PERSON>+Intro para nueva línea", "language": "Idioma", "last-run": "ÚLTIMA CARRERA", "logout": "Cierre de sesión", "model-provider": "<PERSON><PERSON>", "name": "Nombre", "name-db": "NOMBRE", "no-tag-found": "No se ha encontrado ninguna etiqueta", "preference": "Preferencia", "recording": "Grabación", "show-more": "<PERSON>er más", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "Esta operación elimina la etiqueta asociada y no se puede restaurar por favor confirme", "user-or-password-error": "Error de usuario o contraseña", "convert-to-note": "Convertir en nota", "import": "Importar", "create-successfully": "<PERSON><PERSON><PERSON> con éxito", "insert-sandpack": "Insertar saco de arena", "created-in": "Creado en", "set-as-public": "Establecer como público", "unset-as-public": "Desactivado como Público", "no-tag": "Sin etiqueta", "with-link": "Con enlace", "has-file": "Tiene archivo", "created-at": "<PERSON><PERSON><PERSON> en", "user-list": "Lista de usuarios", "create-user": "<PERSON><PERSON><PERSON> usuario", "import-from-memos-memos_prod-db": "Importar de Memos(memos_prod.db)", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "Al exportar memos_prod.db, cierre el contenedor de memos para evitar la pérdida parcial de datos.", "go-to-share-page": "Ir a la página para compartir", "import-done": "Importación realizada", "rebuilding-embedding-progress": "Reconstruir Integrar el progreso", "rebuild-embedding-index": "Reconstruir índice de incrustación", "rebuild": "Reconstruir", "notes-imported-by-other-means-may-not-have-embedded-vectors": "Los billetes importados por otros medios pueden no tener vectores incrustados", "order-by-create-time": "Ordenar por hora de creación", "time-format": "Formato de hora", "version": "Versión", "new-version-available": "Nueva versión disponible", "storage": "Almacenamiento", "local-file-system": "Sistema de archivos local", "object-storage": "Almacenamiento de objetos", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "Además del modelo GPT, es necesario garantizar que sea posible invocar el modelo", "speech-recognition-requires-the-use-of": "El reconocimiento de voz requiere el uso de", "ai-expand": "AI Ampliar", "ai-polish": "AI Polaco", "accept": "Acepte", "reject": "<PERSON><PERSON><PERSON>", "stop": "Stop", "card-columns": "Columnas de tarjetas", "select-a-columns": "Seleccione una columna", "width-less-than-1024px": "Anchura inferior a 1024px", "width-less-than": "Anchura inferior a", "small-device-card-columns": "Columnas de la tarjeta de dispositivo pequeño", "medium-device-card-columns": "Columnas de la tarjeta de dispositivo medio", "large-device-card-columns": "Columnas grandes de la tarjeta de dispositivo", "device-card-columns": "Columnas de la tarjeta de dispositivo", "columns-for-different-devices": "Columnas para distintos dispositivos", "mobile": "Móvil", "tablet": "Tableta", "desktop": "Escritorio", "chars": "Caracteres", "text-fold-length": "Longitud del pliegue del texto", "title-first-line-of-the-text": "T<PERSON><PERSON>lo(primera línea del texto)", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "Contenido(resto del texto,si el texto supera la longitud)", "ai-tag": "Etiqueta AI", "article": "<PERSON><PERSON><PERSON><PERSON>", "embedding-model": "Modelo de incrustación", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "Si tienes muchos billetes puedes consumir un número determinado de fichas", "force-rebuild": "Reconstrucción forzosa", "force-rebuild-embedding-index": "La reconstrucción forzada reconstruirá por completo todos los datos indexados.", "embedding-model-description": "El índice debe reconstruirse después de cambiar de modelo integrado.", "top-k-description": "Número máximo de documentos devueltos", "embedding-score-description": "El umbral de similitud de las consultas suele ser la distancia de la suma euclídea", "embedding-lambda-description": "Resultado de la consulta Parámetro de ponderación de la diversidad", "update-tag-icon": "Actualizar icono de etiqueta", "delete-only-tag": "<PERSON><PERSON><PERSON> s<PERSON>lo etiqueta", "delete-tag-with-note": "Eliminar etiqueta con nota", "update-tag-name": "Actualizar nombre de etiqueta", "thinking": "Pensando...", "select-all": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "deselect-all": "<PERSON>elecci<PERSON><PERSON> todo", "insert-before": "Insertar antes de", "insert-after": "Insertar después de", "update-name": "Actualizar nombre", "ai-emoji": "<PERSON>", "custom-icon": "Icono personalizado", "ai-enhanced-search": "Búsqueda mejorada por IA", "preview-mode": "Modo de vista previa", "source-code": "<PERSON><PERSON><PERSON> fuente", "camera": "<PERSON><PERSON><PERSON>", "reference": "Referencia", "reference-note": "Nota de referencia", "source-code-mode": "Modo de código fuente", "heading": "Rúbrica", "paragraph": "<PERSON><PERSON><PERSON><PERSON>", "quote": "Cita", "bold": "Negrita", "remove-italic": "Eliminar cursiva", "underline": "<PERSON><PERSON><PERSON>", "italic": "Cursiva", "remove-bold": "Eliminar negrita", "remove-underline": "Eliminar subrayado", "select-block-type": "Seleccione el tipo de bloque", "block-type-select-placeholder": "Tipo de bloque", "trash": "basura", "custom-path": "<PERSON>uta <PERSON>", "page-size": "Tamaño de página", "toolbar-visibility": "Visibilidad de la barra de herramientas", "always-hide-toolbar": "Esconderse siempre", "always-show-toolbar": "<PERSON>rar siempre", "hide-toolbar-on-mobile": "Ocultar en el móvil", "select-toolbar-visibility": "Seleccionar la visibilidad de la barra de herramientas", "select-a-time-format": "Seleccione un formato de hora", "enter-code-shown-on-authenticator-app": "Introduzca el código que aparece en la aplicación de autenticación", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "Abra la aplicación de autenticación de terceros e introduzca los códigos que aparecen en pantalla.", "two-factor-authentication": "Autenticación de dos factores", "scan-this-qr-code-with-your-authenticator-app": "Escanea este código QR con tu aplicación de autenticación", "or-enter-this-code-manually": "O introduzca este código manualmente:", "verify": "Verifique", "upload": "<PERSON><PERSON>", "days": "Días", "select-model-provider": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON>", "allow-register": "<PERSON><PERSON><PERSON>", "access-token": "Ficha de acceso", "bucket": "<PERSON><PERSON><PERSON>", "region": "Región", "access-key-secret": "Clave de acceso secreta", "access-key-id": "Identificador de la clave de acceso", "share-and-copy-link": "Compartir y copiar enlace", "copy-share-link": "<PERSON><PERSON><PERSON> enlace compartido", "endpoint": "Punto final", "export-format": "Formato de exportación", "export": "Exportar", "time-range": "Intervalo de tiempo", "all": "Todos", "exporting": "Exportar...", "has-image": "Tiene Imagen", "has-link": "<PERSON>iene enlace", "filter-settings": "Ajustes de filtro", "tag-status": "Estado de la etiqueta", "all-notes": "Todas las notas", "with-tags": "Con etiquetas", "without-tags": "Sin etiquetas", "select-tags": "Seleccionar etiquetas", "additional-conditions": "Condiciones adicionales", "apply-filter": "Aplicar filtro", "to": "A", "start-date": "Fecha de inicio", "end-date": "Fecha final", "reset": "Restablecer", "no-condition": "Ninguna condición", "public": "Público", "ai-model-tooltip": "Introduzca el nombre del modelo a utilizar, como gpt-3.5-turbo, gpt-4, gpt-4o, gpt-4o-mini", "user-custom-azureopenai-api-deployment-tooltip": " Introduzca el nombre de despliegue que desea utilizar, por ejemplo gpt-4o", "ollama-ai-model-tooltip": "Introduce el nombre del modelo a utilizar, por ejemplo llama3.2", "ollama-default-endpoint-is-http-localhost-11434": "El punto final por defecto de Ollama es http://localhost:11434", "your-azure-openai-instance-name": "Nombre de su instancia de Azure OpenAI", "ai-generate-emoji": "", "ai-generating-emoji": "", "api-key": "", "date-range": "", "days-ago": "", "enter-your-api-key": "", "hours-ago": "", "impoort-from-bko": "", "minutes-ago": "", "months-ago": "", "superadmin": "", "user": "", "weeks-ago": "", "years-ago": "", "about": "Acerca de", "alternate-text": "Texto alternativo", "close": "<PERSON><PERSON><PERSON>", "code": "Bloque de código", "code-theme": "Vista previa del tema de bloque de código", "content-theme": "Vista previa del tema del contenido", "delete-row": "Eliminar columna", "devtools": "Herramientas de desarrollo", "down": "Abajo", "download-tip": "El navegador no es compatible con la función de descarga", "edit-mode": "Alternar modo de edición", "edit-user": "<PERSON>ar usuario", "emoji": "<PERSON><PERSON><PERSON>", "exclude-tag-from-embedding": "Excluir contenido etiquetado", "exclude-tag-from-embedding-desc": "Seleccione una etiqueta para excluir sus notas asociadas de la generación de vectores de incrustación de IA.", "exclude-tag-from-embedding-tip": "Las notas con esta etiqueta serán excluidas del procesamiento de incrustación de IA.", "file-type-error": "El tipo de archivo es error", "follow-system": "Sistema de seguimiento", "footnote-ref": "Nota al pie de página", "fullscreen": "Alternar pantalla completa", "generate": "Generando", "heading1": "Título 1", "heading2": "Encabezado 2", "heading3": "Título 3", "heading4": "Encabezado 4", "heading5": "Encabezado 5", "heading6": "Encabezado 6", "headings": "Encabezados", "help": "<PERSON><PERSON><PERSON>", "image-url": "URL de la imagen", "indent": "<PERSON><PERSON><PERSON>", "info": "Información", "inline-code": "Código en línea", "insert-column-left": "Insertar 1 izquierda", "insert-column-right": "Inserte 1 a la derecha", "insert-row-above": "Insertar 1 arriba", "insert-row-below": "Inserte 1 debajo", "instant-rendering": "Renderizado <PERSON>", "light-mode": "<PERSON>do claro", "line": "Lín<PERSON>", "link": "Enlace", "link-ref": "<PERSON>lace de referencia", "list": "Lista", "more": "Más", "name-empty": "El nombre está vacío", "ordered-list": "Lista de pedidos", "original-password": "Contraseña original", "outdent": "<PERSON><PERSON>", "outline": "Esquema", "over": "sobre", "performance-tip": "La vista previa en tiempo real requiere ${x} ms, puedes cerrarla", "preview": "Vista previa", "search-tags": "Buscar etiquetas", "insert-attachment-or-note": "¿Insertar en el archivo adjunto o en la nota?", "context": "Contexto", "paste-to-note-or-attachment": "¿Estás seguro de pegar en el contexto o adjuntar?", "attachment": "Adjunto", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "Después de la eliminación, todos los datos de usuario serán borrados y no se podrán recuperar.", "upload-completed": "Carga completada", "upload-cancelled": "Subida cancelada", "upload-failed": "Error al subir", "import-from-bko-tip": "La carga a s3 para recuperación no está soportada en este momento. Por favor, deshabilite temporalmente la opción s3 cuando desee recuperar.", "edit-time": "Tiempo de edición", "ai-write": "IA Escribir", "download": "<PERSON><PERSON><PERSON>", "rename": "<PERSON><PERSON><PERSON><PERSON>", "move-up": "Subir", "cut": "Cortar", "paste": "<PERSON><PERSON><PERSON>", "confirm-delete": "Confirmar <PERSON>", "confirm-delete-content": "¿Estás seguro de que deseas eliminar {{name}}? Esta acción no se puede deshacer.", "folder-name": "Nombre de la carpeta", "file-name": "Nombre del archivo", "operation-success": "Operación exitosa", "cloud-file": "Archivo en la nube", "move-to-parent": "Mover al padre", "no-resources-found": "No se encontraron recursos.", "operation-in-progress": "Operación en progreso", "new-folder": "Nueva Carpeta", "folder-name-exists": "Nombre de carpeta existente", "folder-name-required": "Nombre de carpeta requerido", "collapse": "Colapso", "show-all": "<PERSON><PERSON> todo", "sun": "Sol", "mon": "<PERSON><PERSON>", "wed": "<PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "Viernes", "sat": "Sábado", "heatMapTitle": "Mapa de calor de notas del último año", "heatMapDescription": "Muestra la cantidad de notas creadas por día.", "select-month": "<PERSON><PERSON><PERSON><PERSON><PERSON> mes", "note-count": "Recuento de notas", "max-daily-words": "Palabras diarias m<PERSON>", "active-days": "Días Activos", "total-words": "Palabras totales", "analytics": "Analítica", "tag-distribution": "Distribución de etiquetas", "other-tags": "Otras etiquetas", "tue": "<PERSON><PERSON>", "offline-status": "<PERSON>do sin conexión", "offline-title": "Estás desconectado.", "offline-description": "Por favor, verifica tu conexión a internet e intenta de nuevo.", "retry": "Reintentar", "back-to-home": "Volver a casa", "offline": "Sin conexión", "close-background-animation": "Cerrar Animación de Fondo", "custom-bg-tip": "Ve a https://www.shadergradient.co/ para crear tu propio fondo degradado.", "custom-background-url": "Fondo personalizado", "share": "Compartir", "need-password-to-access": "Se requiere contraseña para acceder.", "password-error": "E<PERSON>r de contraseña", "cancel-share": "Cancelar Compartir", "create-share": "<PERSON><PERSON><PERSON>", "share-link": "Compartir enlace", "set-access-password": "Establecer contraseña de acceso", "protect-your-shared-content": "Proteja su contenido compartido", "access-password": "Contraseña de acceso", "select-date": "<PERSON><PERSON><PERSON><PERSON><PERSON> fecha", "expiry-time": "Tiempo de expiración", "select-expiry-time": "Seleccionar tiempo de vencimiento", "permanent-valid": "Válido permanente", "7days-expiry": "Caducidad de 7 días", "custom-expiry": "Caducidad personalizada", "30days-expiry": "Caducidad de 30 días.", "share-link-expired": "Enlace compartido caducado", "share-link-expired-desc": "¡Esta compartición ha caducado, por favor contacta al administrador para volver a compartir!", "shared": "Compartido", "internal-shared": "Compartido internamente", "edited": "Editado", "move-down": "Mover hacia abajo", "provider-id": "ID del proveedor", "provider-name": "Nombre del proveedor", "well-known-url": "URL bien conocida", "authorization-url": "URL de autorización", "token-url": "URL de token", "userinfo-url": "URL de información del usuario", "scope": "Alcance", "client-id": "ID de cliente", "client-secret": "Clave secreta del cliente", "sso-settings": "Configuraciones de SSO", "oauth2-providers": "Proveedores de OAuth2", "add-oauth2-provider": "A<PERSON><PERSON><PERSON> proveedor <PERSON>h2", "add-provider": "<PERSON><PERSON><PERSON>", "action": "Acción", "edit-oauth2-provider": "<PERSON>ar proveedor OAuth2", "confirm-delete-provider": "Confirmar <PERSON><PERSON>", "please-select-icon-from-iconify": "Por favor selecciona un icono de Iconify.", "provider-icon": "<PERSON><PERSON><PERSON> proveedor", "select-provider-template": "Seleccionar <PERSON>illa de Proveedor", "provider-template": "Plantilla de proveedor", "please-add-this-url-to-your-oauth-provider-settings": "Por favor, agregue este URL a la configuración de su proveedor de OAuth.", "redirect-url": "Redireccionar URL", "sign-in-with-provider": "Inicia se<PERSON> con {{proveedor}}", "community": "Comunidad", "theme-color": "Color del tema", "link-account": "Vincular cuenta", "select-account": "<PERSON><PERSON><PERSON><PERSON><PERSON> cuenta", "link-account-warning": "Tenga en cuenta que si vincula sus cuentas, los datos de la cuenta actual no se sincronizarán con la cuenta vinculada.", "unlink-account": "Desvincular cuenta", "unlink-account-tips": "¿Confirma el acceso a todas las asociaciones con esta cuenta?", "login-type": "Tipo de inicio de sesión", "close-daily-review": "Revisión Diaria Cercana", "max-home-page-width": "<PERSON><PERSON> m<PERSON> de la página de inicio", "max-home-page-width-tip": "Si se establece en 0, es el ancho máximo.", "no-comments-yet": "Todavía no hay comentarios", "author": "Autor", "from": "De", "reply-to": "Responder a", "comment": "Comentario", "hub": "<PERSON><PERSON><PERSON><PERSON>", "home-site": "Sitio web", "use-blinko-hub": "<PERSON><PERSON><PERSON>link<PERSON>", "full-screen": "Pantalla completa", "exit-fullscreen": "Salir de pantalla completa", "no-note-associated": "No hay nota asociada", "insert-context": "Insertar en el contexto", "follow": "<PERSON><PERSON><PERSON>", "follower": "<PERSON><PERSON><PERSON><PERSON>", "following": "<PERSON><PERSON><PERSON><PERSON>", "admin": "Administrador de página web", "site-url": "URL del sitio de Blinko", "unfollow": "<PERSON><PERSON>", "join-hub": "Únete a Hub", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "comment-notification": "Notificación de comentario", "follow-notification": "Notificación de seguimiento", "followed-you": "te seguí", "mark-all-as-read": "Marcar todo como leído.", "no-notification": "Sin notificación.", "new-notification": "Nueva notificación", "notification": "Notificación", "backup-success": "Éxito de la copia de seguridad🎉", "system-notification": "Notificación del sistema", "embedding-api-endpoint": "Incrustación de punto final de API", "embedding-api-key": "Incrustar clave de API", "recommand": "Recomendar", "has-todo": "Tiene TODO", "reference-by": "Referencia Por", "hide-notification": "Ocultar notificación", "search-settings": "Configuración de búsqueda...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "Esta operación eliminará los archivos seleccionados y no se podrá restaurar. Por favor, confirme.", "plugin-settings": "Ajuste del plugin", "installed-plugins": "Instalado", "marketplace": "<PERSON><PERSON><PERSON>", "local-development": "Desarrollo Local", "add-local-plugin": "Agregar complemento local", "local-plugin": "Plugin local", "uninstall": "<PERSON><PERSON><PERSON><PERSON>", "install": "Instalar", "downloads": "Descargas", "plugin-updated": "Plugin actualizado", "plugin-update-failed": "Error al actualizar el plugin", "plugin-connection-failed": "Conexión del complemento fallida", "disconnect": "Desconectar", "local-development-description": "Agregar un complemento de desarrollo local y depurarlo.", "ai": "IA", "ai-chat-box-notes": "A continuación se muestran las notas relevantes recuperadas para usted.", "copy": "Copiar", "add-to-note": "Añadir a la nota", "add-to-blinko": "Agregar a Blinko", "no-title": "Sin título", "search-blinko-content-or-help-create": "Buscar contenido en blinko o ayudar a crear...", "conversation-history": "Historial de conversaciones", "new-conversation": "Nuevo Chat", "knowledge-base-search": "Búsqueda en base de conocimientos", "add-tools-to-model": "Busca en línea o permite que la IA realice una llamada a la API de blinko.", "clear-current-content": "<PERSON><PERSON>r contenido actual", "welcome-to-blinko": "Bienvenido(a), {{name}}", "ai-prompt-writing": "Eres un escritor profesional, por favor escribe un artículo profesional sobre el tema proporcionado por el usuario.", "coding": "Programación", "writing": "Escritura", "ai-prompt-translation": "Usted es un traductor profesional, por favor traduzca el texto proporcionado por el usuario al {{lang}}", "ai-prompt-coding": "Eres un programador profesional, por favor escribe un programa de Python sencillo basado en el tema proporcionado por el usuario.", "translation": "Traducción", "first-char-delay": "Primera demora de carácter", "total-tokens": "Total de tokens", "check-connect": "Rev<PERSON><PERSON>", "check-connect-error": "La falla de conexión puede ser añadida al final de /v1.", "check-connect-success": "Verificar Conexión Exitosa", "loading": "Cargando", "embedding-dimensions": "Dimensiones de incrustación", "embedding-dimensions-description": "Debes asegurarte de que las dimensiones del modelo sean correctas y necesitas forzar la reconstrucción de los registros del índice después de los cambios.", "model": "<PERSON><PERSON>", "ai-tools": "Herramientas de IA", "tavily-api-key": "Clave de API de Búsqueda Tavily", "tavily-max-results": "Resultados Máximos de Tavily", "ai-prompt-writing-content": "Escribe un artículo de 200 palabras y guárdalo en tus notas", "ai-prompt-coding-content": "Extrayendo contenido web de https://github.com/blinko-space/blinko", "stop-task": "<PERSON><PERSON>", "processing": "Procesamiento", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "Hay una tarea de reconstrucción en progreso, ¿quieres reiniciar?", "hide-blog-images": "Ocultar imágenes de blog", "ai-prompt-translation-content": "Verifique las notas de no etiquetas en los últimos dos días y etiquétalas.", "ai-prompt-delete-content": "Encuentre 2 notas archivadas, resume y guárdelas como nuevas notas, y elimine estas dos notas archivadas", "older": "Más viejo", "newer": "Más nuevo", "restore-this-version": "Restaurar esta versión", "Note History": "Nota historia", "View History Versions": "Ver versiones de historia", "history-note-only": "Atención: este historial contiene solo contenido de texto, no historial de archivos", "referenceResource": "Recurso de referencia", "to-ask-ai": "Preguntarle a ai", "press-enter-to-select-first-result": "Presione Entrar para seleccionar el primer resultado", "ask-ai": "Pregúntale a AI", "ask-blinko-ai-about-this-query": "Pregúntale a Blinko Ai sobre esta consulta", "search-or-ask-ai": "Nota de búsqueda, configuración o preguntar Ai ...", "plugin": "Complemento", "editor-preview": "editor", "both": "Ambos", "auto-add-tags": "Auto Agregar etiquetas", "add-as-comment": "Agregar como comentario", "choose-what-to-do-with-ai-results": "Elija qué hacer con los resultados de IA", "ai-post-processing-mode": "Modo de procesamiento posterior a AI", "ai-post-processing-prompt": "Comentario de solicitud de procesamiento posterior de IA", "2fa-setup-successful": "Configuración de 2FA exitosa", "align-center": "Centro", "align-left": "Iz<PERSON>erda", "align-right": "Bien", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "Analice el contenido de la siguiente nota y sugiera etiquetas apropiadas y proporcione un breve resumen", "check": "Lista de tareas", "column": "Columna", "content-generated-by-ai": "Contenido generado por AI", "copied": "Copiado", "dark-mode": "<PERSON><PERSON> oscuro", "define-custom-prompt-for-ai-to-process-notes": "Opera la IA para comentar sobre la nota actual. Por ejemplo: Por favor, resume el contenido de la nota. Si el contenido de la nota es menos de 10 palabras, por favor corrígelo para mí.", "delete-column": "Eliminar la fila", "enter-custom-prompt-for-post-processing": "Ingrese el indicador personalizado para el procesamiento posterior", "enter-spotify-consumer-key": "Ingrese la tecla API de Spotify", "enter-spotify-consumer-secret": "Ingrese a Spotify Consumer Secret", "music-settings": "Configuración musical", "prompt-used-for-post-processing-notes": "Aviso utilizado para las notas de procesamiento posterior", "rebuild-in-progress": "Reconstruir en progreso", "record": "Registro de registro/final de inicio", "record-tip": "El dispositivo no admite la grabación", "redo": "<PERSON><PERSON><PERSON>", "remove": "Eliminar", "rest-user-info": "Información de usuario de descanso", "role": "Role", "row": "<PERSON><PERSON>", "select-model": "<PERSON><PERSON>", "setting": "", "spin": "<PERSON><PERSON><PERSON>", "split-view": "Vista dividida", "spotify-consumer-key": "Clave de API de Spotify", "spotify-consumer-secret": "Secreto de la API de Spotify", "strike": "<PERSON><PERSON><PERSON>", "table": "Mesa", "text-is-not-empty": "texto (sin vacío)", "title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip-text": "Texto de información sobre herramientas", "undo": "<PERSON><PERSON><PERSON>", "up": "Arriba", "update": "Actualizar", "updated-at": "Actualizar a", "upload-error": "<PERSON><PERSON>r de carga", "uploading": "Subiendo ...", "wysiwyg": "Wysiwyg", "enable-ai-post-processing": "Habilitar el procesamiento posterior a la IA", "automatically-process-notes-after-creation-or-update": "Procesar automáticamente las notas después de la creación", "can-generate-summaries-tags-or-perform-analysis": "Puede generar etiquetas resúmenes en el análisis de rendimiento", "ai-post-processing": "AI después del procesamiento", "model-list-updated": "Lista de modelos actualizada", "to-search-tags": "Para buscar etiquetas", "app-upgrade-required": "Actualización de la aplicación requerida", "current-app-version": "Versión actual de la APP", "required-app-version": "Versión de APP requerida", "upgrade": "Actualización", "online-search": "Búsqueda en línea", "smart-edit": "Edición Inteligente", "function-call-required": "Se Requiere Llamada de Función", "smart-edit-prompt": "Indicación de Edición Inteligente", "define-instructions-for-ai-to-edit-your-notes": "<PERSON>uede usar indicaciones para manipular notas, por ejemplo: si una nota contiene un enlace, resume el contenido del enlace debajo de la nota original y genere una etiqueta.", "rebuild-started": "Reconstrucción Iniciada", "rebuild-stopped-by-user": "Reconstrucción detenida por el usuario", "random-mode": "Caminata Aleatoria", "related-notes": "Notas relacionadas", "no-related-notes-found": "No se encontraron notas relacionadas", "advanced": "<PERSON><PERSON><PERSON>", "rerank-model-description": "Especificar un modelo para reordenar resultados de vectores para mejorar la precisión de búsqueda", "rerank-model": "Modelo de reordenamiento", "rerank": "Reordenar", "use-custom-rerank-endpoint-description": "Cuando está habilitado, los puntos finales y las claves de API del modelo integrado serán reorganizados.", "use-embedding-endpoint": "Usa el punto final de incrustación", "rerank-score-description": "Establezca un umbral de puntuación para el modelo de reordenamiento, por debajo del cual se filtrarán los resultados.", "public-share": "Acción Pública", "internal-share": "Compartir Interno", "no-team-members-found": "No se encontraron miembros del equipo", "selected-users": "Usuarios seleccionados", "tags-prompt": "Etiquetas Rápida", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "Indicación utilizada para la generación automática de etiquetas. Si se establece en vacío, se utilizará la indicación predeterminada.", "generate-low-permission-token": "Generar token de permisos bajos", "low-permission-token-desc": "Los tokens de bajo permiso solo pueden acceder al endpoint de upsertNote y al endpoint de chat de IA. No pueden acceder a la información de tu cuenta u otras notas. Esto es ideal para casos de uso como bots de Telegram o bots de WeChat, donde quieres asegurarte de que no puedan acceder a ninguna otra nota.", "this-token-is-only-displayed-once-please-save-it-properly": "Este token solo se muestra una vez, por favor guárdalo correctamente", "refresh-model-list": "Obtener lista de modelos", "please-set-the-embedding-model": "Por favor, configure el modelo incrustado", "blinko-endpoint": "Punto final de Blinko", "enter-blinko-endpoint": "La URL de tu despliegue de Blinko", "login-failed": "Inicio de sesión fallido", "verification-failed": "Certificación fallida", "download-success": "<PERSON><PERSON><PERSON> exitosa", "download-failed": "<PERSON><PERSON><PERSON> fallida", "downloading": "Descargando", "hide-pc-editor": "Ocultar el editor de PC", "import-from-markdown": "Importar desde un archivo Markdown", "import-from-markdown-tip": "Importar desde un solo archivo .md o un archivo .zip que contiene archivos .md", "not-a-markdown-or-zip-file": "No es un archivo Markdown o zip. Por favor, elija un archivo .md o .zip.", "todo": "Gestión代理", "restore": "Recuperar", "complete": "Completado", "today": "Hoy", "yesterday": "Ayer", "common.refreshing": "Actualizando", "common.releaseToRefresh": "Soltar para actualizar", "common.pullToRefresh": "<PERSON><PERSON><PERSON> hacia abajo para actualizar", "edit-message-warning": "Editar este mensaje borrará todo el historial de conversación subsiguiente y generará nuevamente respuestas de IA.", "enter-your-message": "Introduce tu mensaje", "set-deadline": "<PERSON><PERSON><PERSON> fecha lí<PERSON>", "expired": "Caducado.", "expired-days": "Vencido por {{count}} días", "expired-hours": "<PERSON>pi<PERSON> {{count}} horas.", "expired-minutes": "<PERSON><PERSON><PERSON><PERSON> {{count}} minutos.", "days-left": "{{count}} d<PERSON> después", "hours-left": "{{count}} horas después", "minutes-left": "{{count}} minutos des<PERSON>", "about-to-expire": "<PERSON><PERSON><PERSON><PERSON><PERSON> a vencer.", "1-day": "1 día", "1-week": "una semana", "1-month": "un mes", "quick-select": "Selección rápida", "import-ai-configuration": "Importar configuración de IA", "would-you-like-to-import-this-configuration": "¿Quieres importar esta configuración de IA?", "detected-ai-configuration-to-import": "Detección de configuración de IA para importar", "importing": "Importando", "cache-cleared-successfully": "¡La memoria caché se ha borrado con éxito! La página se recargará automáticamente.", "failed-to-clear-cache": "No se pudo borrar la caché del navegador. Intenta actualizar manualmente (Ctrl+Shift+R).", "select-deployment": "Seleccionar implementación", "deployment-name": "Nombre de la implementación", "please-set-the-api-endpoint": "Por favor, configure el endpoint de la API", "please-set-the-api-key": "Por favor, configure la clave de la API"}