{"spotify-consumer-key-tip": "mp3音楽のカバーを取得するために使用されました", "spotify-consumer-key-tip-2": "https://developer.spotify.com/ から API キーを取得します。", "blinko": "ブリンコ", "notes": "備考", "resources": "リソース", "archived": "アーカイブ", "settings": "設定", "total-tags": "総合タグ", "search": "検索...", "i-have-a-new-idea": "新しいアイデアがあるんだ", "add-tag": "タグを追加", "ai-model": "AIモデル", "all-notes-have-been-loaded": "すべての{{items}}ノートがロードされました。", "already-have-an-account-direct-login": "アカウントをお持ちですか？直接ログイン", "api-endpoint": "APIエンドポイント", "archive": "アーカイブ", "ask-about-your-notes": "ノートについて尋ねる", "basic-information": "基本情報", "bulleted-list": "箇条書きリスト", "confirm": "確認", "confirm-password": "パスワードの確認", "confirm-to-delete": "削除を確認する！", "confrim": "確認", "convert-to": "に変換する。", "convert-to-blinko": "ブリンコに変換", "convert-to-note": "ノートに変換", "create-successfully": "作成に成功", "create-successfully-is-about-to-jump-to-the-login": "正常に作成され、ログインにジャンプしようとしている", "delete": "削除", "delete-confirm": "削除確認", "delete-success": "削除成功", "enter-send-shift-enter-for-new-line": "Enterで送信、Shift+Enterで改行", "enter-your-name": "名前を入力", "enter-your-username": "ユーザー名を入力してください", "every-day": "毎日", "every-half-year": "半年ごと", "every-month": "毎月", "every-three-month": "3ヶ月ごと", "every-week": "毎週", "hello": "こんにちわ", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "こんにちは{{名前}}！あなたのためにノートを検索することができます。", "import": "輸入", "insert-codeblock": "codeBlockを挿入する", "insert-table": "テーブルの挿入", "keep-sign-in": "サインインを維持する", "language": "言語", "last-run": "ラストラン", "logout": "ログアウト", "model-provider": "モデル・プロバイダー", "need-to-create-an-account": "アカウントの作成が必要ですか？", "new-version-detected-click-to-get-the-latest-version": "新しいバージョンが検出されました。", "nickname": "ニックネーム", "no-data-here-well-then-time-to-write-a-note": "データがない？それなら、ノートを書く時間だ！", "no-tag-found": "タグが見つかりません", "not-a-bko-file": "bkoファイルではない", "numbered-list": "番号付きリスト", "operation-failed": "操作に失敗した。", "password": "パスワード", "preference": "プリファレンス", "recovery": "リカバリー", "required-items-cannot-be-empty": "必須項目を空にすることはできない", "rest-user-password": "一休ユーザーのパスワード", "reviewed": "レビュー", "running": "ランニング", "save": "セーブ", "schedule-archive-blinko": "スケジュール アーカイブ ブリンコ", "schedule-back-up": "バックアップのスケジュール", "show-less": "ショー・レス", "show-more": "もっと見る", "show-navigation-bar-on-mobile": "モバイルでのナビゲーションバーの非表示", "sign-in": "ログイン", "status": "ステータス", "stopped": "停止", "theme": "テーマ", "there-are-no-resources-yet-go-upload-them-now": "リソースはまだありません。", "this-operation-will-be-delete-resource-are-you-sure": "この操作はリソースを削除することになりますが、よろしいですか？", "top": "トップ", "backup-file": "バックアップファイル", "cancel": "キャンセル", "cancel-top": "キャンセルトップ", "change-type": "タイプ変更", "change-user-info": "ユーザー情報の変更", "total": "合計", "upload-file": "ファイルのアップロード", "use-ai": "aiを使う", "user-custom-openai-api-key": "ユーザー独自のOpenAI APIキー", "user-custom-azureopenai-api-instance": "Azure OpenAI インスタンス名", "user-custom-azureopenai-api-deployment": "Azure OpenAI デプロイ名", "user-custom-azureopenai-api-version": "APIバージョン", "edit": "編集", "enter-your-password": "パスワードを入力してください", "import-from-bko": ".bkoからのインポート", "insert-hashtag": "ハッシュタグを入れる", "items": "項目", "name-db": "名前", "note": "注", "recording": "レコーディング", "schedule-task": "スケジュール・タスク", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "この操作によって関連ラベルが削除され、元に戻すことはできません。", "update-successfully": "更新成功", "user-or-password-error": "ユーザーまたはパスワードのエラー", "username": "ユーザー名", "your-changes-have-been-saved": "変更が保存されました！", "check-list": "チェックリスト", "confirm-your-password": "パスワードの確認", "congratulations-youve-reviewed-everything-today": "今日はすべてを見直したね。", "daily-review": "デイリーレビュー", "detail": "詳細", "in-progress": "進行中だ...", "insert-sandpack": "サンドパックを入れる", "multiple-select": "複数選択", "must-start-with-http-s-or-use-api-openai-as-default": "http(s)://で始まるか、デフォルトの/api/openaiを使用する必要があります。", "name": "名称", "schedule": "スケジュール", "sign-up": "会員登録", "the-two-passwords-are-inconsistent": "2つのパスワードは矛盾している", "created-in": "で作成された。", "set-as-public": "公開設定", "unset-as-public": "公開未設定", "no-tag": "タグなし", "with-link": "リンク付き", "has-file": "ファイル", "created-at": "で作成する", "role": "役割", "create-user": "ユーザー作成", "edit-user": "編集ユーザー", "import-from-memos-memos_prod-db": "メモからのインポート(memos_prod.db)", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "memos_prod.dbをエクスポートする際は、データの一部消失を避けるため、メモコンテナを閉じてください。", "go-to-share-page": "シェアページへ", "rebuilding-embedding-progress": "再構築 進歩の定着", "rebuild": "再構築", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "多くのノートをお持ちの場合、一定数のトークンを消費する可能性があります。", "order-by-create-time": "作成時間順", "time-format": "時間形式", "version": "バージョン", "new-version-available": "新バージョン", "storage": "ストレージ", "local-file-system": "ローカルファイルシステム", "object-storage": "オブジェクトストレージ", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "GPTモデルに加えて、次のようなことが可能であることを保証する必要がある。", "speech-recognition-requires-the-use-of": "音声認識には", "ai-expand": "AI拡大", "ai-polish": "AIポーランド語", "accept": "受け入れる", "reject": "却下", "stop": "ストップ", "card-columns": "カード・コラム", "select-a-columns": "列を選択する", "width-less-than-1024px": "横幅が1024px未満", "width-less-than": "幅", "small-device-card-columns": "小型デバイスカードのコラム", "medium-device-card-columns": "ミディアムデバイスカードのコラム", "large-device-card-columns": "大型デバイスカードのコラム", "device-card-columns": "デバイスカードのコラム", "columns-for-different-devices": "デバイス別コラム", "mobile": "モバイル", "tablet": "タブレット", "desktop": "デスクトップ", "chars": "文字", "text-fold-length": "テキストの折りの長さ", "title-first-line-of-the-text": "タイトル（本文の1行目）", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "コンテンツ(テキストが長さを超える場合、残りの部分)", "ai-tag": "AIタグ", "article": "記事", "embedding-model": "埋め込みモデル", "force-rebuild": "フォース・リビルド", "force-rebuild-embedding-index": "強制再構築は、インデックスが作成されたすべてのデータを完全に再構築します。", "embedding-model-description": "組み込みモデルを切り替えた後は、インデックスを再構築する必要があります。", "top-k-description": "最終的に返される文書の最大数", "embedding-score-description": "クエリの類似度閾値は一般的にユークリッド和距離である。", "embedding-lambda-description": "クエリ結果の多様性重み付けパラメータ", "update-tag-icon": "タグアイコンの更新", "delete-only-tag": "タグのみ削除", "delete-tag-with-note": "メモ付きタグの削除", "update-tag-name": "タグ名の更新", "thinking": "考える...", "select-all": "すべて選択", "deselect-all": "すべての選択を解除", "insert-before": "前に挿入", "insert-after": "の後に挿入する。", "update-name": "更新名", "ai-emoji": "絵文字", "custom-icon": "カスタムアイコン", "ai-enhanced-search": "AIによる検索強化", "preview-mode": "プレビュー・モード", "source-code": "ソースコード", "camera": "カメラ", "reference": "参考", "reference-note": "参考資料", "source-code-mode": "ソースコードモード", "heading": "見出し", "paragraph": "パラグラフ", "quote": "引用", "bold": "太字", "remove-italic": "イタリック体の削除", "underline": "アンダーライン", "italic": "イタリック", "remove-bold": "太字を削除", "remove-underline": "アンダーラインの削除", "select-block-type": "ブロックタイプを選択", "block-type-select-placeholder": "ブロックタイプ", "trash": "ゴミ", "custom-path": "カスタムパス", "page-size": "ページサイズ", "toolbar-visibility": "ツールバーの表示", "always-hide-toolbar": "常に隠れる", "always-show-toolbar": "常にショー", "hide-toolbar-on-mobile": "モバイルで隠す", "select-toolbar-visibility": "ツールバーの表示を選択", "select-a-time-format": "時間フォーマットを選択する", "enter-code-shown-on-authenticator-app": "認証アプリに表示されたコードを入力", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "サードパーティ認証アプリを開き、画面に表示されたコードを入力する。", "two-factor-authentication": "二要素認証", "scan-this-qr-code-with-your-authenticator-app": "認証アプリでこのQRコードをスキャンしてください。", "or-enter-this-code-manually": "または、このコードを手動で入力する：", "verify": "ベリファイ", "about": "について", "upload": "アップロード", "days": "日数", "select-model-provider": "モデルプロバイダーを選択", "allow-register": "登録許可", "access-token": "アクセス・トークン", "bucket": "バケット", "region": "地域", "access-key-secret": "アクセスキーの秘密", "access-key-id": "アクセスキーID", "share-and-copy-link": "リンクの共有とコピー", "copy-share-link": "共有リンクをコピーする", "endpoint": "エンドポイント", "export-format": "エクスポート形式", "export": "輸出", "time-range": "時間範囲", "all": "すべて", "exporting": "輸出...", "has-image": "イメージ", "has-link": "リンクあり", "filter-settings": "フィルター設定", "tag-status": "タグステータス", "all-notes": "すべての注意事項", "with-tags": "タグ付き", "without-tags": "タグなし", "select-tags": "タグを選択", "additional-conditions": "追加条件", "apply-filter": "フィルタを適用する", "to": "へ", "start-date": "開始日", "end-date": "終了日", "reset": "リセット", "no-condition": "コンディションなし", "public": "パブリック", "ai-model-tooltip": "gpt-3.5-turbo、gpt-4、gpt-4o、gpt-4o-miniなど、使用するモデル名を入力してください。", "user-custom-azureopenai-api-deployment-tooltip": "使用するデプロイメント名（gpt-4o など）を入力します。", "ollama-ai-model-tooltip": "llama3.2のように、使用するモデル名を入力してください。", "ollama-default-endpoint-is-http-localhost-11434": "Ollama デフォルトのエンドポイントは http://localhost:11434", "your-azure-openai-instance-name": "Azure OpenAIインスタンス名", "action": "行動", "ai-generate-emoji": "", "ai-generating-emoji": "", "align-center": "センター", "align-left": "左", "align-right": "正しい", "alternate-text": "代替テキスト", "api-key": "", "both": "両方", "check": "タスクリスト", "close": "閉める", "code": "コードブロック", "code-theme": "コードブロックテーマプレビュー", "column": "列", "comment": "コメント", "content-theme": "コンテンツテーマのプレビュー", "copied": "コピーされました", "copy": "コピー", "dark-mode": "ダークモード", "date-range": "", "days-ago": "", "delete-column": "行を削除する", "delete-row": "列を削除します。", "devtools": "DevTools\n\nデベロッパーツール", "down": "ダウン", "download-tip": "ブラウザはダウンロード機能をサポートしていません", "edit-mode": "編集モードの切り替え", "emoji": "絵文字", "enter-your-api-key": "", "exclude-tag-from-embedding": "タグ付けされたコンテンツを除外します。", "exclude-tag-from-embedding-desc": "AI埋め込みベクトル生成から関連するノートを排除するためのタグを選択します。", "exclude-tag-from-embedding-tip": "このタグが付けられたノートは、AIの埋め込み処理から除外されます。", "follow-system": "システムに従う", "footnote-ref": "脚注参照", "fullscreen": "フルスクリーン切り替え", "generate": "生成します", "heading1": "見出し1", "heading2": "見出し2", "headings": "見出し", "help": "助けてください", "hours-ago": "", "image-url": "画像のURL", "impoort-from-bko": "", "import-done": "インポートが完了しました。", "indent": "インデント", "info": "情報", "inline-code": "インラインコード", "insert-column-left": "1 を挿入左", "insert-column-right": "1を右に挿入", "search-tags": "検索タグ", "insert-attachment-or-note": "添付ファイルに挿入しますか、それともメモに書き込みますか？", "context": "コンテクスト", "paste-to-note-or-attachment": "コンテキストや添付ファイルに貼り付けることを確認しますか？", "attachment": "添付", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "削除後、すべてのユーザーデータは消去され、回復不能になります。", "upload-completed": "アップロードが完了しました", "upload-cancelled": "アップロードがキャンセルされました", "upload-failed": "アップロードに失敗しました", "import-from-bko-tip": "この時点では、S3 へのアップロードによるリカバリはサポートされていません。リカバリを行う際には一時的に S3 オプションを無効にしてください。", "edit-time": "編集時間", "ai-write": "AIライト", "download": "ダウンロードします。", "rename": "名前を変更", "move-up": "上に移動する", "cut": "切る", "paste": "ペースト", "confirm-delete": "削除を確認します。", "confirm-delete-content": "「{{name}}」を削除しますか？この操作は取り消すことができません。", "folder-name": "フォルダー名", "file-name": "ファイル名", "operation-success": "手術が成功しました", "cloud-file": "クラウドファイル", "move-to-parent": "親に移動", "no-resources-found": "リソースが見つかりません", "operation-in-progress": "進行中の操作", "new-folder": "新しいフォルダ", "folder-name-exists": "フォルダ名が既に存在します", "folder-name-required": "フォルダ名が必要です。", "collapse": "崩壊", "show-all": "すべて表示", "sun": "太陽", "mon": "月", "wed": "水曜日", "thu": "木曜日", "fri": "金曜日", "sat": "土曜日", "heatMapTitle": "過去1年間のノートのヒートマップ", "heatMapDescription": "1日あたりに作成されたメモの数を表示します", "select-month": "月を選択", "note-count": "ノートカウント", "max-daily-words": "1日の最大単語数", "active-days": "アクティブな日々", "total-words": "総単語数", "analytics": "アナリティクス", "tag-distribution": "タグ配布", "other-tags": "その他のタグ", "tue": "火曜日", "offline-status": "オフラインモード", "offline-title": "オフラインです", "offline-description": "インターネット接続をチェックし、もう一度試してください", "retry": "リトライ", "back-to-home": "ホームに戻る", "offline": "オフライン", "close-background-animation": "背景アニメーションを閉じる", "custom-bg-tip": "https://www.shadergradient.co/ にアクセスして、独自のグラデーション背景を作成してください。", "custom-background-url": "カスタム背景", "share": "共有", "need-password-to-access": "パスワードアクセスが必要です", "password-error": "パスワードエラー", "cancel-share": "共有をキャンセル", "create-share": "共有を作成する", "share-link": "共有リンク", "set-access-password": "アクセスパスワードを設定します", "protect-your-shared-content": "共有コンテンツを保護します。", "access-password": "アクセスパスワード", "select-date": "日付を選択してください。", "expiry-time": "有効期限", "select-expiry-time": "有効期限を選択します", "permanent-valid": "永久有効", "7days-expiry": "有効期限7日間", "custom-expiry": "カスタム有効期限", "30days-expiry": "30日有効期限", "share-link-expired": "共有リンクの期限切れ", "share-link-expired-desc": "この共有は期限切れです。再共有するには管理者に連絡してください！", "shared": "共有", "internal-shared": "内部共有", "edited": "編集されました", "move-down": "下に移動", "provider-id": "プロバイダーID", "provider-name": "プロバイダー名", "well-known-url": "WellKnown URL\n\n認識されているURL", "authorization-url": "認可URL", "token-url": "トークンURL", "userinfo-url": "ユーザー情報URL", "scope": "範囲", "client-id": "クライアントID", "client-secret": "クライアントシークレット", "sso-settings": "単一サインオン設定", "oauth2-providers": "Oauth2 プロバイダー", "add-oauth2-provider": "Oauth2 プロバイダを追加します", "add-provider": "プロバイダーを追加する", "edit-oauth2-provider": "Oauth2 プロバイダーの編集", "confirm-delete-provider": "削除プロバイダを確認しますか？", "please-select-icon-from-iconify": "アイコニファイからアイコンを選択してください", "provider-icon": "提供元アイコン", "select-provider-template": "プロバイダーテンプレートを選択します", "provider-template": "プロバイダーテンプレート", "please-add-this-url-to-your-oauth-provider-settings": "このURLをOAuthプロバイダー設定に追加してください", "redirect-url": "リダイレクトURL", "sign-in-with-provider": "{{ プロバイダ }} でサインイン", "community": "コミュニティ", "theme-color": "テーマカラー", "link-account": "アカウントをリンクする", "select-account": "アカウントを選択", "link-account-warning": "アカウントをリンクすると、現在のアカウントからのデータはリンクされたアカウントに同期されませんのでご注意ください。", "unlink-account": "アカウントのリンク解除", "unlink-account-tips": "このアカウントで全ての関連付けにアクセスできることを確認しますか？", "login-type": "ログインタイプ", "close-daily-review": "デイリーレビューを閉じる", "max-home-page-width": "ホームページの最大幅", "max-home-page-width-tip": "0に設定すると、最大の幅になります。", "no-comments-yet": "まだコメントはありません", "author": "著者", "from": "から", "reply-to": "返信する", "hub": "ハブ", "home-site": "ホームサイト", "use-blinko-hub": "Blinko Hub を使用してください。", "full-screen": "フルスクリーン", "exit-fullscreen": "フルスクリーンを終了", "no-note-associated": "関連するノートはありません", "insert-context": "コンテキストに挿入", "follow": "フォロー", "follower": "フォロワー", "following": "以下", "admin": "ウェブマスター", "site-url": "BlinkoサイトのURL", "unfollow": "フォローを外す", "join-hub": "ハブに参加", "refresh": "リフレッシュ", "comment-notification": "コメント通知", "follow-notification": "通知をフォロー", "followed-you": "あなたに従いました", "mark-all-as-read": "すべてを読み終わったとマークします", "no-notification": "通知なし", "new-notification": "新しい通知", "notification": "通知", "backup-success": "バックアップ成功🎉", "system-notification": "システム通知", "embedding-api-endpoint": "APIエンドポイントの埋め込み", "embedding-api-key": "APIキーの埋め込み", "recommand": "お勧め", "has-todo": "「やるべきこと」", "reference-by": "言及者", "hide-notification": "通知を非表示", "search-settings": "検索設定...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "この操作により選択したファイルが削除され、元に戻すことはできません。ご確認ください。", "plugin-settings": "プラグインの設定", "installed-plugins": "インストール済み", "marketplace": "マーケットプレイス", "local-development": "地域開発", "add-local-plugin": "ローカルプラグインを追加します。", "local-plugin": "ローカル プラグイン", "uninstall": "アンインストール", "install": "インストールします。", "downloads": "ダウンロード", "plugin-updated": "プラグインが更新されました", "plugin-update-failed": "プラグインの更新に失敗しました", "plugin-connection-failed": "プラグインの接続に失敗しました", "disconnect": "切断", "local-development-description": "ローカル開発プラグインを追加してデバッグします。", "setting": "", "ai": "人工知能", "ai-chat-box-notes": "以下はあなたのために取得された関連ノートです。", "add-to-note": "ノートに追加", "add-to-blinko": "ブリンコに追加", "no-title": "タイトルなし", "search-blinko-content-or-help-create": "コンテンツを検索するか、blinkoの作成を手助けしてください...", "conversation-history": "会話履歴", "new-conversation": "ニューチャット", "knowledge-base-search": "ナレッジベース検索", "add-tools-to-model": "オンラインで検索するか、AIにblinko apiを呼び出させる", "clear-current-content": "現在のコンテンツをクリア", "welcome-to-blinko": "ようこそ、{{name}}さん", "ai-prompt-writing": "あなたはプロの作家です。ユーザーが提供したトピックに関して、プロフェッショナルな記事を書いてください。", "coding": "コーディング", "writing": "ライティング", "ai-prompt-translation": "あなたはプロの翻訳者です。ユーザーが提供したテキストを{{lang}}に翻訳してください。", "ai-prompt-coding": "あなたはプロのコーダーです。ユーザーが提供したトピックに基づいた簡単なPythonプログラムを書いてください。", "translation": "翻訳", "first-char-delay": "最初の文字遅延", "total-tokens": "トータルトークン", "check-connect": "確認", "check-connect-error": "接続障害は/v1の末尾に追加されることがあります。", "check-connect-success": "接続成功を確認してください。", "loading": "読み込み中", "embedding-dimensions": "埋め込み次元", "embedding-dimensions-description": "モデルの寸法が正しいことを確認する必要があり、変更後にインデックスレコードを再構築するよう強制する必要があります。", "model": "モデル", "ai-tools": "AIツール", "tavily-api-key": "Tavily Search API キー", "tavily-max-results": "タヴィリーマックスリザルト", "ai-prompt-writing-content": "200語の記事を書いて、メモに保存してください", "ai-prompt-coding-content": "https://github.com/blinko-space/blinko のWebコンテンツを抽出", "stop-task": "タスクを停止", "processing": "処理中", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "再構築作業が進行中です、再起動しますか？", "hide-blog-images": "ブログ画像を非表示にします", "ai-prompt-translation-content": "過去2日間でNOタグノートを確認し、タグを付けます。", "ai-prompt-delete-content": "アーカイブされた2つのメモを見つけ、それらを要約して新しいメモとして保存し、これら2つのアーカイブメモを削除します", "older": "年上", "newer": "新しい", "restore-this-version": "このバージョンを復元します", "Note History": "履歴に注意してください", "View History Versions": "履歴バージョンを表示します", "history-note-only": "注意：この履歴には、ファイル履歴ではなく、テキストコンテンツのみが含まれています", "referenceResource": "参照リソース", "to-ask-ai": "AIに尋ねる", "press-enter-to-select-first-result": "Enterを押して、最初の結果を選択します", "ask-ai": "aiに尋ねてください", "ask-blinko-ai-about-this-query": "Blinko AIにこのクエリについて尋ねてください", "search-or-ask-ai": "ノート、設定、またはAIに尋ねる...", "plugin": "プラグイン", "editor-preview": "エディタ", "auto-add-tags": "タグを自動追加します", "add-as-comment": "コメントとして追加します", "choose-what-to-do-with-ai-results": "AIの結果をどうするかを選択します", "ai-post-processing-mode": "AI後処理モード", "ai-post-processing-prompt": "AIポスト処理コメントプロンプト", "2fa-setup-successful": "2FAセットアップが成功しました", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "次のメモコンテンツを分析し、適切なタグを提案し、簡単な要約を提供します", "content-generated-by-ai": "AIによって生成されたコンテンツ", "define-custom-prompt-for-ai-to-process-notes": "AIを操作して、現在のノートへのコメントを行ってください。例えば：ノートの内容を要約してください。もしノートの内容が10語未満であれば、私に対してそれを磨いてください。", "enter-custom-prompt-for-post-processing": "ポストプロセッシングのカスタムプロンプトを入力します", "enter-spotify-consumer-key": "Spotify APIキーを入力します", "enter-spotify-consumer-secret": "Spotify Consumer Secretを入力してください", "file-type-error": "ファイルタイプはエラーです", "heading3": "見出し3", "heading4": "見出し4", "heading5": "見出し5", "heading6": "見出し6", "insert-row-above": "上に1を挿入します", "insert-row-below": "以下に1を挿入します", "instant-rendering": "インスタントレンダリング", "light-mode": "ライトモード", "line": "ライン", "link": "リンク", "link-ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list": "リスト", "minutes-ago": "", "months-ago": "", "more": "もっと", "music-settings": "音楽設定", "name-empty": "名前は空です", "notes-imported-by-other-means-may-not-have-embedded-vectors": "他の手段によってインポートされたメモには、ベクトルが埋め込まれていない場合があります", "ordered-list": "注文リスト", "original-password": "元のパスワード", "outdent": "外に出る", "outline": "概要", "over": "以上", "performance-tip": "リアルタイムプレビューには$ {x} msが必要です。閉じることができます", "preview": "プレビュー", "prompt-used-for-post-processing-notes": "ポストプロセッシングノートに使用されるプロンプト", "rebuild-embedding-index": "埋め込みインデックスの再構築", "rebuild-in-progress": "進行中の再構築", "record": "レコード/終了レコードを開始します", "record-tip": "デバイスは録音をサポートしていません", "redo": "やり直します", "remove": "取り除く", "rest-user-info": "休憩ユーザー情報", "row": "行", "select-model": "モデルを選択します", "spin": "スピン", "split-view": "分割ビュー", "spotify-consumer-key": "Spotify APIキー", "spotify-consumer-secret": "Spotify API Secret", "strike": "ストライク", "superadmin": "", "table": "テーブル", "text-is-not-empty": "テキスト（空はありません）", "title": "タイトル", "tooltip-text": "ツールチップテキスト", "undo": "元に戻します", "up": "上", "update": "アップデート", "updated-at": "で更新します", "upload-error": "エラーをアップロードします", "uploading": "アップロード...", "user": "", "user-list": "ユーザーリスト", "weeks-ago": "", "wysiwyg": "wysiwyg", "years-ago": "", "enable-ai-post-processing": "AI後処理を有効にします", "automatically-process-notes-after-creation-or-update": "作成後にメモを自動的に処理します", "can-generate-summaries-tags-or-perform-analysis": "パフォーマンス分析で概要タグを生成できます", "ai-post-processing": "AI後処理", "model-list-updated": "モデルリストが更新されました", "to-search-tags": "タグを検索する", "app-upgrade-required": "アプリのアップグレードが必要です", "current-app-version": "現在のAPPバージョン", "required-app-version": "要求されるアプリのバージョン", "upgrade": "アップグレード", "online-search": "オンライン検索", "smart-edit": "スマートエディット", "function-call-required": "関数呼び出しが必要です", "smart-edit-prompt": "スマート編集プロンプト", "define-instructions-for-ai-to-edit-your-notes": "メモを操作するためにプロンプトを使用できます。たとえば、メモにリンクが含まれている場合は、元のメモの下にリンク内容を要約してラベルを生成します。", "rebuild-started": "再構築が開始されました", "rebuild-stopped-by-user": "ユーザーによる再構築停止", "random-mode": "ランダム・ウォーク", "related-notes": "関連ノート", "no-related-notes-found": "関連するノートが見つかりませんでした", "advanced": "高度な", "rerank-model-description": "検索精度を向上させるためのベクトル結果の並べ替えモデルを指定する", "rerank-model": "モデルの再ランク付け", "rerank": "再ランク付け", "use-custom-rerank-endpoint-description": "有効にすると、組み込まれたモデルのエンドポイントとAPIキーが再配置されます。", "use-embedding-endpoint": "エンベディングエンドポイントを使用する", "rerank-score-description": "並べ替えモデルのためのスコアしきい値を設定し、それ以下の結果はフィルタリングされます。", "public-share": "公開シェア", "internal-share": "内部共有", "no-team-members-found": "チームメンバーが見つかりませんでした", "selected-users": "選ばれたユーザー", "tags-prompt": "タグプロンプト", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "自動生成タグ用のプロンプト。空に設定すると、デフォルトのプロンプトが使用されます。", "generate-low-permission-token": "低許可トークンを生成する", "low-permission-token-desc": "低権限トークンは、upsertNoteエンドポイントとAIチャットエンドポイントのみにアクセスできます。あなたのアカウント情報や他のノートへのアクセスはできません。これは、TelegramボットやWeChatボットなど、他のノートにアクセスできないようにしたい場合に理想的です。", "this-token-is-only-displayed-once-please-save-it-properly": "このトークンは一度しか表示されませんので、適切に保存してください。", "refresh-model-list": "モデルリストを取得する", "please-set-the-embedding-model": "埋め込みモデルを設定してください", "blinko-endpoint": "Blinkoエンドポイント", "enter-blinko-endpoint": "あなたのBlinkoデプロイメントのURL", "login-failed": "ログインに失敗しました", "verification-failed": "認証失敗", "download-success": "ダウンロード成功", "download-failed": "ダウンロード失敗", "downloading": "ダウンロード中", "hide-pc-editor": "PCエディターを非表示にする", "import-from-markdown": "Markdownファイルからインポート", "import-from-markdown-tip": "単一の .md ファイルまたは .md ファイルを含む .zip アーカイブからインポート", "not-a-markdown-or-zip-file": "Markdownまたはzipファイルではありません。.mdまたは.zipファイルを選択してください。", "todo": "代行", "restore": "回復", "complete": "完了", "today": "今日", "yesterday": "昨日", "common.refreshing": "更新中", "common.releaseToRefresh": "リフレッシュのために開放します", "common.pullToRefresh": "下に引っ張って更新", "edit-message-warning": "このメッセージを編集すると、以降のすべての会話記録が消去され、AIの返信が再生成されます。", "enter-your-message": "あなたのメッセージを入力してください", "set-deadline": "締切日を設定する", "expired": "期限切れ", "expired-days": "期限切れ{{count}}日", "expired-hours": "期限切れ{{count}}時間", "expired-minutes": "有効期限切れ{{count}}分", "days-left": "{{count}}日後", "hours-left": "{{count}}時間後", "minutes-left": "{{count}}分後", "about-to-expire": "まもなく期限切れ", "1-day": "1日", "1-week": "一週間", "1-month": "1か月", "quick-select": "クイック選択", "import-ai-configuration": "AI設定をインポートする", "would-you-like-to-import-this-configuration": "そのAI設定をインポートしたいですか？", "detected-ai-configuration-to-import": "AI設定のインポート待ちが検出されました", "importing": "インポート中", "cache-cleared-successfully": "キャッシュが正常にクリアされました！ページは自動的に再読み込みされます。", "failed-to-clear-cache": "ブラウザのキャッシュを削除できませんでした。手動で更新してください (Ctrl+Shift+R)。", "select-deployment": "デプロイを選択", "deployment-name": "デプロイ名", "please-set-the-api-endpoint": "APIエンドポイントを設定してください", "please-set-the-api-key": "APIキーを設定してください"}