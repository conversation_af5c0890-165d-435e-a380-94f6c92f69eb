{"spotify-consumer-key-tip": "كان يُستخدم للحصول على غلاف موسيقى mp3", "spotify-consumer-key-tip-2": "احصل على مفتاح API من https://developer.spotify.com/", "hello": "مرحبًا", "blinko": "بلينكو", "notes": "ملاحظات", "resources": "الموارد", "archived": "المؤرشفة", "settings": "الإعدادات", "total-tags": "إجمالي العلامات", "search": "بحث...", "i-have-a-new-idea": "لدي فكرة جديدة...", "note": "ملاحظة", "edit": "تعديل", "multiple-select": "اختيار متعدد", "convert-to": "تحويل إلى", "delete": "<PERSON><PERSON><PERSON>", "recovery": "استعادة", "archive": "أرشفة", "items": "عناصر", "your-changes-have-been-saved": "تم حفظ التغييرات!", "operation-failed": "فشلت العملية.", "in-progress": "قيد التنفيذ...", "confirm-to-delete": "تأكيد الحذف!", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "هذه العملية ستزيل التصنيف المرتبط ولن يمكن استعادته. يرجى التأكيد", "add-tag": "إضافة علامة", "cancel": "إلغاء", "confirm": "تأكيد", "no-data-here-well-then-time-to-write-a-note": "لا توجد بيانات هنا؟ إذن حان الوقت لكتابة ملاحظة!", "basic-information": "معلومات أساسية", "name": "الاسم", "preference": "التفضيل", "theme": "السمة", "language": "اللغة", "change-type": "تغيير النوع", "insert-hashtag": "إدراج وسم", "bulleted-list": "قائمة نقطية", "numbered-list": "قائمة مرقمة", "check-list": "قائمة التحقق", "insert-table": "إدراج جدول", "insert-codeblock": "إدراج كتلة كود", "insert-sandpack": "إدراج حزمة الرمل", "upload-file": "رفع ملف", "delete-confirm": "تأكيد الحذف", "this-operation-will-be-delete-resource-are-you-sure": "سيتم حذف المورد. هل أنت متأكد؟", "delete-success": "تم الحذف بنجاح", "update-successfully": "تم التحديث بنجاح", "create-successfully": "تم الإنشاء بنجاح", "total": "الإجمالي", "all-notes-have-been-loaded": "تم تحميل جميع {{items}} الملاحظات", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "مرحبًا {{name}}! يمكنني البحث عن الملاحظات لك، كيف يمكنني مساعدتك اليوم؟", "ask-about-your-notes": "اسأل عن ملاحظاتك", "use-ai": "استخدام الذكاء الاصطناعي", "model-provider": "مزو<PERSON> الخدمة", "api-endpoint": "نقطة نهاية API", "must-start-with-http-s-or-use-api-openai-as-default": "يجب أن يبدأ بـ ://http(s) أو استخدام /api/openai كإعداد افتراضي", "user-custom-openai-api-key": "مفتاح واجهة برمجة التطبيقات المخصص للمستخدم  من OpenAI", "user-custom-azureopenai-api-instance": "اسم مثيل Azure OpenAI", "user-custom-azureopenai-api-deployment": "اسم نشر Azure OpenAI", "user-custom-azureopenai-api-version": "نسخة API", "ai-model": "نموذج الذكاء الاصطناعي", "logout": "تسجيل الخروج", "user-or-password-error": "خطأ في اسم المستخدم أو كلمة المرور", "username": "اسم المستخدم", "enter-your-name": "أ<PERSON><PERSON>ل اسمك", "password": "كلمة المرور", "enter-your-password": "أد<PERSON>ل كلمة المرور", "need-to-create-an-account": "هل تحتاج إلى إنشاء حساب؟", "sign-up": "التسجيل", "sign-in": "تسجيل الدخول", "nickname": "اللقب", "change-user-info": "تغيير معلومات المستخدم", "rest-user-password": "إعادة تعيين كلمة مرور المستخدم", "confirm-password": "تأكيد كلمة المرور", "confirm-your-password": "أ<PERSON><PERSON> كلمة المرور", "enter-your-username": "أدخل اسم المستخدم", "save": "<PERSON><PERSON><PERSON>", "keep-sign-in": "البقاء مسجلًا", "recording": "تسجيل", "required-items-cannot-be-empty": "العناصر المطلوبة لا يمكن أن تكون فارغة", "the-two-passwords-are-inconsistent": "كلمتا المرور غير متطابقتين", "create-successfully-is-about-to-jump-to-the-login": "تم الإنشاء بنجاح، سيتم التحويل إلى صفحة تسجيل الدخول", "already-have-an-account-direct-login": "هل لديك حساب بالفعل؟ تسجيل الدخول مباشرة", "no-tag-found": "لم يتم العثور على علامة", "new-version-detected-click-to-get-the-latest-version": "🎉 تم اكتشاف إصدار جديد، اضغط للحصول على أحدث إصدار", "schedule-task": "جدولة المهام", "schedule-back-up": "جدولة النسخ الاحتياطي", "every-day": "كل يوم", "every-week": "كل أسبوع", "every-month": "كل شهر", "every-three-month": "كل ثلاثة أشهر", "every-half-year": "كل نصف عام", "import": "استيراد", "impoort-from-bko": "استيراد من ملف.bko", "not-a-bko-file": "ليس ملف .bko", "convert-to-note": "تحويل إلى ملاحظة", "convert-to-blinko": "تحويل إلى بلينكو", "reviewed": "تمت المراجعة", "congratulations-youve-reviewed-everything-today": "تهانينا، لقد راجعت كل شيء اليوم.", "name-db": "الاسم", "schedule": "الجدولة", "last-run": "آخر تشغيل", "backup-file": "ملف الن<PERSON><PERSON> الاحتياطي", "status": "الحالة", "running": "قيد التشغيل", "stopped": "متوقف", "show-navigation-bar-on-mobile": "إخفاء شريط التنقل على الجوال", "schedule-archive-blinko": "جدولة أرشفة بلينكو", "there-are-no-resources-yet-go-upload-them-now": "لا توجد موارد حتى الآن، قم برفعها الآن", "confrim": "تأكيد", "daily-review": "مراجعة يومية", "detail": "التفاصيل", "enter-send-shift-enter-for-new-line": "إدخال للإرسال، Shift+Enter لسطر جديد", "show-less": "<PERSON>ر<PERSON> أقل", "show-more": "عر<PERSON> المزيد", "top": "أعلى", "cancel-top": "إلغاء التثبيت", "created-in": "تم الإنشاء في", "set-as-public": "تعيين كعام", "unset-as-public": "إلغاء التعيين كعام", "with-link": "برابط", "no-tag": "بدون علامة", "has-file": "يتضمن ملفًا", "created-at": "تم الإنشاء في", "updated-at": "تم التحديث في", "role": "الدور", "user-list": "قائمة المستخدمين", "create-user": "إنشاء مستخدم", "action": "الإجراء", "original-password": "كلمة المرور الأصلية", "edit-user": "تعديل المستخدم", "import-from-memos-memos_prod-db": "استيراد من قاعدة البيانات(memos_prod.db)", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "عند تصدير memos_prod.db، يرجى إغلاق الحاوية لتجنب فقدان البيانات جزئيًا.", "allow-register": "السماح بالتسجيل", "access-token": "ر<PERSON>ز الوصول", "superadmin": "مدير النظام", "user": "مستخدم", "select-model-provider": "اختيار مزود النموذج", "select-model": "اختيار النموذج", "api-key": "مفتاح API", "enter-your-api-key": "أدخل مفتاح API الخاص بك", "delete-only-tag": "حذ<PERSON> العلامة فقط", "update-name": "تحديث الاسم", "delete-tag-with-note": "حذ<PERSON> العلامة مع الملاحظة", "minutes-ago": "منذ {{count}} دقيقة", "hours-ago": "منذ {{count}} ساعة", "days-ago": "منذ {{count}} يوم", "weeks-ago": "منذ {{count}} أسبوع", "months-ago": "منذ {{count}} شهر", "years-ago": "منذ {{count}} سنة", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "بالإضافة إلى نموذج GPT، هناك حاجة إلى التأكد من إمكانية استدعاء", "speech-recognition-requires-the-use-of": "يتطلب التعرف على الكلام استخدام", "ai-expand": "توسيع نطاق الذكاء الاصطناعي", "ai-polish": "الذكاء الاصطناعي البولندي", "accept": "قبول", "reject": "<PERSON><PERSON><PERSON>", "stop": "توقف", "card-columns": "أعمدة البطاقة", "select-a-columns": "<PERSON><PERSON><PERSON>عمدة", "width-less-than-1024px": "العرض أقل من 1024 بكسل", "width-less-than": "عرض أقل من", "small-device-card-columns": "أعمدة بطاقة الجهاز الصغيرة", "medium-device-card-columns": "أعمدة بطاقة الجهاز المتوسطة", "large-device-card-columns": "أعمدة بطاقة الجهاز الكبيرة", "device-card-columns": "أعمدة بطاقة الجهاز", "columns-for-different-devices": "أعمدة للأجهزة المختلفة", "mobile": "الهات<PERSON> المحمول", "tablet": "جهاز لوحي", "desktop": "سط<PERSON> المكتب", "chars": "الأحرف", "text-fold-length": "طول طية النص", "title-first-line-of-the-text": "العنوان (السطر الأول من النص)", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "المحتوى(بقية النص، إذا كان النص أطول من الطول)", "ai-tag": "علامة الذكاء الاصطناعي", "article": "المادة", "embedding-model": "نموذج التضمين", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "إذا كان لديك الكثير من الملاحظات فقد تستهلك عددًا معينًا من الرموز المميزة", "force-rebuild": "إعادة بناء القوة", "force-rebuild-embedding-index": "ستؤدي إعادة البناء القسري إلى إعادة بناء جميع البيانات التي تمت فهرستها بالكامل", "embedding-model-description": "يجب إعادة بناء الفهرس بعد تبديل النماذج المدمجة", "top-k-description": "الح<PERSON> الأق<PERSON>ى لعدد المستندات التي تم إرجاعها في النهاية", "embedding-score-description": "عادة ما تكون عتبة التشابه للاستعلامات هي مجموع المسافة الإقليدية", "embedding-lambda-description": "معلمة ترجيح تنوع نتائج الاستعلامات", "update-tag-icon": "أيقونة تحديث العلامة", "update-tag-name": "تحديث اسم العلامة", "thinking": "التفكير...", "select-all": "اختر الكل", "insert-before": "إدرا<PERSON> قبل", "insert-after": "إدراج بعد", "ai-emoji": "الرموز التعبيرية Ai", "custom-icon": "أيقونة مخصصة", "ai-enhanced-search": "البحث المحسّن بالذكاء الاصطناعي", "preview-mode": "وضع المعاينة", "source-code": "<PERSON><PERSON><PERSON> المصدر", "camera": "كاميرا", "reference": "المرجع", "reference-note": "مذكرة مرجعية", "source-code-mode": "وضع شفرة المصدر", "heading": "العنوان", "paragraph": "الفقرة", "quote": "اقتباس", "bold": "جريئة", "remove-italic": "إزالة الخط المائل", "underline": "تسطير", "italic": "مائل", "remove-bold": "إزالة الخط العريض", "remove-underline": "إزالة التسطير", "select-block-type": "حدد نوع الكتلة", "block-type-select-placeholder": "نوع المربع", "trash": "قمامة", "custom-path": "المسار المخصص", "page-size": "حجم الصفحة", "toolbar-visibility": "رؤية شريط الأدوات", "always-hide-toolbar": "اختبئ دائماً", "always-show-toolbar": "اعرض دائماً", "hide-toolbar-on-mobile": "إخ<PERSON>اء على الجوال", "select-toolbar-visibility": "تحديد رؤية شريط الأدوات", "select-a-time-format": "حد<PERSON> تنسيق الوقت", "enter-code-shown-on-authenticator-app": "أد<PERSON>ل الرمز الظاهر في تطبيق المصادقة", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "افت<PERSON> تطبيق المصادقة التابع لجهة خارجية، وأدخل الرموزالتي تظهر على الشاشة", "two-factor-authentication": "المصادقة الثنائية", "scan-this-qr-code-with-your-authenticator-app": "امسح رمز الاستجابة السريعة هذا باستخدام تطبيق المصادقة الخاص بك", "or-enter-this-code-manually": "أو أدخل هذا الرمز يدوياً:", "verify": "تحقق", "about": "نبذة عن", "upload": "التحميل", "days": "الأيام", "bucket": "دلو", "region": "المنطقة", "access-key-secret": "م<PERSON><PERSON><PERSON><PERSON> الوصول السري", "access-key-id": "معرّف مفتاح الوصول", "share-and-copy-link": "مشاركة ونسخ الرابط", "copy-share-link": "نسخ رابط المشاركة", "endpoint": "نقطة النهاية", "export-format": "تنسيق التصدير", "export": "التصدير", "time-range": "النطاق الزمني", "all": "الكل", "exporting": "التصدير...", "has-image": "لديه صورة", "has-link": "لديه رابط", "filter-settings": "إعدادات التصفية", "tag-status": "حالة العلامة", "all-notes": "جميع الملاحظات", "with-tags": "مع العلامات", "without-tags": "بدون علامات", "select-tags": "<PERSON><PERSON><PERSON> العلامات", "additional-conditions": "الشروط الإضافية", "apply-filter": "تطبيق الفلتر", "to": "إ<PERSON><PERSON>", "start-date": "تاريخ البدء", "end-date": "تاريخ الانتهاء", "reset": "إعادة تعيين", "no-condition": "لا توجد حالة", "public": "عام", "ai-model-tooltip": "أدخل اسم الطراز المراد استخدامه، مثل gpt-3.5-turbo، gpt-4، gpt-4o، gpt-4o، gpt-4o-mini", "user-custom-azureopenai-api-deployment-tooltip": " أدخل اسم النشر المراد استخدامه، مثل gpt-4o", "ollama-ai-model-tooltip": "أدخل اسم الطراز المراد استخدامه، مثل llama3.2", "ollama-default-endpoint-is-http-localhost-11434": "نقطة نهاية أولاما الافتراضية هي http://localhost:11434", "your-azure-openai-instance-name": "اسم مثيل Azure OpenAI الخاص بك", "ai-generate-emoji": "", "ai-generating-emoji": "", "date-range": "", "2fa-setup-successful": "تم إعداد المصادقة الثنائية بنجاح", "both": "كلاهما", "code": "<PERSON><PERSON><PERSON> بلوك", "copied": "تم النسخ", "dark-mode": "الوضع الداكن", "delete-column": "<PERSON>ذ<PERSON> الصف", "delete-row": "<PERSON><PERSON><PERSON> العمود", "devtools": "أدوات المطورين", "down": "أسفل", "download-tip": "لا يدعم المتصفح وظيفة التنزيل", "edit-mode": "تبديل وضع التحرير", "emoji": "إيموجي", "exclude-tag-from-embedding-desc": "حدد علامة لاستبعاد ملاحظاتها المرتبطة من إنشاء النواة التضمينية باستخدام الذكاء الصناعي", "exclude-tag-from-embedding-tip": "سيتم استبعاد الملاحظات التي تحتوي على هذه العلامة من معالجة تضمين الذكاء الاصطناعي", "file-type-error": "نوع الملف خاطىء", "follow-system": "متابعة النظام", "footnote-ref": "المرجع السفلي", "fullscreen": "تبديل وضع ملء الشاشة", "generate": "توليد", "go-to-share-page": "انتقل إلى صفحة المشاركة", "heading1": "العنوان ١", "heading2": "العنوان ٢", "heading3": "العنوان 3", "heading4": "العنوان 4", "heading5": "العنوان 5", "heading6": "العنوان 6", "headings": "العناوين", "help": "مساعدة", "image-url": "عنوان URL", "import-done": "تم الاستيراد", "import-from-bko": "استيراد من .bko", "indent": "المسافة البادئة", "info": "معلومات", "inline-code": "<PERSON><PERSON><PERSON> مضمن", "insert-column-left": "ادخل 1 يسار", "insert-column-right": "أدخل 1 يمين", "insert-row-above": "أدخل 1 أعلى", "insert-row-below": "أدخل 1 أسفل", "instant-rendering": "التقديم الفوري", "light-mode": "وضع النور", "line": "خط", "link": "رابط", "link-ref": "رابط المرجع", "list": "قائمة", "local-file-system": "نظام الملفات المحلي", "more": "أكثر", "name-empty": "الاسم فارغ", "new-version-available": "الإصدار الجديد متاح", "notes-imported-by-other-means-may-not-have-embedded-vectors": "قد لا تحتوي الملاحظات المستوردة بطرق أخرى على متجهات مضمنة", "object-storage": "تخزين الكائنات", "order-by-create-time": "ترتيب حسب وقت الإنشاء", "ordered-list": "قائمة الطلبات", "outdent": "نسبة العجز", "outline": "مخطط/ تفصيلات", "over": "فوق", "performance-tip": "المعاينة في الوقت الحقيقي تتطلب ${x} ميلِّ ثانية، يمكنك إغلاقها", "preview": "معاينة", "rebuild": "إعادة البناء", "rebuild-embedding-index": "إعادة بناء فهرس التضمين", "rebuilding-embedding-progress": "إعادة بناء تقدم التضمين", "record": "بدء التسجيل / إنهاء التسجيل", "record-tip": "الجهاز لا يدعم التسجيل", "redo": "إعادة", "remove": "إزالة", "row": "صف", "spin": "الدوران", "split-view": "تقسيم العرض", "storage": "تخزين", "strike": "ضربة", "table": "الطاولة", "text-is-not-empty": "النص (غير فارغ)", "time-format": "تنسيق الوقت", "title": "العنوان", "tooltip-text": "نص التلميح", "undo": "تراجع", "up": "فوق", "update": "تحديث", "upload-error": "خطأ في التحميل", "uploading": "جاري التحميل...", "version": "الإصدار", "wysiwyg": "ما تراه هو ما تحصل عليه", "search-tags": "البحث عن العلامات", "insert-attachment-or-note": "الإرفاق بملف أو المذكرة؟", "context": "السياق", "paste-to-note-or-attachment": "هل أنت متأكد من لصق النص في السياق أو المرفق؟", "attachment": "المرفق", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "بعد الحذف، سيتم مسح جميع بيانات المستخدم ولن يمكن استرجاعها.", "upload-completed": "تم الرفع بنجاح", "upload-cancelled": "تم إلغاء التحميل", "upload-failed": "فشل التحميل", "import-from-bko-tip": "تحميل إلى s3 للإسترداد غير مدعوم في الوقت الحالي. يُرجى تعطيل الخيار s3 مؤقتًا عند رغبتك في الاسترداد.", "edit-time": "وقت التحرير", "ai-write": "الذكاء الاصطناعي", "download": "تحميل", "rename": "إعادة تسمية", "move-up": "تحرك لأعلى", "cut": "قطع", "paste": "لصق", "confirm-delete": "تأكيد الحذف", "confirm-delete-content": "هل أنت متأكد أنك تريد حذف {{name}}؟ لا يمكن التراجع عن هذا الإجراء.", "folder-name": "اسم المجلد", "file-name": "اسم الملف", "operation-success": "نجاح العملية", "cloud-file": "مل<PERSON> سحابي", "move-to-parent": "الانتقال إلى الوالد", "no-resources-found": "لم يتم العثور على موارد", "operation-in-progress": "العملية قيد التقدم", "new-folder": "م<PERSON><PERSON><PERSON> جديد", "folder-name-exists": "الاسم المجلد موجود", "folder-name-required": "اسم المجلد مطلوب", "collapse": "انهيار", "show-all": "اظهر الكل", "sun": "الشمس", "mon": "الإثنين", "wed": "الأربعاء", "thu": "الخميس", "fri": "الجمعة", "sat": "جل<PERSON>", "heatMapTitle": "خريطة الحرارة للملاحظات من العام الماضي", "heatMapDescription": "تُظهر عدد الملاحظات التي تم إنشاؤها في اليوم الواحد", "select-month": "اخت<PERSON> الشهر", "note-count": "ع<PERSON> البن<PERSON>", "max-daily-words": "أق<PERSON>ى عدد من الكلمات يوميًا", "active-days": "أيام نشطة", "total-words": "إجمالي الكلمات", "analytics": "تحليلات", "tag-distribution": "توزيع العلامة", "other-tags": "العلامات الأخرى", "tue": "الثلاثاء", "offline-status": "وضع عدم الاتصال", "offline-title": "أنت خارج الاتصال", "offline-description": "يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى", "retry": "إعادة المحاولة", "back-to-home": "العودة إلى الصفحة الرئيسية", "offline": "<PERSON>ير متصل", "close-background-animation": "إغلاق الخلفية المتحركة", "custom-bg-tip": "انتقل إلى https://www.shadergradient.co/ لإنشاء خلفية التدرج الخاصة بك", "custom-background-url": "الخلفية المخصصة", "share": "مشاركة", "need-password-to-access": "مطلوب كلمة مرور للوصول", "password-error": "خطأ في كلمة المرور", "cancel-share": "إلغاء المشاركة", "create-share": "إنشاء مشاركة", "share-link": "حصة الرابط", "set-access-password": "قم بتعيين كلمة مرور الوصول", "protect-your-shared-content": "حا<PERSON>ظ على محتواك المشترك", "access-password": "كلمة مرور الوصول", "select-date": "<PERSON><PERSON><PERSON> التاريخ", "expiry-time": "وقت انتهاء الصلاحية", "select-expiry-time": "اختر وقت انتهاء الصلاحية", "permanent-valid": "صالح دائما", "7days-expiry": "انتهاء صلاحية بعد 7 أيام", "custom-expiry": "انتهاء مخصص", "30days-expiry": "انتهاء صلاحية خلال 30 يومًا", "share-link-expired": "انتهت صلاحية الرابط المشاركة", "share-link-expired-desc": "لقد انتهت صلاحية هذا المشاركة، يرجى الاتصال بالمسؤول لإعادة مشاركتها!", "shared": "مشترك", "internal-shared": "مشترك داخلي", "edited": "تم التحرير", "move-down": "تحرك لأسفل", "provider-id": "مُعرف مُزوِّد", "provider-name": "اسم المزوِّد", "well-known-url": "عنوان الرابط المعروف", "authorization-url": "رابط التفويض", "token-url": "عنوان URL الرمزية", "userinfo-url": "معلومات المستخدم URL", "scope": "نطاق", "client-id": "معرّف العميل", "client-secret": "السر الخاص بالعميل", "sso-settings": "إعدادات الوصول الواحد (SSO)", "oauth2-providers": "موفِّري Oauth2", "add-oauth2-provider": "إضافة موفّر Oauth2", "add-provider": "إضافة مزود", "edit-oauth2-provider": "تحرير مزود <PERSON>h2", "confirm-delete-provider": "تأكيد حذف مزود الخدمة", "please-select-icon-from-iconify": "يرجى اختيار أيقونة من مجموعة الأيقونات.", "provider-icon": "أيقونة المزود", "select-provider-template": "اختيار قالب موفر", "provider-template": "قالب مزود الخدمة", "please-add-this-url-to-your-oauth-provider-settings": "يرجى إضافة عنوان الرابط هذا إلى إعدادات مزود خدمة OAuth الخاص بك", "redirect-url": "إعادة توجيه عنوان url", "sign-in-with-provider": "قم بتسجيل الدخول باستخدام {{ مزود }}", "community": "المجتمع", "theme-color": "لون السمة", "link-account": "ر<PERSON><PERSON> الحساب", "select-account": "تحديد الحساب", "link-account-warning": "يرجى ملاحظة أنه إذا قمت بربط حساباتك، فلن يتم مزامنة أية بيانات من الحساب الحالي إلى الحساب المرتبط.", "unlink-account": "فصل الحساب", "unlink-account-tips": "هل تؤكد الوصول إلى جميع الجمعيات باستخدام هذا الحساب؟", "login-type": "نوع تسجيل الدخول", "close-daily-review": "مراجعة يومية وثيقة", "max-home-page-width": "عرض الصفحة الرئيسية الأقصى", "max-home-page-width-tip": "إذا تم تعيينها على 0 فهذا هو العرض الأقصى", "no-comments-yet": "لا تعليقات حتى الآن", "author": "الكاتب", "from": "من", "reply-to": "ا<PERSON><PERSON><PERSON> على", "comment": "تعليق", "hub": "م<PERSON>و<PERSON>", "home-site": "موقع البيت", "use-blinko-hub": "استخدم برنام<PERSON><PERSON>", "full-screen": "شاشة كاملة", "exit-fullscreen": "الخروج من وضع ملء الشاشة", "no-note-associated": "لا يوجد مذكرة مرتبطة", "insert-context": "إدراج في السياق", "follow": "اتبع", "follower": "تابع", "following": "متابعة", "admin": "مدير الموقع", "site-url": "عنوان موقع بلينكو", "unfollow": "إلغاء المتابعة", "join-hub": "انضم للهب", "refresh": "تحديث", "comment-notification": "تنبيه التعليقات", "follow-notification": "متابعة الإشعارات", "followed-you": "تابعتك", "mark-all-as-read": "علم الكل كمقروء", "no-notification": "لا إشعار", "new-notification": "إشعا<PERSON> جديد", "notification": "الإشعار", "backup-success": "نجاح النسخ الاحتياطي 🎉", "system-notification": "إشعار النظام", "embedding-api-endpoint": "تضمين نقطة نهاية واجهة برمجة التطبيقات", "embedding-api-key": "تضمين مفتاح واجهة برمجة التطبيقات", "recommand": "اقتراح", "has-todo": "يج<PERSON> القيام به", "reference-by": "بالمرجعية", "hide-notification": "إخفاء الإشعارات", "search-settings": "إعدادات البحث...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "سيقوم هذا الإجراء بحذف الملفات المحددة ولا يمكن استعادتها، يرجى تأكيد.", "plugin-settings": "إعداد الإضافة", "installed-plugins": "تم التثبيت", "marketplace": "السوق", "local-development": "التنمية المحلية", "add-local-plugin": "أضف الإضافة المحلية", "local-plugin": "الإضافة المحلية", "uninstall": "إلغاء التثبيت", "install": "تثبيت", "downloads": "تنزيلات", "plugin-updated": "تم تحديث الإضافة", "plugin-update-failed": "فشل تحديث الإضافة", "plugin-connection-failed": "فشل الاتصال بالإضافة", "disconnect": "افصل", "local-development-description": "أضف إضافة تطوير محلية وقم بتصحيح الأخطاء فيها.", "ai": "الذكاء الاصطناعي", "ai-chat-box-notes": "أدناه تم استرداد الملاحظات ذات الصلة بالنسبة لك", "copy": "نسخة", "add-to-note": "أ<PERSON><PERSON> الملاحظة", "add-to-blinko": "<PERSON><PERSON><PERSON> بلينكو", "no-title": "بدون عنوان", "search-blinko-content-or-help-create": "ابحث في محتوى بلينكو أو ساعد في الإبداع...", "conversation-history": "سجل المحادثة", "new-conversation": "محاد<PERSON>ة جديدة", "knowledge-base-search": "بحث في قاعدة المعرفة", "add-tools-to-model": "ابحث عبر الإنترنت أو اسمح للذكاء الاصطناعي بالاتصال بـ API لـ blinko", "clear-current-content": "ام<PERSON><PERSON> المحتوى الحالي", "welcome-to-blinko": "مرحبًا، {{name}}", "ai-prompt-writing": "أنت كاتب محترف، يرجى كتابة مقال احترافي حول الموضوع الذي قدمه المستخدم.", "coding": "برمجة", "writing": "الكتابة", "ai-prompt-translation": "أنت مترجم محترف، يرجى ترجمة النص المقدّم من قِبل المستخدم إلى {{lang}}", "ai-prompt-coding": "أنت مبرمج محترف، رجاءً اكتب برنامج بايثون بسيط يُعتمد على الموضوع الذي يقدمه المستخدم.", "translation": "ترجمة", "first-char-delay": "تأخير الحرف الأول", "total-tokens": "إجمالي الرموز المميزة", "check-connect": "فحص", "check-connect-error": "قد يُضاف فشل الاتصال إلى نهاية /v1", "check-connect-success": "تحقق من نجاح الاتصال", "loading": "جارِ التحميل", "embedding-dimensions": "أبعاد التضمين", "embedding-dimensions-description": "عليك التأكد من أن أبعاد النموذج صحيحة، وتحتاج إلى فرض إعادة بناء سجلات الفهرس بعد إجراء التغييرات.", "model": "نموذج", "ai-tools": "أدوات الذكاء الاصطناعي", "tavily-api-key": "مفتاح واجهة برمجة تطبيقات البحث في \"تافيلي\"", "tavily-max-results": "نتائج تافيلي ماكس", "ai-prompt-writing-content": "اكتب مقالًا مكونًا من 200 كلمة واحفظه في ملاحظاتك", "ai-prompt-coding-content": "استخراج محتوى الويب من https://github.com/blinko-space/blinko", "stop-task": "<PERSON>ي<PERSON><PERSON><PERSON> المهمة", "processing": "معالجة", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "هناك مهمة إعادة بناء جارية، هل تريد إعادة التشغيل؟", "hide-blog-images": "إخفاء صور المدونة", "ai-prompt-translation-content": "تحقق من NO TAGS NOTES في اليومين الماضيين ووضع علامة عليها.", "ai-prompt-delete-content": "ابحث عن ملاحظات مؤرشفة ، وتلخيصها وحفظها كملاحظات جديدة ، وحذف هاتين الملاحظات المؤرشفة", "older": "أقدم", "newer": "<PERSON><PERSON><PERSON><PERSON>", "restore-this-version": "استعادة هذا الإصدار", "Note History": "<PERSON><PERSON><PERSON><PERSON> التاريخ", "View History Versions": "عرض إصدارات التاريخ", "history-note-only": "الانتباه: يحتوي هذا السجل على محتوى نص فقط ، وليس سجل الملف", "referenceResource": "مورد مرجعي", "to-ask-ai": "أن تسأل الذكاء الاصطناعي", "press-enter-to-select-first-result": "اضغط على Enter لتحديد النتيجة الأولى", "ask-ai": "اسأل الذكاء الاصطناعي", "ask-blinko-ai-about-this-query": "اسأل Blinko AI عن هذا الاستعلام", "search-or-ask-ai": "بحث ، أو الإعدادات أو اسأل الذكاء الاصطناعي ...", "plugin": "البرنامج المساعد", "editor-preview": "<PERSON><PERSON><PERSON><PERSON>", "auto-add-tags": "تلقائي إضافة العلامات", "add-as-comment": "<PERSON><PERSON><PERSON>عليق", "choose-what-to-do-with-ai-results": "اختر ما يجب فعله بنتائج الذكاء الاصطناعي", "ai-post-processing-mode": "وضع معالجة ما بعد الذكاء الاصطناعي", "ai-post-processing-prompt": "تعليق تحرير لاحق بواسطة الذكاء الاصطناعي", "align-center": "مر<PERSON><PERSON>", "align-left": "غاد<PERSON>", "align-right": "يمين", "alternate-text": "نص بديل", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "تحليل محتوى الملاحظة التالية واقترح العلامات المناسبة وتقديم ملخص موجز", "check": "قائمة المهام", "close": "يغلق", "code-theme": "معاينة موضوع كتلة الكود", "column": "<PERSON><PERSON><PERSON><PERSON>", "content-generated-by-ai": "المحتوى الناتج عن الذكاء الاصطناعي", "content-theme": "معاينة موضوع المحتوى", "define-custom-prompt-for-ai-to-process-notes": "قم بتشغيل الذكاء الصناعي للتعليق على الملاحظة الحالية. على سبيل المثال: يرجى تلخيص محتوى الملاحظة. إذا كان محتوى الملاحظة أقل من 10 كلمات، فيرجى صقلها لي.", "enter-custom-prompt-for-post-processing": "أدخل مطالبة مخصصة للمعالجة بعد", "enter-spotify-consumer-key": "أدخل مفتاح Spotify API", "enter-spotify-consumer-secret": "أدخل Spotify Consumer Secret", "exclude-tag-from-embedding": "استبعاد المحتوى الموسومة", "music-settings": "إعدادات الموسيقى", "prompt-used-for-post-processing-notes": "موجه يستخدم لملاحظات المعالجة بعد ذلك", "rebuild-in-progress": "إعادة البناء في التقدم", "rest-user-info": "راحة معلومات المستخدم", "setting": "", "spotify-consumer-key": "سبوتيفي مفتاح API", "spotify-consumer-secret": "Spotify API Secret", "enable-ai-post-processing": "تمكين معالجة ما بعد الذكاء الاصطناعي", "automatically-process-notes-after-creation-or-update": "معالجة الملاحظات تلقائيًا بعد الإنشاء", "can-generate-summaries-tags-or-perform-analysis": "يمكن توليد علامات ملخصات على أداء التحليل", "ai-post-processing": "معالجة ما بعد الذكاء الاصطناعي", "model-list-updated": "قائمة النماذج المحدثة", "to-search-tags": "للبحث عن العلامات", "app-upgrade-required": "مطلوب ترقية التطبيق", "current-app-version": "النسخة الحالية من التطبيق", "required-app-version": "النسخة المطلوبة من التطبيق", "upgrade": "ترقية", "online-search": "البحث على الإنترنت", "smart-edit": "تحرير ذكي", "function-call-required": "مطلوب استدعاء الوظيفة", "smart-edit-prompt": "تحرير ذكي", "define-instructions-for-ai-to-edit-your-notes": "يمكنك استخدام التوجيهات للتلاعب بالملاحظات، على سبيل المثال: إذا احتوت ملاحظة على رابط، قم بتلخيص محتوى الرابط أدناه من الملاحظة الأصلية وولِّد تسمية.", "rebuild-started": "بد<PERSON>ت عملية الإعادة البناء", "rebuild-stopped-by-user": "إعادة البناء توقفت بواسطة المستخدم", "random-mode": "جولة عشوائية", "related-notes": "ملاحظات ذات صلة", "no-related-notes-found": "لم يتم العثور على ملاحظات ذات صلة", "advanced": "متقدم", "rerank-model-description": "حدد نموذجًا لإعادة ترتيب نتائج الفيكتور لتحسين دقة البحث", "rerank-model": "نموذج إعادة الترتيب", "rerank": "إعادة ترتيب", "use-custom-rerank-endpoint-description": "عند التمكين، سيتم إعادة ترتيب نقاط النهاية ومفاتيح API للنموذج المضمن", "use-embedding-endpoint": "استخدم نقطة انتهاء التضمين", "rerank-score-description": "حدد عتبة درجة لنموذج إعادة الترتيب، سيتم تصفية النتائج دونها.", "public-share": "المشاركة العامة", "internal-share": "المشاركة الداخلية", "no-team-members-found": "لم يتم العثور على أعضاء في الفريق", "selected-users": "المستخدمين المحددين", "tags-prompt": "علامات موجه", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "المحفز المستخدم لتوليد العلامات تلقائيًا. إذا تُرك فارغًا، سيتم استخدام المحفز الافتراضي.", "generate-low-permission-token": "توليد رمز الإذن المنخفض", "low-permission-token-desc": "يمكن للرموز ذات الأذونات المنخفضة الوصول فقط إلى نقطة نهاية upsertNote ونقطة نهاية الدردشة الذكاء الاصطناعي. لا يمكنهم ​​الوصول إلى معلومات حسابك أو الملاحظات الأخرى. هذا مثالي لحالات استخدام مثل بوتات تيليجرام أو بوتات ويتشات، حيث ترغب في التأكد من أنهم لا يستطيعون ​​الولوج إلى أية ملاحظات أخرى.", "this-token-is-only-displayed-once-please-save-it-properly": "هذا الرمز يُعرض مرة واحدة فقط، يُرجى حفظه بشكل صحيح", "refresh-model-list": "الحصول على قائمة النماذج", "please-set-the-embedding-model": "الرجاء تعيين نموذج الإدراج", "blinko-endpoint": "نقطة النهاية بلينكو", "enter-blinko-endpoint": "رابط تثبيت Blinko الخاص بك", "login-failed": "فشل تسجيل الدخول", "verification-failed": "فشل التحقق", "download-success": "تم التنزيل بنجاح", "download-failed": "تحميل فاشل", "downloading": "جاري التحميل", "hide-pc-editor": "隐藏PC端编辑器  \nإخفاء محرر جهاز الكمبيوتر", "import-from-markdown": "استيراد من ملف Markdown", "import-from-markdown-tip": "استيراد من ملف .md أو أرشيف .zip يحتوي على ملفات .md", "not-a-markdown-or-zip-file": "ليس ملف Markdown أو zip. الرجاء اختيار ملف .md أو .zip.", "todo": "الوكيل", "restore": "استعادة", "complete": "完成  \nإكمال", "today": "اليوم", "yesterday": "البارحة", "common.refreshing": "جارٍ التحديث", "common.releaseToRefresh": "أطلق للتحديث", "common.pullToRefresh": "السحب لأسفل للتحديث", "edit-message-warning": "تحرير هذه الرسالة سيقوم بمسح جميع سجلات المحادثات اللاحقة، وإعادة توليد الردود من الذكاء الاصطناعي.", "enter-your-message": "أدخل رسالتك", "set-deadline": "设置截止日期  \nتحديد الموعد النهائي", "expired": "منتهي الصلاحية", "expired-days": "منتهية الصلاحية منذ {{count}} يومًا", "expired-hours": "انتهت الصلاحية منذ {{count}} ساعة", "expired-minutes": "انتهت صلاحيته منذ {{count}} دقيقة", "days-left": "{{count}} بعد يوم.", "hours-left": "{{count}} ساعة لاحقًا", "minutes-left": "{{count}} دقيقة بعد", "about-to-expire": "على وشك الانتهاء", "1-day": "一天", "1-week": "一周  \nأسبوع", "1-month": "一个月  \nشهر واحد", "quick-select": "الاختيار السريع", "import-ai-configuration": "استيراد إعدادات الذكاء الصناعي", "would-you-like-to-import-this-configuration": "هل تريد استيراد إعدادات الذكاء الاصطناعي هذه؟", "detected-ai-configuration-to-import": "تم اكتشاف إعدادات AI في انتظار الاستيراد", "importing": "جارٍ الاستيراد", "cache-cleared-successfully": "تم مسح ذاكرة التخزين المؤقت بنجاح! ستتم إعادة تحميل الصفحة تلقائيًا.", "failed-to-clear-cache": "إزالة ذاكرة التخزين المؤقت للمتصفح فشل. حاول التحديث يدويًا (Ctrl+Shift+R).", "select-deployment": "اختر النشر", "deployment-name": "اسم النشر", "please-set-the-api-endpoint": "يرجى تعيين نقطة نهاية API", "please-set-the-api-key": "يرجى تعيين مفتاح API"}