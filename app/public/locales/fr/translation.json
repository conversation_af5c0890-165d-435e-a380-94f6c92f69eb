{"spotify-consumer-key-tip": "Utilisé pour obtenir la couverture des albums de musique mp3", "spotify-consumer-key-tip-2": "Obtenez la clé API à partir de https://developer.spotify.com/", "blinko": "Blinko", "notes": "Notes", "resources": "Ressources", "archived": "Archives", "settings": "Paramètres", "total-tags": "TOTAL DES ÉTIQUETTES", "search": "Recherche...", "i-have-a-new-idea": "J'ai une nouvelle idée...", "add-tag": "Ajouter une étiquette", "ai-model": "Modèle IA", "already-have-an-account-direct-login": "Vous avez déjà un compte ? Se connecter", "api-endpoint": "Point final de l'API", "archive": "Archiver", "ask-about-your-notes": "<PERSON><PERSON><PERSON> vos notes", "cancel": "Annuler", "cancel-top": "<PERSON><PERSON><PERSON> le haut", "change-user-info": "Modifier les informations de l'utilisateur", "confirm-your-password": "Confirmez votre mot de passe", "convert-to": "Convertir en", "convert-to-blinko": "Convertir en Blinko", "convert-to-note": "Convertir en note", "create-successfully": "Créé(e) avec succès", "create-successfully-is-about-to-jump-to-the-login": "<PERSON><PERSON><PERSON>(e) avec succès, passage à la connexion", "delete": "<PERSON><PERSON><PERSON><PERSON>", "delete-confirm": "<PERSON><PERSON><PERSON><PERSON> Confirmer", "edit": "Editer", "every-day": "Tous les jours", "every-half-year": "To<PERSON> les semestres", "every-month": "<PERSON><PERSON> mois", "every-three-month": "Tous les trois mois", "every-week": "<PERSON><PERSON>", "hello": "Bonjour", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "Bonjour {{nom}}, je peux rechercher les notes pour vous, comment puis-je vous aider aujourd'hui ?", "import-from-bko": "Importation à partir de .bko", "in-progress": "En cours...", "insert-codeblock": "Insérer un bloc de code", "insert-sandpack": "Ins<PERSON><PERSON> le sac de sable", "insert-table": "Insérer un tableau", "items": "articles", "last-run": "DERNIÈRE COURSE", "logout": "Déconnexion", "multiple-select": "Sélection multiple", "name": "Nom", "name-db": "NOM", "new-version-detected-click-to-get-the-latest-version": "🎉 Nouvelle version détectée, cliquer pour obtenir la dernière version", "nickname": "Surnom", "no-data-here-well-then-time-to-write-a-note": "Pas de données ici ? Dans ce cas, il est temps de rédiger une note !", "note": "Note", "numbered-list": "Liste numérotée", "preference": "Préférences", "recording": "Enregistrement", "recovery": "Récupération", "required-items-cannot-be-empty": "Les éléments requis ne peuvent pas être vides", "rest-user-password": "Mot de passe de l'utilisateur du repos", "running": "La course à pied", "save": "<PERSON><PERSON><PERSON><PERSON>", "schedule-archive-blinko": "Archive du calendrier Blinko", "schedule-back-up": "Planification de la sauvegarde", "schedule-task": "Tâche de planification", "show-less": "<PERSON><PERSON> moins", "show-more": "Afficher plus", "sign-in": "Se connecter", "stopped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theme": "Thème", "there-are-no-resources-yet-go-upload-them-now": "Il n'y a pas encore de ressources, uploadez-les maintenant", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "Cette opération supprime l'étiquette associée et ne peut pas être restaurée.", "this-operation-will-be-delete-resource-are-you-sure": "Cette opération permettra de supprimer des ressources. Êtes-vous sûr ?", "top": "<PERSON><PERSON> de page", "total": "Total", "update-successfully": "Mise à jour réussie", "upload-file": "Uploader le fichier", "use-ai": "Utiliser l'AI", "user-custom-openai-api-key": "Clé Api OpenAI personnalisée par l'utilisateur", "user-custom-azureopenai-api-instance": "Nom de l'instance Azure OpenAI", "user-custom-azureopenai-api-deployment": "Nom du déploiement Azure OpenAI", "user-custom-azureopenai-api-version": "Version API", "user-or-password-error": "Erreur d'utilisateur ou de mot de passe", "username": "Nom d'utilisateur", "backup-file": "FICHIER DE SAUVEGARDE", "basic-information": "Informations de base", "bulleted-list": "Liste à puces", "change-type": "Modifier le type", "confirm": "Confirmer", "confirm-password": "Confirmez le mot de passe", "confirm-to-delete": "Confirmer pour supprimer !", "confrim": "Confirmer", "congratulations-youve-reviewed-everything-today": "vous avez tout passé en revue aujourd'hui.", "daily-review": "Revue quotidienne", "detail": "Détail", "enter-send-shift-enter-for-new-line": "<PERSON><PERSON><PERSON> envoyer, Shift+Enter pour une nouvelle ligne", "all-notes-have-been-loaded": "Toutes les notes {{items}} ont été chargées", "check-list": "Liste de contrôle", "delete-success": "Supprimer avec succès", "enter-your-name": "Entrez votre nom", "enter-your-password": "Entrez votre mot de passe", "enter-your-username": "Entrez votre nom d'utilisateur", "import": "Importation", "language": "<PERSON><PERSON>", "must-start-with-http-s-or-use-api-openai-as-default": "Doit commencer par http(s):// ou utiliser /api/openai par défaut.", "need-to-create-an-account": "Besoin de créer un compte ?", "insert-hashtag": "Insérer un hashtag", "model-provider": "<PERSON><PERSON><PERSON><PERSON>", "no-tag-found": "<PERSON><PERSON>ne éti<PERSON>te trouvée", "not-a-bko-file": "pas un fichier bko", "password": "Mot de passe", "schedule": "CALENDRIER", "show-navigation-bar-on-mobile": "Barre de navigation cachée sur mobile", "sign-up": "S'inscrire", "status": "STATUT", "the-two-passwords-are-inconsistent": "Les deux mots de passe sont incohérents", "your-changes-have-been-saved": "Vos modifications ont été enregistrées !", "keep-sign-in": "Rester connecté(e)", "operation-failed": "L'opération a échoué.", "reviewed": "Examiné", "created-in": "<PERSON><PERSON><PERSON> en", "set-as-public": "Définir comme public", "unset-as-public": "Non défini comme public", "no-tag": "Pas d'étiquette", "with-link": "Avec lien", "has-file": "A le fichier", "created-at": "<PERSON><PERSON><PERSON>", "user-list": "Utilisateurs", "create-user": "C<PERSON>er un utilisateur", "action": "Action", "original-password": "Mot de passe original", "edit-user": "Modifier l'utilisateur", "import-from-memos-memos_prod-db": "Importation de Memos(memos_prod.db)", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "Lors de l'exportation de memos_prod.db, veuil<PERSON><PERSON> fermer le conteneur memos afin d'éviter une perte partielle des données.", "go-to-share-page": "<PERSON>er à la page de partage", "import-done": "Importation effectuée", "rebuilding-embedding-progress": "Reconstruire Intégrer le progrès", "rebuild-embedding-index": "Reconstruire l'index d'intégration", "rebuild": "Reconstruction", "notes-imported-by-other-means-may-not-have-embedded-vectors": "Les notes importées par d'autres moyens peuvent ne pas avoir de vecteurs intégrés", "order-by-create-time": "Trier par date de création", "time-format": "Format de l'heure", "version": "Version", "new-version-available": "Nouvelle version disponible", "storage": "Stockage", "local-file-system": "Système de fichiers local", "object-storage": "Stockage d'objets", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "En plus du modèle GPT, il est nécessaire de s'assurer qu'il est possible d'invoquer le modèle", "speech-recognition-requires-the-use-of": "La reconnaissance vocale nécessite l'utilisation de", "ai-expand": "Extension de l'IA", "ai-polish": "AI Polish", "accept": "Accepter", "reject": "<PERSON><PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON><PERSON>", "card-columns": "Colonnes de la carte", "select-a-columns": "Sélectionner une colonne", "width-less-than-1024px": "Largeur inférieure à 1024px", "width-less-than": "Largeur inférieure à", "small-device-card-columns": "Colonnes de la carte des petits appareils", "medium-device-card-columns": "Colonnes de la carte de dispositif moyen", "large-device-card-columns": "Grandes colonnes de la carte d'appareil", "device-card-columns": "Colonnes de la carte d'appareil", "columns-for-different-devices": "Colonnes pour différents appareils", "mobile": "Mobile", "tablet": "Tablette", "desktop": "Bureau", "chars": "Chars", "text-fold-length": "Longueur du pli du texte", "title-first-line-of-the-text": "Titre (première ligne du texte)", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "Contenu (reste du texte, si le texte est plus long que la longueur)", "ai-tag": "Étiquette AI", "article": "Article", "embedding-model": "Mod<PERSON>le d'intégration", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "Si vous avez beaucoup de notes, vous pouvez consommer un certain nombre de jetons.", "force-rebuild": "Reconstruction des forces", "force-rebuild-embedding-index": "La reconstruction forcée reconstruit toutes les données qui ont été complètement indexées.", "embedding-model-description": "L'index doit être reconstruit après avoir changé de modèle intégré.", "top-k-description": "Nombre maximum de documents éventuellement renvoyés", "embedding-score-description": "Le seuil de similarité pour les requêtes est généralement la distance de la somme euclidienne", "embedding-lambda-description": "Résultat de la requête Paramètre de pondération de la diversité", "update-tag-icon": "Mise à jour de l'icône de la balise", "delete-only-tag": "Supprimer uniquement l'étiquette", "delete-tag-with-note": "Supprimer une étiquette avec une note", "update-tag-name": "Mise à jour du nom de l'étiquette", "thinking": "Réflexion...", "select-all": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "deselect-all": "<PERSON><PERSON>", "insert-before": "<PERSON><PERSON><PERSON><PERSON> avant", "insert-after": "<PERSON><PERSON><PERSON><PERSON> après", "update-name": "Nom de la mise à jour", "ai-emoji": "<PERSON>", "custom-icon": "Icône personnalisée", "ai-enhanced-search": "Recherche améliorée par l'IA", "preview-mode": "Mode de prévisualisation", "source-code": "Code source", "camera": "Appareil photo", "reference": "Référence", "reference-note": "Note de référence", "source-code-mode": "Mode code source", "heading": "Rubrique", "paragraph": "Paragraphe", "quote": "Citation", "bold": "Gras", "remove-italic": "Supprimer l'italique", "underline": "<PERSON><PERSON><PERSON>", "italic": "Italique", "remove-bold": "Supprimer les caractères gras", "remove-underline": "Supprimer le soulignement", "select-block-type": "Sélectionner le type de bloc", "block-type-select-placeholder": "Type de bloc", "trash": "<PERSON><PERSON><PERSON>", "custom-path": "<PERSON>emin d'accès personnal<PERSON>", "page-size": "<PERSON><PERSON>", "toolbar-visibility": "Visibilité de la barre d'outils", "always-hide-toolbar": "Toujours cacher", "always-show-toolbar": "<PERSON><PERSON><PERSON><PERSON> montrer", "hide-toolbar-on-mobile": "C<PERSON> sur mobile", "select-toolbar-visibility": "Sélection de la visibilité de la barre d'outils", "select-a-time-format": "Sélectionner un format d'heure", "enter-code-shown-on-authenticator-app": "Saisir le code affiché sur l'application d'authentification", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "Ouvrez votre application d'authentification tierce et saisissez les codes affichés à l'écran.", "two-factor-authentication": "Authentification à deux facteurs", "scan-this-qr-code-with-your-authenticator-app": "Scannez ce code QR avec votre application d'authentification", "or-enter-this-code-manually": "Ou saisissez ce code manuellement :", "verify": "Vérifier", "about": "A propos de", "upload": "Uploader", "days": "Jours", "select-model-provider": "Sélectionner le fournisseur du modèle", "allow-register": "Autoriser le registre", "access-token": "<PERSON>on d'a<PERSON>ès", "bucket": "<PERSON><PERSON>", "region": "Région", "access-key-secret": "Clé d'accès secret", "access-key-id": "Clé d'accès", "share-and-copy-link": "Partager et copier le lien", "copy-share-link": "Copier le lien de partage", "endpoint": "Point final", "export-format": "Format d'exportation", "export": "Exportation", "time-range": "Plage de temps", "all": "Tous", "exporting": "Exporter...", "has-image": "A l'image", "has-link": "A le lien", "filter-settings": "Paramètres du filtre", "tag-status": "Statut de l'étiquette", "all-notes": "Toutes les notes", "with-tags": "Avec Tags", "without-tags": "Sans étiquette", "select-tags": "Sélectionner les étiquettes", "additional-conditions": "Conditions supplémentaires", "apply-filter": "App<PERSON><PERSON> le filtre", "to": "à", "start-date": "Date de début", "end-date": "Date de fin", "reset": "Remise à zéro", "no-condition": "Pas de condition", "public": "Public", "ai-model-tooltip": "Entrez le nom du modèle à utiliser, par exemple gpt-3.5-turbo, gpt-4, gpt-4o, gpt-4o-mini.", "user-custom-azureopenai-api-deployment-tooltip": " Sai<PERSON><PERSON>z le nom de déploiement à utiliser, par exemple gpt-4o", "ollama-ai-model-tooltip": "Sai<PERSON><PERSON>z le nom du modèle à utiliser, par exemple llama3.2.", "ollama-default-endpoint-is-http-localhost-11434": "Le point de terminaison par défaut d'Ollama est http://localhost:11434", "your-azure-openai-instance-name": "Nom de votre instance Azure OpenAI", "ai-generate-emoji": "", "ai-generating-emoji": "", "api-key": "", "date-range": "", "days-ago": "", "enter-your-api-key": "", "hours-ago": "", "impoort-from-bko": "", "minutes-ago": "", "months-ago": "", "superadmin": "", "user": "Utilisa<PERSON>ur", "weeks-ago": "", "years-ago": "", "align-center": "Centre", "both": "Les deux", "check": "Liste de tâches", "close": "<PERSON><PERSON><PERSON>", "code-theme": "Aperçu du thème de bloc de code", "content-theme": "Aperçu du thème de contenu", "dark-mode": "Mode sombre", "delete-column": "Supprimer la ligne", "delete-row": "Supprimer la colonne", "devtools": "Outils de développement", "download-tip": "Le navigateur ne prend pas en charge la fonction de téléchargement", "edit-mode": "Activer/désactiver le mode d'édition", "emoji": "<PERSON><PERSON><PERSON>", "exclude-tag-from-embedding": "Exclure le contenu balisé", "exclude-tag-from-embedding-desc": "Sélectionnez une balise pour exclure ses notes associées de la génération du vecteur d'encastrement de l'IA.", "exclude-tag-from-embedding-tip": "Les notes avec cette étiquette seront exclues du traitement d'intégration de l'IA.", "file-type-error": "Le type de fichier est une erreur.", "follow-system": "Système de suivi", "footnote-ref": "Ré<PERSON><PERSON><PERSON><PERSON> de bas de page", "fullscreen": "Activer/désactiver le mode plein écran", "generate": "Génération", "heading1": "Titre 1", "heading2": "Sous-titre 2", "heading3": "Titre 3", "heading4": "Titre 4", "heading5": "Niveau 5", "heading6": "Niveau 6", "headings": "Titres", "help": "Aider", "image-url": "URL de l'image", "indent": "Retrait", "info": "Informations", "inline-code": "Code en ligne", "insert-column-left": "Insérez 1 à gauche", "insert-column-right": "Insérez 1 à droite", "insert-row-above": "Insérez 1 ci-dessus", "insert-row-below": "Insérez 1 ci-dessous", "instant-rendering": "<PERSON><PERSON> instantan<PERSON>", "light-mode": "Mode clair", "line": "Ligne", "link": "<PERSON><PERSON>", "link-ref": "<PERSON><PERSON> de réfé<PERSON>ce", "list": "Liste", "more": "Plus", "name-empty": "Le nom est vide", "preview": "<PERSON><PERSON><PERSON><PERSON>", "select-model": "Sélectionner un modèle", "search-tags": "Balises de recherche", "insert-attachment-or-note": "Insérer dans la pièce jointe ou la note ?", "context": "Contexte", "paste-to-note-or-attachment": "Êtes-vous sûr de coller dans le contexte ou en pièce jointe ?", "attachment": "Pièce jointe", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "Après la suppression, toutes les données utilisateur seront effacées et définitivement irrécupérables.", "upload-completed": "Upload terminé", "upload-cancelled": "Upload annulé", "upload-failed": "Upload <PERSON><PERSON><PERSON>", "import-from-bko-tip": "Le téléversement vers s3 pour la récupération n'est pas pris en charge actuellement. Veuillez désactiver temporairement l'option s3 lorsque vous souhaitez récupérer.", "music-settings": "Musique", "spotify-consumer-key": "Clé API Spotify", "spotify-consumer-secret": "Secret API Spotify", "enter-spotify-consumer-key": "Entrez la Clé API Spotify", "enter-spotify-consumer-secret": "Entrez le Secret API Spotify", "edit-time": "Modifier l'heure", "ai-write": "Écrire en IA", "download": "Télécharger", "rename": "<PERSON>mmer", "move-up": "<PERSON><PERSON>", "cut": "Couper", "paste": "<PERSON><PERSON>", "confirm-delete": "Confirmer la <PERSON>", "confirm-delete-content": "Êtes-vous sûr de vouloir supprimer {{name}} ? Cette action est irréversible.", "folder-name": "Nom du dossier", "file-name": "Nom du fichier", "operation-success": "Opération réussie", "cloud-file": "Fichier en nuage", "move-to-parent": "<PERSON><PERSON><PERSON><PERSON> vers le parent", "no-resources-found": "<PERSON><PERSON><PERSON> ressource trouvée", "operation-in-progress": "Opération en cours", "new-folder": "Nouveau Dossier", "folder-name-exists": "Le nom du dossier existe", "folder-name-required": "Nom du dossier requis", "collapse": "Effondrement", "show-all": "<PERSON><PERSON><PERSON><PERSON> tout", "sun": "<PERSON><PERSON>", "mon": "Mon", "wed": "<PERSON><PERSON><PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "<PERSON><PERSON><PERSON><PERSON>", "sat": "<PERSON><PERSON>", "heatMapTitle": "Carte thermique des notes de l'année dernière", "heatMapDescription": "Montre le nombre de notes créées par jour", "select-month": "Sé<PERSON><PERSON>ner le mois", "note-count": "Décompte", "max-daily-words": "Mots quotidiens max", "active-days": "Jours actifs", "total-words": "<PERSON><PERSON> to<PERSON>", "analytics": "Analytique", "tag-distribution": "Distribution des étiquettes", "other-tags": "Autres balises", "tue": "<PERSON><PERSON>", "offline-status": "Mode hors ligne", "offline-title": "Vous êtes hors ligne", "offline-description": "Veuillez vérifier votre connexion internet et réessayer", "retry": "<PERSON><PERSON><PERSON><PERSON>", "back-to-home": "Retour à la page d'accueil", "offline": "<PERSON><PERSON> ligne", "close-background-animation": "Animation d'arrière-plan proche", "custom-bg-tip": "Allez sur https://www.shadergradient.co/ pour créer votre propre arrière-plan dégradé.", "custom-background-url": "Arrière-plan person<PERSON><PERSON>", "share": "Partager", "need-password-to-access": "Accès par mot de passe requis", "password-error": "Erreur de mot de passe", "cancel-share": "Annuler le partage", "create-share": "<PERSON><PERSON><PERSON>", "share-link": "Partager le lien", "set-access-password": "Définir le mot de passe d'accès", "protect-your-shared-content": "Protégez votre contenu partagé.", "access-password": "Mot de passe d'accès", "select-date": "Sélectionner la date", "expiry-time": "Heure d'expiration", "select-expiry-time": "Sélectionnez l'heure d'expiration", "permanent-valid": "Valide en permanence", "7days-expiry": "Expiration de 7 jours", "custom-expiry": "Expiration personnalisée", "30days-expiry": "30 jours d'expiration", "share-link-expired": "Lien partagé expiré", "share-link-expired-desc": "Cette part a expiré, veuillez contacter l'administrateur pour la re-partager !", "shared": "Partagé", "internal-shared": "Partagé en interne", "edited": "<PERSON><PERSON><PERSON><PERSON>", "move-down": "Descendre", "provider-id": "Identifiant du fournisseur", "provider-name": "Nom du fournisseur", "well-known-url": "URL bien connue", "authorization-url": "URL d'autorisation", "token-url": "URL de jeton", "userinfo-url": "URL d'informations utilisateur", "scope": "Portée", "client-id": "Identifiant client", "client-secret": "Secret client", "sso-settings": "Paramètres SSO", "oauth2-providers": "Fournisseurs Oauth2", "add-oauth2-provider": "Ajouter un fournisseur Oauth2", "add-provider": "Ajouter un fournisseur", "edit-oauth2-provider": "Modifier le fournisseur Oauth2", "confirm-delete-provider": "Confirmer la <PERSON> du fournisseur", "please-select-icon-from-iconify": "Veuillez sélectionner une icône à partir d'Iconify.", "provider-icon": "Icône du fournisseur", "select-provider-template": "Sélectionner le modèle de fournisseur", "provider-template": "<PERSON><PERSON><PERSON><PERSON>", "please-add-this-url-to-your-oauth-provider-settings": "Veuillez ajouter cette URL à vos paramètres de fournisseur OAuth.", "redirect-url": "Rediriger l'URL", "sign-in-with-provider": "Connectez-vous avec {{ provider }}", "community": "Communauté", "theme-color": "Couleur Thème", "link-account": "<PERSON><PERSON> le compte", "select-account": "Sélectionner un compte", "link-account-warning": "Veuillez noter que si vous liez vos comptes, toutes les données du compte actuel ne seront pas synchronisées avec le compte lié.", "unlink-account": "Dissocier le compte", "unlink-account-tips": "Confirmez-vous l'accès à toutes les associations avec ce compte ?", "login-type": "Type de connexion", "close-daily-review": "Examen quotidien de clôture", "max-home-page-width": "Largeur maximale de la page d'accueil", "max-home-page-width-tip": "Si réglé à 0, c'est la largeur maximale.", "no-comments-yet": "Pas de commentaires pour le moment", "author": "<PERSON><PERSON><PERSON>", "from": "De", "reply-to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "comment": "Commentaire", "hub": "<PERSON><PERSON><PERSON>", "home-site": "Site d'accueil", "use-blinko-hub": "<PERSON><PERSON><PERSON><PERSON>", "full-screen": "Plein écran", "exit-fullscreen": "<PERSON><PERSON><PERSON> le mode plein écran", "no-note-associated": "Pas de note associée", "insert-context": "Insérer dans le contexte", "follow": "Suivre", "follower": "<PERSON><PERSON>le", "following": "Suivant", "admin": "Webmestre", "site-url": "Adresse URL du site Blinko", "unfollow": "Ne plus suivre", "join-hub": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "Actualiser", "comment-notification": "Notification de commentaire", "follow-notification": "Notification de suivi", "followed-you": "je t'ai suivi", "mark-all-as-read": "<PERSON><PERSON> tout comme lu", "no-notification": "Aucune notification", "new-notification": "Nouvelle notification", "notification": "Notification", "backup-success": "Sauvegarde réussie🎉", "system-notification": "Notification système", "embedding-api-endpoint": "Incorporation du point de terminaison de l'API", "embedding-api-key": "Clé API d'intégration", "recommand": "Recommander", "has-todo": "A faire", "reference-by": "Référence par", "hide-notification": "Masquer la notification", "search-settings": "Paramètres de recherche...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "Cette opération supprimera les fichiers sélectionnés et ne pourra pas être restaurée. Veuillez confirmer.", "plugin-settings": "Paramètres du plugin", "installed-plugins": "Installé", "marketplace": "Place de marché", "local-development": "Développement local", "add-local-plugin": "Ajouter le plugin local", "local-plugin": "Plugin local", "uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "install": "Installer", "downloads": "Téléchargements", "plugin-updated": "Extension mise à jour", "plugin-update-failed": "Mise à jour du plugin écho<PERSON>e", "plugin-connection-failed": "Échec de la connexion du plug-in", "disconnect": "Déconnecter", "local-development-description": "Ajoutez un plug-in de développement local et déboguez-le.", "ai": "IA", "ai-chat-box-notes": "Ci-dessous se trouvent les notes pertinentes récupérées pour vous", "copy": "<PERSON><PERSON>", "add-to-note": "Ajouter à la note", "add-to-blinko": "Ajouter à Blinko", "no-title": "Sans titre", "search-blinko-content-or-help-create": "Recherchez du contenu blinko ou aidez à créer...", "conversation-history": "Historique des conversations", "new-conversation": "Nouveau Chat", "knowledge-base-search": "Recherche dans la base de connaissances", "add-tools-to-model": "Effectuez une recherche en ligne ou autorisez l'IA à appeler l'API blinko.", "clear-current-content": "Effacer le contenu actuel", "welcome-to-blinko": "Bienvenue, {{name}}", "ai-prompt-writing": "Vous êtes écrivain professionnel, veuille<PERSON> rédiger un article professionnel sur le sujet fourni par l'utilisateur.", "coding": "Codage", "writing": "Écriture", "ai-prompt-translation": "Vous êtes un traducteur professionnel, veuillez traduire le texte fourni par l'utilisateur en {{lang}}", "ai-prompt-coding": "Vous êtes un codeur professionnel, veuillez écrire un programme Python simple basé sur le sujet fourni par l'utilisateur.", "translation": "Traduction", "first-char-delay": "Premier retard de caractère", "total-tokens": "Nombre total de jetons", "check-connect": "Vérifier", "check-connect-error": "La défaillance de connexion peut être ajoutée à la fin de /v1", "check-connect-success": "Vérification de la réussite de connexion", "loading": "Chargement", "embedding-dimensions": "Dimensions d'encastrement", "embedding-dimensions-description": "Vous devez vous assurer que les dimensions du modèle sont correctes et vous devez forcer la reconstruction des index des enregistrements après les modifications.", "model": "<PERSON><PERSON><PERSON><PERSON>", "ai-tools": "Outils d'IA", "tavily-api-key": "Clé API de recherche Tavily", "tavily-max-results": "Résultats Maximaux de Tavily", "ai-prompt-writing-content": "Rédigez un article de 200 mots et enregistrez-le dans vos notes", "ai-prompt-coding-content": "Extraction du contenu web https://github.com/blinko-space/blinko", "stop-task": "<PERSON><PERSON><PERSON><PERSON> tâche", "processing": "Traitement", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "Il y a une tâche de reconstruction en cours, voulez-vous redémarrer ?", "hide-blog-images": "Masquer les images du blog", "ai-prompt-translation-content": "Vérifiez les notes No Tags au cours des deux derniers jours et étiquetez-les.", "ai-prompt-delete-content": "Trouvez 2 notes archivées, résumez-les et économisez en tant que nouvelles notes, et supprimez ces deux notes archivées", "older": "Plus vieux", "newer": "Plus récent", "restore-this-version": "Restaurer cette version", "Note History": "Note historique", "View History Versions": "Voir les versions de l'histoire", "history-note-only": "Attention: cet historique ne contient que du contenu texte, pas de l'historique des fichiers", "referenceResource": "Ressource de référence", "to-ask-ai": "Demander AI", "press-enter-to-select-first-result": "Appuyez sur Entrée pour sélectionner le premier résultat", "ask-ai": "Demandez à l'AI", "ask-blinko-ai-about-this-query": "Demandez à Blinko Ai sur cette requête", "search-or-ask-ai": "Note de recherche, paramètres ou demandez à l'IA ...", "plugin": "Plugin", "editor-preview": "éditeur", "auto-add-tags": "Ajouter automatiquement des balises", "add-as-comment": "Ajouter comme commentaire", "choose-what-to-do-with-ai-results": "Choisissez quoi faire avec les résultats de l'IA", "ai-post-processing-mode": "Mode post-traitement de l'IA", "ai-post-processing-prompt": "invite de commentaire pour le post-traitement IA", "2fa-setup-successful": "2fa Configuration réussie", "align-left": "G<PERSON><PERSON>", "align-right": "<PERSON><PERSON><PERSON>", "alternate-text": "Texte alternatif", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "Analysez le contenu de la note suivante et suggérez des balises appropriées et fournissez un bref résumé", "code": "Bloc de code", "column": "Colonne", "content-generated-by-ai": "Contenu généré par l'IA", "copied": "<PERSON><PERSON><PERSON>", "define-custom-prompt-for-ai-to-process-notes": "Faites fonctionner l'IA pour commenter la note actuelle. Par exemple : Veuillez résumer le contenu de la note. Si le contenu de la note est inférieur à 10 mots, veuil<PERSON><PERSON> le peaufiner pour moi.", "down": "Vers le bas", "enter-custom-prompt-for-post-processing": "Entrez l'invite personnalisée pour le post-traitement", "ordered-list": "Liste de commandes", "outdent": "Exagé<PERSON>", "outline": "<PERSON>tour", "over": "sur", "performance-tip": "L'aperçu en temps réel nécessite $ {x} ms, vous pouvez le fermer", "prompt-used-for-post-processing-notes": "Invite utilisé pour les notes de post-traitement", "rebuild-in-progress": "Reconstruire en cours", "record": "Démarrer l'enregistrement / l'enregistrement de fin", "record-tip": "L'appareil ne prend pas en charge l'enregistrement", "redo": "<PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON>", "rest-user-info": "Informations sur l'utilisateur de repos", "role": "R<PERSON><PERSON>", "row": "<PERSON><PERSON><PERSON>", "setting": "", "spin": "Rotation", "split-view": "Vue de division", "strike": "<PERSON><PERSON><PERSON><PERSON>", "table": "<PERSON><PERSON>", "text-is-not-empty": "Texte (pas vide)", "title": "Titre", "tooltip-text": "Texte info-bulle", "undo": "<PERSON><PERSON><PERSON><PERSON>", "up": "En haut", "update": "Mise à jour", "updated-at": "Mettre à jour", "upload-error": "Télécharger une erreur", "uploading": "Téléchargement ...", "wysiwyg": "Wysiwyg", "enable-ai-post-processing": "Activer la post-traitement de l'IA", "automatically-process-notes-after-creation-or-update": "Traiter automatiquement les notes après la création", "can-generate-summaries-tags-or-perform-analysis": "Peut générer des balises de résumé sur l'analyse de performance", "ai-post-processing": "AI après traitement", "model-list-updated": "Liste de modèle mise à jour", "to-search-tags": "Chercher des étiquettes", "app-upgrade-required": "Mise à jour de l'application requise", "current-app-version": "Version actuelle de l'APP", "required-app-version": "Version de l'APP requise", "upgrade": "Mise à niveau", "online-search": "Recherche en ligne", "smart-edit": "<PERSON><PERSON>", "function-call-required": "Appel de fonction requis", "smart-edit-prompt": "É<PERSON> intelligente", "define-instructions-for-ai-to-edit-your-notes": "Vous pouvez utiliser des invites pour manipuler les notes, par exemple : si une note contient un lien, résumez le contenu du lien sous la note originale et générez une étiquette.", "rebuild-started": "La reconstruction a commencé", "rebuild-stopped-by-user": "Reconstruction arrêtée par l'utilisateur", "random-mode": "Marche aléatoire", "related-notes": "Notes associées", "no-related-notes-found": "Aucune note liée trouvée", "advanced": "<PERSON><PERSON><PERSON>", "rerank-model-description": "Spécifiez un modèle pour réorganiser les résultats de vecteur afin d'améliorer la précision de la recherche", "rerank-model": "Mod<PERSON>le de réévaluation", "rerank": "Réorganiser", "use-custom-rerank-endpoint-description": "Lorsq<PERSON>'elle est activée, les points de terminaison et les clés API du modèle intégré seront réorganisés.", "use-embedding-endpoint": "Utiliser le point de terminaison d'intégration", "rerank-score-description": "Définissez un seuil de score pour le modèle de réorganisation, en dessous duquel les résultats seront filtrés.", "public-share": "Partage Public", "internal-share": "Partage Interne", "no-team-members-found": "Aucun membre de l'équipe trouvé", "selected-users": "Utilisateurs sélectionnés", "tags-prompt": "<PERSON><PERSON> d'invite", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "Invitation utilisée pour la génération automatique de balises. Si elle est définie comme vide, l'invitation par défaut sera utilisée.", "generate-low-permission-token": "Générer un jeton à faibles permissions", "low-permission-token-desc": "Les jetons à faibles permissions ne peuvent accéder qu'au point de terminaison upsertNote et au point de terminaison du chat IA. Ils ne peuvent pas accéder à vos informations de compte ou à d'autres notes. Ceci est idéal pour des cas d'utilisation tels que les bots Telegram ou les bots WeChat, où vous souhaitez garantir qu'ils ne peuvent pas accéder à d'autres notes.", "this-token-is-only-displayed-once-please-save-it-properly": "Ce jeton n'est affiché qu'une seule fois, veuillez le sauvegarder correctement.", "refresh-model-list": "obtenir la liste des modèles", "please-set-the-embedding-model": "Veuillez définir le modèle intégré", "blinko-endpoint": "Point terminal Blinko", "enter-blinko-endpoint": "l'URL de votre déploiement Blinko", "login-failed": "Échec de connexion", "verification-failed": "Échec de l'authentification", "download-success": "Téléchargement réussi", "download-failed": "Échec du téléchargement", "downloading": "En téléchargement", "hide-pc-editor": "Masquer l'éditeur sur le PC.", "import-from-markdown": "Importer à partir d'un fichier Markdown", "import-from-markdown-tip": "Importer à partir d'un simple fichier .md ou d'une archive .zip contenant des fichiers .md", "not-a-markdown-or-zip-file": "Ce n'est pas un fichier Markdown ou zip. Veuillez sélectionner un fichier .md ou .zip.", "todo": "Procuration", "restore": "Rétablissement", "complete": "terminé", "today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er", "common.refreshing": "Actualisation en cours", "common.releaseToRefresh": "Lâchez pour actualiser", "common.pullToRefresh": "Tirer pour rafraîchir", "edit-message-warning": "Modifier ce message effacera tous les enregistrements de conversation ultérieurs et générera à nouveau une réponse IA.", "enter-your-message": "Entrez votre message", "set-deadline": "Définir la date limite", "expired": "Expiré", "expired-days": "<PERSON><PERSON><PERSON> de {{count}} jours", "expired-hours": "<PERSON><PERSON><PERSON> de {{count}} heures", "expired-minutes": "<PERSON><PERSON><PERSON> depuis {{count}} minutes", "days-left": "{{count}} jours plus tard", "hours-left": "{{count}} heures plus tard", "minutes-left": "{{count}} minutes après", "about-to-expire": "Sur le point d'expirer", "1-day": "1 jour", "1-week": "une semaine", "1-month": "Un mois", "quick-select": "Sélection rapide", "import-ai-configuration": "Importer la configuration AI", "would-you-like-to-import-this-configuration": "Voulez-vous importer cette configuration AI ?", "detected-ai-configuration-to-import": "Détection de la configuration IA à importer", "importing": "Importation en cours", "cache-cleared-successfully": "Le cache a été effacé avec succès ! La page se rechargera automatiquement.", "failed-to-clear-cache": "Échec du nettoyage du cache du navigateur. Veuillez essayer de rafraî<PERSON>r man<PERSON> (Ctrl+Shift+R).", "select-deployment": "Sélectionner le déploiement", "deployment-name": "Nom du déploiement", "please-set-the-api-endpoint": "Veuillez définir le point de terminaison de l'API", "please-set-the-api-key": "Veuillez définir la clé de l'API"}