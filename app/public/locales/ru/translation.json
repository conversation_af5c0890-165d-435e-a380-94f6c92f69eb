{"blinko": "Blinko", "notes": "Заметки", "resources": "Файлы", "archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "Настройки", "total-tags": "ОБЩИЕ ТЕГИ", "search": "Поиск...", "i-have-a-new-idea": "У меня есть новая идея...", "ask-about-your-notes": "Спросите о своих заметках", "backup-file": "РЕЗЕРВНАЯ КОПИЯ ФАЙЛА", "basic-information": "Основная информация", "cancel": "Отмена", "cancel-top": "Открепить", "change-user-info": "Изменить информацию о пользователе", "check-list": "Контрольный список", "confirm": "Подтвердите", "confirm-password": "Подтвердите пароль", "confirm-to-delete": "Подтвердите удаление!", "confirm-your-password": "Подтвердите свой пароль", "confrim": "Подтвердите", "convert-to-blinko": "Конвертировать в Blinko", "convert-to-note": "Конвертировать в заметку", "create-successfully": "Создано", "create-successfully-is-about-to-jump-to-the-login": "Успешно. Теперь можете авторизоваться", "delete": "Удалить", "delete-confirm": "Подтвердите удаление", "delete-success": "Успешное удаление", "detail": "Подробности", "enter-your-name": "Ва<PERSON> логин", "enter-your-username": "Введите ваше имя пользователя", "every-day": "Каждый день", "every-month": "Каждый месяц", "every-three-month": "Каждые три месяца", "every-week": "Каждую неделю", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "Привет {{имя}}! Я могу найти для вас заметки, как я могу помочь вам сегодня?", "import-from-bko": "Импорт из .bko", "import": "Импорт", "in-progress": "В процессе...", "insert-codeblock": "Вставка блока кода", "insert-hashtag": "Вставьте хэштег", "insert-sandpack": "Вставьте Sandpack", "insert-table": "Вставить таблицу", "items": " ", "keep-sign-in": "Сохранить", "language": "Язык", "last-run": "ПОСЛЕДНИЙ ЗАПУСК", "logout": "Выход из системы", "must-start-with-http-s-or-use-api-openai-as-default": "Должно начинаться с http(s):// или использовать /api/openai по умолчанию", "name-db": "ИМЯ", "need-to-create-an-account": "Вам нужно создать учетную запись?", "new-version-detected-click-to-get-the-latest-version": "🎉 Обнаружена новая версия, нажмите, чтобы получить последнюю версию", "nickname": "Ник", "no-tag-found": "Тег не найден", "not-a-bko-file": "не файл bko", "numbered-list": "Нумерованный список", "operation-failed": "Операция не удалась.", "password": "пароль", "original-password": "Текущий пароль", "preference": "Оформление", "recording": "Запись", "recovery": "Восстановление", "required-items-cannot-be-empty": "Требуемые элементы не могут быть пустыми", "rest-user-password": "Изменить пароль", "reviewed": "Рассмотрено", "running": "Запущен", "save": "Сохранить", "schedule": "РАСПИСАНИЕ", "schedule-archive-blinko": "Архивировать Blinko", "schedule-back-up": "Резервное копирование", "schedule-task": "Планировщик заданий", "show-less": "Показать меньше", "show-more": "Показать еще", "show-navigation-bar-on-mobile": "Скрыть панель навигации на мобильных устройствах", "sign-up": "Зарегистрироваться", "status": "СТАТУС", "stopped": "Остановлено", "the-two-passwords-are-inconsistent": "Два пароля несовместимы", "theme": "Тема", "there-are-no-resources-yet-go-upload-them-now": "Ресурсов еще нет, создайте их сейчас", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "Эта операция удилит текущую запись и не может быть восстановлена, пожалуйста, подтвердите", "this-operation-will-be-delete-resource-are-you-sure": "Эта операция будет удалена с ресурса, вы уверены?", "top": "Закрепить", "update-successfully": "Обновление прошло успешно", "upload-file": "Загрузить файл", "use-ai": "Включить AI", "user-custom-openai-api-key": "Пользовательский ключ OpenAI Api Key", "user-custom-azureopenai-api-instance": "Имя экземпляра Azure OpenAI", "user-custom-azureopenai-api-deployment": "Имя развертывания Azure OpenAI", "user-custom-azureopenai-api-version": "Версия API", "user-or-password-error": "Ошибка пользователя или пароля", "username": "имя пользователя", "your-changes-have-been-saved": "Ваши данные сохранены!", "add-tag": "Добавить тег", "ai-model": "Модель искусственного интеллекта", "all-notes-have-been-loaded": "Все заметки {{items}} были загружены", "already-have-an-account-direct-login": "У вас уже есть аккаунт? Прямой вход", "api-endpoint": "Конечная точка API", "archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bulleted-list": "Пунктуальный список", "change-type": "Тип изменения", "congratulations-youve-reviewed-everything-today": "вы сегодня все пересмотрели.", "convert-to": "Преобразовать в", "daily-review": "Ежедневный обзор", "edit": "Редактировать", "enter-send-shift-enter-for-new-line": "Enter для отправки, Shift+Enter для новой строки", "enter-your-password": "Введите пароль", "every-half-year": "Каждые полгода", "hello": "Здравствуйте", "multiple-select": "Множественный выбор", "name": "Имя", "no-data-here-well-then-time-to-write-a-note": "Похоже здесь ничего нет. Тогда пора написать заметку!", "note": "Примечание", "sign-in": "Войти", "total": "Всего", "model-provider": "Поставщик моделей", "created-in": "Создан в", "set-as-public": "Поделиться", "unset-as-public": "Закрыть доступ", "no-tag": "Без тега", "with-link": "Со ссылкой", "has-file": "Имеет файл", "created-at": "Создать в", "role": "Роль", "user-list": "Список пользователей", "create-user": "Создать пользователя", "action": "Действие", "edit-user": "Редактировать пользователя", "import-from-memos-memos_prod-db": "Импорт из Memos(memos_prod.db)", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "При экспорте memos_prod.db закройте контейнер memos, чтобы избежать частичной потери данных.", "go-to-share-page": "Перейдите на страницу обмена информацией", "import-done": "Импорт выполнен", "rebuild-embedding-index": "Восстановить индекс встраивания", "notes-imported-by-other-means-may-not-have-embedded-vectors": "Ноты, импортированные другими способами, могут не иметь встроенных векторов", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "Если у вас много заметок, вы можете израсходовать определенное количество жетонов.", "order-by-create-time": "Сортировка по дате", "time-format": "Формат времени", "version": "Версия", "new-version-available": "Доступна новая версия", "storage": "Хранение", "local-file-system": "Локальная файловая система", "object-storage": "Расположение", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "В дополнение к модели GPT необходимо обеспечить возможность вызова", "speech-recognition-requires-the-use-of": "Распознавание речи требует использования", "ai-expand": "AI Expand", "ai-polish": "AI Polish", "accept": "Принять", "reject": "Отклонить", "stop": "Стоп", "card-columns": "Колонки карты", "select-a-columns": "Выберите столбцы", "width-less-than-1024px": "<PERSON><PERSON><PERSON><PERSON>на менее 1024px", "width-less-than": "Ширина менее", "small-device-card-columns": "Колонки карты малого устройства", "medium-device-card-columns": "Колонки карты среднего устройства", "large-device-card-columns": "Большие колонки карты устройства", "device-card-columns": "Настройка вывода", "columns-for-different-devices": "Количество блоков на разных устройствах", "chars": "символов", "mobile": "Мобильный", "tablet": "План<PERSON>ет", "desktop": "Настольный компьютер", "text-fold-length": "Количество символов", "title-first-line-of-the-text": "Заголовок (первая строка текста)", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "Содержание (остальная часть текста, если текст длиннее указанной длины)", "ai-tag": "Метка AI", "article": "Статья", "embedding-model": "Модель встраивания", "force-rebuild": "Восстановление сил", "force-rebuild-embedding-index": "Принудительная перестройка полностью перестроит все данные, которые были проиндексированы.", "embedding-model-description": "Индекс должен быть перестроен после переключения встроенных моделей", "top-k-description": "Максимальное количество возвращаемых документов", "embedding-score-description": "Порогом сходства для запросов обычно является расстояние, равное евклидовой сумме", "embedding-lambda-description": "Весовой параметр разнообразия результатов запросов", "update-tag-icon": "Обновить значок тега", "delete-only-tag": "Удалить только тег", "delete-tag-with-note": "Удалить тег и данные", "update-tag-name": "Переименовать тег", "thinking": "Думать...", "select-all": "Выбрать все", "deselect-all": "Снять выделение", "insert-before": "Вставить перед", "insert-after": "Вставить после", "update-name": "Переименовать", "ai-emoji": "Ai Эмодзи ", "custom-icon": "Пользовательский значок", "ai-enhanced-search": "Поиск с улучшенным искусственным интеллектом", "preview-mode": "Режим предварительного просмотра", "source-code": "Исходный код", "camera": "Камера", "reference": "Ссылка", "reference-note": "Справочное пособие", "source-code-mode": "Режим исходного кода", "heading": "Заголовок", "paragraph": "Пар<PERSON><PERSON><PERSON><PERSON><PERSON>", "quote": "Цитата", "bold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remove-italic": "Удалить курсив", "underline": "Подчеркивание", "remove-bold": "Убрать жирный", "italic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remove-underline": "Удалить подчеркивание", "select-block-type": "Выберите тип блока", "block-type-select-placeholder": "Тип блока", "trash": "Корзина", "page-size": "Размер страницы", "toolbar-visibility": "Видимость панели инструментов", "always-hide-toolbar": "Всегда прячьтесь", "always-show-toolbar": "Всегда показывать", "hide-toolbar-on-mobile": "Скрыть на мобильном", "select-toolbar-visibility": "Выбор видимости панели инструментов", "select-a-time-format": "Выберите формат времени", "enter-code-shown-on-authenticator-app": "Введите код, показанный в приложении аутентификатора", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "Откройте стороннее приложение для аутентификации и введите код, показанный на экране.", "two-factor-authentication": "Двухфакторная аутентификация", "scan-this-qr-code-with-your-authenticator-app": "Отсканируйте этот QR-код с помощью приложения-аутентификатора.", "or-enter-this-code-manually": "Или введите этот код вручную:", "verify": "Проверьте", "about": "О сайте", "upload": "Загрузить", "days": "<PERSON><PERSON>и", "select-model": "Выберите модель", "select-model-provider": "Выберите поставщика модели", "allow-register": "Разрешить регистрацию", "access-token": "Токен доступа", "bucket": "Ведро", "region": "Регион", "access-key-secret": "Секрет ключа доступа", "access-key-id": "Идентификатор ключа доступа", "share-and-copy-link": "Поделиться и скопировать ссылку", "copy-share-link": "Скопируйте ссылку на общий доступ", "endpoint": "Конечная точка", "export-format": "Формат экспорта", "export": "Экспорт", "time-range": "Диапазон времени", "all": "Все", "exporting": "Экспорт...", "has-image": "Имеет изображение", "has-link": "Имеет ссылку", "filter-settings": "Настройки фильтра", "tag-status": "Статус тега", "all-notes": "Все заметки", "with-tags": "С тегами", "without-tags": "Без тегов", "select-tags": "Выберите теги", "additional-conditions": "Дополнительные условия", "apply-filter": "Применить фильтр", "to": "На", "start-date": "Дата начала", "end-date": "Дата окончания", "reset": "Сброс", "no-condition": "Нет условий", "public": "Общественность", "ai-model-tooltip": "Введите название модели, которое необходимо использовать, например gpt-3.5-turbo, gpt-4, gpt-4o, gpt-4o-mini", "user-custom-azureopenai-api-deployment-tooltip": " Введите имя развертывания, которое необходимо использовать, например gpt-4o", "ollama-ai-model-tooltip": "Введите имя модели, которое необходимо использовать, например llama3.2", "ollama-default-endpoint-is-http-localhost-11434": "Конечная точка Ollama по умолчанию - http://localhost:11434.", "your-azure-openai-instance-name": "Имя вашего экземпляра Azure OpenAI", "search-tags": "Поиск тегов", "insert-attachment-or-note": "Вставить во вложение или заметку?", "context": "Контекст", "paste-to-note-or-attachment": "Вы уверены, что хотите вставить в контекст или прикрепление?", "attachment": "Прикрепление", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "После удаления все данные пользователей будут очищены и не поддадутся восстановлению.", "upload-completed": "Загрузка завершена", "upload-cancelled": "Загрузка отменена", "upload-failed": "Не удалось загрузить", "import-from-bko-tip": "Загрузка в s3 для восстановления в настоящее время не поддерживается. Пожалуйста, временно отключите опцию s3, когда планируете выполнить восстановление.", "music-settings": "Настройки музыки", "enter-spotify-consumer-secret": "Введите секрет потребителя Spotify", "spotify-consumer-secret": "Spotify Секрет потребителя", "enter-spotify-consumer-key": "Введите ключ потребителя Spotify", "spotify-consumer-key-tip": "Использовались для получения обложек музыкальных альбомов в формате mp3.", "spotify-consumer-key-tip-2": "Получите ключ API на https://developer.spotify.com/", "edit-time": "Время редактирования", "ai-write": "Искусственный интеллект.", "download": "Скачать", "rename": "Переименовать", "move-up": "Двигаться вверх", "cut": "Порезать", "paste": "Вставить", "confirm-delete": "Подтвердить удаление", "confirm-delete-content": "Вы уверены, что хотите удалить {{name}}? Это действие нельзя отменить.", "folder-name": "Имя папки", "file-name": "Имя файла", "operation-success": "Операция успешно выполнена", "cloud-file": "Облачный файл", "move-to-parent": "Переместиться к родительскому элементу", "no-resources-found": "Ресурсы не найдены", "operation-in-progress": "Операция в процессе", "new-folder": "Новая папка", "folder-name-exists": "Имя папки существует", "folder-name-required": "Название папки обязательно", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "show-all": "Показать все", "sun": "Солнце", "mon": "Понедельник", "thu": "Чтв", "wed": "Обвенчались", "fri": "Пт.", "sat": "Суб.", "heatMapTitle": "Тепловая карта заметок за последний год", "heatMapDescription": "Показывает количество заметок, созданных за день", "select-month": "Выберите месяц", "note-count": "Заметка счета", "total-words": "Общее количество слов", "active-days": "Активные дни", "max-daily-words": "Максимальное количество слов в день", "analytics": "Аналитика", "tag-distribution": "Распределение тегов", "other-tags": "Другие теги", "tue": "Вт", "offline-status": "Офлайн режим", "offline-title": "Вы не в сети", "offline-description": "Пожалуйста, проверьте ваше интернет-соединение и попробуйте снова", "retry": "Повторить", "back-to-home": "Назад на главную", "offline": "О<PERSON><PERSON><PERSON><PERSON><PERSON>", "close-background-animation": "Закрыть фоновую анимацию", "custom-bg-tip": "Перейдите на https://www.shadergradient.co/, чтобы создать свой собственный градиентный фон.", "custom-background-url": "Пользовательский фон", "share": "Поделиться", "need-password-to-access": "Требуется пароль для доступа", "password-error": "Ошибка пароля", "cancel-share": "Отменить публикацию", "create-share": "Создать Совместно", "share-link": "Поделиться ссылкой", "set-access-password": "Установите пароль доступа", "protect-your-shared-content": "Защищайте ваш общий контент", "access-password": "Пароль доступа", "select-date": "Выберите дату", "expiry-time": "Время окончания", "select-expiry-time": "Выберите время истечения.", "permanent-valid": "Постоянно действительный", "7days-expiry": "Истекает через 7 дней", "custom-expiry": "Пользовательский срок действия", "30days-expiry": "Срок действия 30 дней", "share-link-expired": "Ссылка для обмена устарела", "share-link-expired-desc": "Эта доля истекла, пожалуйста, свяжитесь с администратором для повторного обмена.", "shared": "Общий", "internal-shared": "Внутренний общий доступ", "edited": "Измененный", "move-down": "Двигаться вниз", "provider-id": "Идентификатор поставщика", "provider-name": "Название поставщика", "well-known-url": "Известный URL", "authorization-url": "URL авторизации", "token-url": "Токен URL", "userinfo-url": "URL информации пользователя", "scope": "Область", "client-id": "Идентификатор клиента", "client-secret": "Секрет клиента", "sso-settings": "Настройки SSO", "oauth2-providers": "Поставщики Oauth2", "add-oauth2-provider": "Добавить поставщика Oauth2", "add-provider": "Добавить поставщика", "edit-oauth2-provider": "Редактировать Поставщик Oauth2", "confirm-delete-provider": "Подтвердите удаление поставщика", "please-select-icon-from-iconify": "Пожалуйста, выберите иконку из Iconify.", "provider-icon": "Иконка поставщика", "select-provider-template": "Выберите шаблон поставщика.", "provider-template": "Шаблон поставщика", "please-add-this-url-to-your-oauth-provider-settings": "Пожалуйста, добавьте этот URL в настройки вашего поставщика службы OAuth.", "redirect-url": "Перенаправить URL", "sign-in-with-provider": "Войти с помощью {{ provider }}", "community": "Сообщество", "theme-color": "Цвет темы", "link-account": "Связать аккаунт", "select-account": "Выберите учетную запись", "link-account-warning": "Обратите внимание, что если связать свои аккаунты, данные из текущего аккаунта не будут синхронизироваться с привязанным аккаунтом.", "unlink-account": "Отсоединить аккаунт", "unlink-account-tips": "Вы подтверждаете доступ ко всем ассоциациям с этой учетной записью?", "login-type": "Тип входа", "close-daily-review": "Ежедневный обзор.", "max-home-page-width": "Максимальная ширина домашней страницы", "max-home-page-width-tip": "Если установлено значение 0, это максимальная ширина.", "no-comments-yet": "Пока нет комментариев", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "from": "Из", "reply-to": "Ответить на", "comment": "Комментарий", "hub": "Центральный хаб", "home-site": "Домашняя страница", "use-blinko-hub": "Используйте Blinko Hub", "full-screen": "Полноэкранный", "exit-fullscreen": "Выйти из полноэкранного режима", "no-note-associated": "Нет примечания, связанного", "insert-context": "Вставить в контекст", "follow": "Следовать", "follower": "Последователь", "following": "Следующий", "admin": "Веб-мастер", "site-url": "Ссылка на сайт Blinko", "unfollow": "Отписаться", "join-hub": "Присоединиться к Hub", "refresh": "Обновить", "comment-notification": "Уведомление о комментарии", "follow-notification": "Уведомления о подписке", "followed-you": "последовал за тобой", "mark-all-as-read": "Отметить все как прочитанные", "no-notification": "Нет уведомления", "new-notification": "Новое уведомление", "notification": "Уведомление", "backup-success": "Резервное копирование выполнено успешно🎉", "system-notification": "Уведомление системы", "embedding-api-endpoint": "Встраивание конечной точки API", "embedding-api-key": "Встраивание API-ключа", "recommand": "Рекомендовать", "has-todo": "Есть ДЛЯ-СДЕЛКИ", "reference-by": "Ссылкой от", "hide-notification": "Скрыть уведомление", "search-settings": "Настройки поиска...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "Эта операция удалит выбранные файлы и не может быть восстановлена. Подтвердите, пожалуйста.", "plugin-settings": "Настройка плагина", "installed-plugins": "Установлено", "marketplace": "Торговая площадь", "local-development": "Местное развитие", "add-local-plugin": "Добавить локальный плагин", "local-plugin": "Локальный плагин", "uninstall": "Удалить", "install": "Установить", "downloads": "Загрузки", "plugin-updated": "Плагин обновлен", "plugin-update-failed": "Обновление плагина не удалось", "plugin-connection-failed": "Ошибка соединения с плагином", "disconnect": "Отключить", "local-development-description": "Добавьте локальный плагин разработки и отладите его.", "ai": "ИИ (искусственный интеллект)", "ai-chat-box-notes": "Ниже приведены соответствующие заметки, найденные для вас", "copy": "Копировать", "add-to-note": "Добавить в заметку", "add-to-blinko": "Добавить в Blinko", "no-title": "Без заголовка", "search-blinko-content-or-help-create": "Искать содержимое Blinko или помочь создать...", "conversation-history": "История беседы", "new-conversation": "Новый чат", "knowledge-base-search": "Поиск по базе знаний", "add-tools-to-model": "Искать в интернете или разрешить искусственному интеллекту вызвать API blinko.", "clear-current-content": "Очистить текущее содержимое", "welcome-to-blinko": "Добро пожаловать, {{name}}", "coding": "Программирование", "ai-prompt-writing": "Вы профессиональный писатель, пожалуйста, напишите профессиональную статью на тему, предоставленную пользователем.", "writing": "Письмо", "ai-prompt-translation": "Вы профессиональный переводчик, пожалуйста, переведите предоставленный пользователем текст на {{lang}}", "ai-prompt-coding": "Вы профессиональный программист, пожалуйста, напишите простую программу на Python по предоставленной пользователем теме.", "translation": "Перевод", "first-char-delay": "Задержка первого символа", "total-tokens": "Общее количество токенов", "check-connect": "Проверка", "check-connect-error": "Возможный сбой связи может быть добавлен в конец /v1", "check-connect-success": "Проверка успешного соединения", "loading": "Загрузка", "embedding-dimensions": "Встраиваемые размерности", "embedding-dimensions-description": "Вам необходимо убедиться, что размеры модели верны, и вам нужно принудительно перестроить индексные записи после изменений.", "model": "Модель", "ai-tools": "Инструменты Искусственного Интеллекта", "tavily-api-key": "Ключ API для поиска Tavily", "tavily-max-results": "Результа<PERSON><PERSON> Tavily Max", "ai-prompt-writing-content": "Напишите статью на 200 слов и сохраните ее в своих заметках.", "ai-prompt-coding-content": "Извлечение веб-содержимого https://github.com/blinko-space/blinko", "stop-task": "Остановить задачу", "processing": "Обработка", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "Идет задача по пересборке, хотите перезагрузиться?", "hide-blog-images": "Скрыть изображения блога", "ai-prompt-translation-content": "Проверьте заметки без тегов за последние два дня и отметьте их.", "ai-prompt-delete-content": "Найдите 2 архивированных заметках, суммируйте и сохраните их в виде новых заметок и удалите эти две архивные ноты", "older": "Старше", "newer": "Новее", "restore-this-version": "Восстановите эту версию", "Note History": "Примечание История", "View History Versions": "Просмотреть исторические версии", "history-note-only": "Внимание: эта история содержит только текстовое содержимое, а не историю файлов", "referenceResource": "Справочный ресурс", "to-ask-ai": "Чтобы спросить ИИ", "press-enter-to-select-first-result": "Нажмите Enter, чтобы выбрать первый результат", "ask-ai": "Спросите AI", "ask-blinko-ai-about-this-query": "Спросите Blinko AI об этом запросе", "search-or-ask-ai": "Поиск примечания, настройки или спросите AI ...", "plugin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editor-preview": "редактор", "both": "Оба", "auto-add-tags": "Автоматическое добавление тегов", "add-as-comment": "Добавить как комментарий", "choose-what-to-do-with-ai-results": "Выберите, что делать с результатами ИИ", "ai-post-processing-mode": "Режим обработки пост -обработки ИИ", "ai-post-processing-prompt": "Комментарий к заданию постобработки ИИ", "2fa-setup-successful": "2FA установка успешно", "ai-generate-emoji": "", "ai-generating-emoji": "", "align-center": "Центр", "align-left": "Левый", "align-right": "Верно", "alternate-text": "Альтернативный текст", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "Проанализируйте следующее содержимое ноты и предложите соответствующие теги и предоставьте краткое изложение", "api-key": "", "check": "Список задач", "close": "Закрывать", "code": "Кодовый блок", "code-theme": "Предварительный просмотр темы кода", "column": "Столбец", "content-generated-by-ai": "Контент, генерируемый ИИ", "content-theme": "Предварительный просмотр темы контента", "copied": "Скопированный", "custom-path": "Пользовательский путь", "dark-mode": "Темный режим", "date-range": "", "days-ago": "", "define-custom-prompt-for-ai-to-process-notes": "Управляйте ИИ для комментирования текущей заметки. Например: Пожал<PERSON>йста, кратко изложите содержание заметки. Если содержание заметки составляет менее 10 слов, пожалуйста, отредактируйте его для меня.", "delete-column": "Удалить ряд", "delete-row": "Удалить столбец", "devtools": "Devtools", "down": "<PERSON><PERSON><PERSON><PERSON>", "download-tip": "Браузер не поддерживает функцию загрузки", "edit-mode": "Перевернуть режим редактирования", "emoji": "Эмодзи", "enter-custom-prompt-for-post-processing": "Введите пользовательскую подсказку для пост -обработки", "enter-your-api-key": "", "exclude-tag-from-embedding": "Исключить тегированный контент", "exclude-tag-from-embedding-desc": "Выберите тег, чтобы исключить связанные с ними заметки из генерации вектора AI Enceding Vector", "exclude-tag-from-embedding-tip": "Примечания с этим тегом будет исключено из обработки встраивания искусственного интеллекта", "file-type-error": "Тип файла - ошибка", "follow-system": "Следуйте системе", "footnote-ref": "Сноска ref", "fullscreen": "Переверните полноэкран", "generate": "Генерирование", "heading1": "Заголовок 1", "heading2": "Заголовок 2", "heading3": "Заголовок 3", "heading4": "Заголовок 4", "heading5": "Заголовок 5", "heading6": "Заголовок 6", "headings": "Заголовки", "help": "Помощь", "hours-ago": "", "image-url": "изображение URL", "impoort-from-bko": "", "indent": "Отступать", "info": "Информация", "inline-code": "Встроенный код", "insert-column-left": "Вставьте 1 слева", "insert-column-right": "Вставьте 1 справа", "insert-row-above": "Вставьте 1 выше", "insert-row-below": "Вставьте 1 ниже", "instant-rendering": "Мгновенный рендеринг", "light-mode": "Легкий режим", "line": "Линия", "link": "Связь", "link-ref": "Ссылка ref", "list": "Список", "minutes-ago": "", "months-ago": "", "more": "Более", "name-empty": "Имя пусто", "ordered-list": "Список заказов", "outdent": "Превзойти", "outline": "<PERSON>он<PERSON><PERSON><PERSON>", "over": "над", "performance-tip": "Предварительный просмотр в реальном времени требует $ {x} MS, вы можете закрыть его", "preview": "Предварительный просмотр", "prompt-used-for-post-processing-notes": "Запрос используется для примечаний по пост -обработке", "rebuild": "Восстановить", "rebuild-in-progress": "Восстановить в процессе", "rebuilding-embedding-progress": "Восстановление прогресса внедрения", "record": "Начальная запись/конечная запись", "record-tip": "Устройство не поддерживает запись", "redo": "Переде<PERSON>ан", "remove": "Удалять", "rest-user-info": "REST USER INFO", "row": "<PERSON>яд", "setting": "", "spin": "Вращаться", "split-view": "Расколотый вид", "spotify-consumer-key": "Spotify API -ключ", "strike": "Ударять", "superadmin": "", "table": "Стол", "text-is-not-empty": "Текст (нет пусто)", "title": "Заголовок", "tooltip-text": "Текст подъема инструментов", "undo": "Отменить", "up": "Ввер<PERSON>", "update": "Обновлять", "updated-at": "Обновление в", "upload-error": "ошибка загрузки", "uploading": "Загрузка ...", "user": "", "weeks-ago": "", "wysiwyg": "Wysiwyg", "years-ago": "", "to-search-tags": "Искать теги", "app-upgrade-required": "Требуется обновление приложения", "current-app-version": "Текущая версия приложения", "required-app-version": "Требуемая версия приложения", "upgrade": "Обновление", "online-search": "Онлайн поиск", "smart-edit": "Умное Редактирование", "function-call-required": "Требуется вызов функции", "smart-edit-prompt": "Умный Редактор Подсказок", "define-instructions-for-ai-to-edit-your-notes": "Вы можете использовать подсказки для управления записями, например: Если запись содержит ссылку, кратко изложите содержание ссылки ниже исходной записи и сгенерируйте метку.", "rebuild-started": "Начато Восстановление", "rebuild-stopped-by-user": "Восстановление остановлено пользователем", "random-mode": "Случайное блуждание", "related-notes": "Связанные заметки", "no-related-notes-found": "Не найдено связанных заметок", "advanced": "Продвинутый", "rerank-model-description": "Укажите модель для переупорядочивания векторных результатов с целью повышения точности поиска", "rerank-model": "Модель переранжирования", "rerank": "Перер<PERSON><PERSON><PERSON><PERSON><PERSON>овать", "use-custom-rerank-endpoint-description": "Когда включено, конечные точки и ключи API встроенной модели будут переупорядочены.", "use-embedding-endpoint": "Используйте конечную точку встраивания", "rerank-score-description": "Установите пороговое значение для модели переупорядочивания, ниже которого результаты будут отфильтрованы.", "public-share": "Публичная акция", "internal-share": "Внутренняя доля", "no-team-members-found": "Не найдено участников команды", "selected-users": "Выбранные пользователи", "tags-prompt": "Теги Подсказка", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "Текст, используемый для автоматической генерации тегов. Если оставить пустым, будет использован текст по умолчанию.", "generate-low-permission-token": "Сгенерировать токен с низкими правами доступа", "low-permission-token-desc": "Токены с низкими правами доступа могут получить доступ только к конечной точке upsertNote и конечной точке AI chat. Они не могут получить доступ к информации о вашем аккаунте или другим заметкам. Это идеально подходит для таких случаев использования, как боты Telegram или боты WeChat, где вы хотите гарантировать, что они не смогут получить доступ к другим заметкам.", "this-token-is-only-displayed-once-please-save-it-properly": "Этот токен отображается только один раз, сохраните его правильно", "refresh-model-list": "Получить список моделей", "please-set-the-embedding-model": "Пожалуйста, установите встроенную модель.", "blinko-endpoint": "Blinko конечная точка", "enter-blinko-endpoint": "URL вашего развертывания Blinko", "login-failed": "Ошибка входа", "verification-failed": "Аутентификация не удалась", "download-success": "Успешно загружено", "download-failed": "Загрузка не удалась", "hide-pc-editor": "скрыть редактор для ПК.", "import-from-markdown": "从Markdown文件导入 -> Импорт из файла Markdown", "not-a-markdown-or-zip-file": "这不是Markdown或zip文件。请选择.md或.zip文件。\n\nЭто не файл Markdown или zip. Пожалуйста, выберите файл .md или .zip.", "todo": "Представительство", "restore": "恢复 - восстановление", "complete": "完成 - Завершить", "today": "сегодня", "yesterday": "Вчера", "common.refreshing": "Обновление", "common.releaseToRefresh": "Отпустите для обновления", "common.pullToRefresh": "Обновить, потянув вниз", "edit-message-warning": "Редактирование этого сообщения удалит все последующие записи разговора и снова сгенерирует ответ AI.", "enter-your-message": "Введите свое сообщение", "set-deadline": "设置截止日期 -> Установить крайний срок", "expired": "已过期 -> Истёк срок действия", "expired-days": "истекло {{count}} дней", "expired-hours": "Просрочено {{count}} часов.", "expired-minutes": "Просрочено {{count}} минут", "days-left": "{{count}} дней спустя", "hours-left": "{{count}} часов спустя", "minutes-left": "{{count}} минут спустя", "about-to-expire": "Скоро истекает срок.", "1-day": "1 день", "1-week": "一周 - неделя", "1-month": "один месяц", "quick-select": "Быстрый выбор", "import-ai-configuration": "Импорт настроек AI", "would-you-like-to-import-this-configuration": "Вы хотите импортировать эту конфигурацию AI?", "detected-ai-configuration-to-import": "Обнаружена конфигурация AI для импорта", "importing": "Импорт", "cache-cleared-successfully": "Кэш успешно очищен! Страница будет автоматически перезагружена.", "failed-to-clear-cache": "Не удалось очистить кэш браузера. Пожалуйста, попробуйте обновить вручную (Ctrl+Shift+R).", "select-deployment": "Выберите развертывание", "deployment-name": "Имя развертывания", "please-set-the-api-endpoint": "Пожалуйста, укажите конечную точку API", "please-set-the-api-key": "Пожалуйста, укажите ключ API"}