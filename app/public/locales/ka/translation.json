{"hello": "გამარჯობა", "blinko": "ბლინკო", "notes": "ჩანაწერები", "resources": "რესურსები", "archived": "დაარქივებული", "settings": "პარამეტრები", "total-tags": "ჯამური თეგები", "search": "ძებნა...", "i-have-a-new-idea": "მაქვს ახალი იდეა...", "note": "ჩანაწერი", "multiple-select": "მრავლობითი არჩევანი", "convert-to": "გარდაქმნა", "delete": "წაშლა", "recovery": "აღდგენა", "archive": "არქივი", "items": "ელემენტები", "your-changes-have-been-saved": "თქვენი ცვლილებები შენახულია!", "operation-failed": "ოპერაცია ვერ შესრულდა.", "in-progress": "მიმდინარეობს...", "confirm-to-delete": "დაადასტურეთ წაშლა!", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "ეს ოპერაცია შლის დაკავშირებულ ეტიკეტს და ვერ აღდგება, გთხოვთ დაადასტუროთ", "add-tag": "თეგის დამატება", "cancel": "გაუქმება", "no-data-here-well-then-time-to-write-a-note": "აქ მონაცემები არ არის~", "basic-information": "ძირითადი ინფორმაცია", "name": "სახელი", "preference": "პრეფერენცია", "theme": "თემა", "change-type": "ტიპის შეცვლა", "insert-hashtag": "ჰეშთეგის ჩასმა", "bulleted-list": "მარკირებული სია", "numbered-list": "დანომრილი სია", "check-list": "შემოწმების სია", "insert-table": "ცხრილის ჩასმა", "insert-codeblock": "კოდის ბლოკის ჩასმა", "insert-sandpack": "სენდპეკის ჩასმა", "upload-file": "ფაილის ატვირთვა", "delete-confirm": "წაშლის დადასტურება", "this-operation-will-be-delete-resource-are-you-sure": "ეს ოპერაცია წაშლის რესურსს, დარწმუნებული ხართ?", "delete-success": "წარმატებით წაიშალა", "update-successfully": "წარმატებით განახლდა", "create-successfully": "წარმატებით შეიქმნა", "total": "ჯამი", "all-notes-have-been-loaded": "ყველა {{items}} ჩანაწერი ჩაიტვირთა", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "გამარჯობა {{name}}!, შემიძლია მოვძებნო ჩანაწერები თქვენთვის, როგორ შემიძლია დაგეხმაროთ დღეს?", "ask-about-your-notes": "შეკითხვა თქვენს ჩანაწერებზე", "use-ai": "AI-ს გამოყენება", "model-provider": "მოდელის მომწოდებელი", "api-endpoint": "API წერტილი", "must-start-with-http-s-or-use-api-openai-as-default": "უნდა დაიწყოს http(s):// ან გამოიყენოს /api/openai როგორც ნაგულისხმევი", "user-custom-openai-api-key": "მომხმარებლის OpenAI Api გასაღები", "user-custom-azureopenai-api-instance": "Azure OpenAI ეგზემპლარის სახელი", "user-custom-azureopenai-api-deployment": "Azure OpenAI გაშლის სახელი", "user-custom-azureopenai-api-version": "API ვერსია", "ai-model": "AI მოდელი", "logout": "გასვლა", "user-or-password-error": "მომხმარებლის ან პაროლის შეცდომა", "username": "მომხმარებლის სახელი", "enter-your-name": "შეიყვანეთ თქვენი სახელი", "password": "პაროლი", "enter-your-password": "შეიყვანეთ თქვენი პაროლი", "need-to-create-an-account": "გჭირდებათ ანგარიშის შექმნა?", "sign-up": "რეგისტრაცია", "sign-in": "შესვლა", "nickname": "მეტსახელი", "change-user-info": "მომხმარებლის ინფორმაციის შეცვლა", "rest-user-password": "პაროლის აღდგენა", "confirm-password": "პაროლის დადასტურება", "confirm-your-password": "დაადასტურეთ თქვენი პაროლი", "enter-your-username": "შეიყვანეთ თქვენი მომხმარებლის სახელი", "save": "შენახვა", "keep-sign-in": "შესვლის შენარჩუნება", "required-items-cannot-be-empty": "სავალდებულო ველები არ შეიძლება იყოს ცარიელი", "the-two-passwords-are-inconsistent": "ორი პაროლი არ ემთხვევა", "create-successfully-is-about-to-jump-to-the-login": "წარმატებით შეიქმნა, გადასვლა შესვლის გვერდზე", "already-have-an-account-direct-login": "უკვე გაქვთ ანგარიში? პირდაპირი შესვლა", "no-tag-found": "თეგი ვერ მოიძებნა", "new-version-detected-click-to-get-the-latest-version": "🎉აღმოჩენილია ახალი ვერსია, დააწკაპუნეთ უახლესი ვერსიის მისაღებად", "schedule-task": "დაგეგმილი დავალება", "schedule-back-up": "დაგეგმილი სარეზერვო ასლი", "every-day": "ყოველ დღე", "every-week": "ყოველ კვირა", "every-month": "ყოველ თვე", "every-three-month": "ყოველ სამ თვეში", "every-half-year": "ყოველ ნახევარ წელიწადში", "import": "იმპორტი", "import-from-bko": ".bko-დან იმპორტი", "not-a-bko-file": "არ არის bko ფაილი", "convert-to-note": "ჩანაწერად გარდაქმნა", "convert-to-blinko": "ბლინკოდ გარდაქმნა", "reviewed": "განხილული", "congratulations-youve-reviewed-everything-today": "გილოცავთ, დღეს ყველაფერი განიხილეთ.", "name-db": "სახელი", "schedule": "განრიგი", "last-run": "ბოლო გაშვება", "backup-file": "სარეზერვო ფაილი", "status": "სტატუსი", "running": "მიმდინარე", "stopped": "შეჩერებული", "show-navigation-bar-on-mobile": "ნავიგაციის ზოლის დამალვა მობილურზე", "schedule-archive-blinko": "ბლინკოს დაგეგმილი არქივი", "there-are-no-resources-yet-go-upload-them-now": "ჯერ რესურსები არ არის, ატვირთეთ ახლა", "confrim": "დადასტურება", "daily-review": "ყოველდღიური მიმოხილვა", "detail": "დეტალები", "enter-send-shift-enter-for-new-line": "Enter გაგზავნა, Shift+Enter ახალი ხაზისთვის", "show-less": "ნაკლების ჩვენება", "show-more": "მეტის ჩვენება", "top": "თავში", "cancel-top": "თავში გაუქმება", "created-in": "შექმნილია", "set-as-public": "საჯაროდ დაყენება", "unset-as-public": "საჯაროდ გაუქმება", "with-link": "ბმულით", "no-tag": "თეგის გარეშე", "has-file": "აქვს ფაილი", "created-at": "შექმნის თარიღი", "updated-at": "განახლების თარიღი", "role": "როლი", "user-list": "მომხმარებელთა სია", "create-user": "მომხმარებლის შექმნა", "action": "მოქმედება", "original-password": "ორიგინალი პაროლი", "edit-user": "მომხმარებლის რედაქტირება", "import-from-memos-memos_prod-db": "Memos-დან იმპორტი (memos_prod.db)", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "memos_prod.db-ის ექსპორტისას, გთხოვთ დახუროთ memos კონტეინერი მონაცემების ნაწილობრივი დაკარგვის თავიდან ასაცილებლად.", "go-to-share-page": "გაზიარების გვერდზე გადასვლა", "import-done": "იმპორტი დასრულებულია", "rebuilding-embedding-progress": "ჩაშენების პროგრესის აღდგენა", "rebuild-embedding-index": "ჩაშენების ინდექსის აღდგენა", "rebuild": "აღდგენა", "notes-imported-by-other-means-may-not-have-embedded-vectors": "სხვა საშუალებებით იმპორტირებულ ჩანაწერებს შეიძლება არ ჰქონდეთ ჩაშენებული ვექტორები", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "თუ ბევრი ჩანაწერი გაქვთ, შეიძლება გარკვეული რაოდენობის ტოკენები დახარჯოთ", "order-by-create-time": "შექმნის დროის მიხედვით დალაგება", "time-format": "დროის ფორმატი", "version": "ვერსია", "new-version-available": "ხელმისაწვდომია ახალი ვერსია", "storage": "საცავი", "local-file-system": "ლოკალური ფაილური სისტემა", "object-storage": "ობიექტების საცავი", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "GPT მოდელის გარდა, საჭიროა იმის უზრუნველყოფა, რომ შესაძლებელია გამოძახება", "speech-recognition-requires-the-use-of": "მეტყველების ამოცნობას სჭირდება გამოყენება", "ai-expand": "AI გაფართოება", "ai-polish": "AI გაპრიალება", "accept": "მიღება", "reject": "უარყოფა", "stop": "შეჩერება", "card-columns": "ბარათის სვეტები", "select-a-columns": "აირჩიეთ სვეტები", "width-less-than-1024px": "სიგანე ნაკლებია 1024px-ზე", "width-less-than": "სიგანე ნაკლებია", "small-device-card-columns": "პატარა მოწყობილობის ბარათის სვეტები", "medium-device-card-columns": "საშუალო მოწყობილობის ბარათის სვეტები", "large-device-card-columns": "დიდი მოწყობილობის ბარათის სვეტები", "device-card-columns": "მოწყობილობის ბარათის სვეტები", "enter-custom-prompt-for-post-processing": "შეიყვანეთ პერსონალური მოთხოვნა პოსტის დამუშავებისთვის", "columns-for-different-devices": "სვეტები სხვადასხვა მოწყობილობებისთვის", "mobile": "მობილური", "tablet": "ტაბლეტი", "desktop": "კომპიუტერი", "chars": "სიმბოლოები", "text-fold-length": "ტექსტის დაკეცვის სიგრძე", "title-first-line-of-the-text": "სათაური (ტექსტის პირველი ხაზი)", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "შინაარსი (დანარჩენი ტექსტი, თუ ტექსტი უფრო გრძელია ვიდრე სიგრძე)", "ai-tag": "AI თეგი", "article": "სტატია", "embedding-model": "ჩაშენების მოდელი", "force-rebuild": "ძალით აღდგენა", "force-rebuild-embedding-index": "ძალით აღდგენა სრულად აღადგენს ყველა ინდექსირებულ მონაცემს", "embedding-model-description": "ინდექსი უნდა აღდგეს ჩაშენებული მოდელების გადართვის შემდეგ", "top-k-description": "საბოლოოდ დაბრუნებული დოკუმენტების მაქსიმალური რაოდენობა", "embedding-score-description": "მსგავსების ზღვარი მოთხოვნებისთვის ჩვეულებრივ არის ევკლიდური ჯამის მანძილი", "embedding-lambda-description": "მოთხოვნის შედეგის მრავალფეროვნების წონის პარამეტრი", "update-tag-icon": "თეგის ხატულას განახლება", "delete-only-tag": "მხოლოდ თეგის წაშლა", "delete-tag-with-note": "თეგის წაშლა ჩანაწერთან ერთად", "update-name": "სახელის განახლება", "update-tag-name": "თეგის სახელის განახლება", "thinking": "ფიქრობს...", "select-all": "ყველას არჩევა", "deselect-all": "ყველას გაუქმება", "ai-emoji": "AI ემოჯი", "custom-icon": "მორგებული ხატულა", "ai-enhanced-search": "AI გაძლიერებული ძიება", "preview-mode": "გადახედვის რეჟიმი", "source-code": "საწყისი კოდი", "camera": "კამერა", "reference": "მითითება", "reference-note": "მითითების ჩანაწერი", "source-code-mode": "საწყისი კოდის რეჟიმი", "heading": "სათაური", "paragraph": "პარაგრაფი", "remove-bold": "მუქის მოხსნა", "remove-italic": "დახრილის მოხსნა", "underline": "ხაზგასმა", "remove-underline": "ხაზგასმის მოხსნა", "select-block-type": "ბლოკის ტიპის არჩევა", "block-type-select-placeholder": "ბლოკის ტიპი", "trash": "სანაგვე ყუთი", "light-mode": "ნათელი რეჟიმი", "dark-mode": "მუქი რეჟიმი", "follow-system": "სისტემის მიყოლა", "custom-path": "მორგებული გზა", "page-size": "გვერდის ზომა", "toolbar-visibility": "ხელსაწყოთა ზოლის ხილვადობა", "select-toolbar-visibility": "აირჩიეთ ხელსაწყოთა ზოლის ხილვადობა", "always-show-toolbar": "ყოველთვის ჩვენება", "hide-toolbar-on-mobile": "მობილურზე დამალვა", "always-hide-toolbar": "ყოველთვის დამალვა", "select-a-time-format": "აირჩიეთ დროის ფორმატი", "enter-code-shown-on-authenticator-app": "შეიყვანეთ კოდი, რომელიც ნაჩვენებია აუტენტიფიკაციის აპში", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "გახსენით თქვენი მესამე მხარის აუტენტიფიკაციის აპი და შეიყვანეთ ეკრანზე ნაჩვენები კოდი", "two-factor-authentication": "ორფაქტორიანი აუტენტიფიკაცია", "scan-this-qr-code-with-your-authenticator-app": "დაასკანერეთ ეს QR კოდი თქვენი აუტენტიფიკაციის აპით", "or-enter-this-code-manually": "ან შეიყვანეთ ეს კოდი ხელით:", "verify": "გადამოწმება", "2fa-setup-successful": "2FA წარმატებით დაყენდა", "about": "შესახებ", "days": "დღეები", "select-model-provider": "აირჩიეთ მოდელის მომწოდებელი", "select-model": "აირჩიეთ მოდელი", "allow-register": "რეგისტრაციის დაშვება", "access-token": "წვდომის ტოკენი", "bucket": "ბაკეტი", "region": "რეგიონი", "access-key-secret": "წვდომის გასაღების საიდუმლო", "access-key-id": "წვდომის გასაღების ID", "copy-share-link": "გაზიარების ბმულის კოპირება", "share-and-copy-link": "გაზიარება და ბმულის კოპირება", "endpoint": "საბოლოო წერტილი", "export-format": "ექსპორტის ფორმატი", "time-range": "დროის შუალედი", "all": "ყველა", "exporting": "ექსპორტირება...", "tag-status": "თეგის სტატუსი", "all-notes": "ყველა ჩანაწერი", "with-tags": "თეგებით", "without-tags": "თეგების გარეშე", "select-tags": "თეგების არჩევა", "additional-conditions": "დამატებითი პირობები", "apply-filter": "ფილტრის გამოყენება", "has-image": "აქვს სურათი", "has-link": "აქვს ბმული", "filter-settings": "ფილტრის პარამეტრები", "to": "მდე", "reset": "გადატვირთვა", "start-date": "დაწყების თარიღი", "end-date": "დასრულების თარიღი", "no-condition": "პირობის გარეშე", "public": "საჯარო", "exclude-tag-from-embedding": "თეგირებული შინაარსის გამორიცხვა", "exclude-tag-from-embedding-tip": "ჩანაწერები ამ თეგით გამოირიცხება AI ჩაშენების დამუშავებიდან", "exclude-tag-from-embedding-desc": "აირჩიეთ თეგი მასთან დაკავშირებული ჩანაწერების AI ჩაშენების ვექტორის გენერაციიდან გამოსარიცხად", "ai-model-tooltip": "შეიყვანეთ გამოსაყენებელი მოდელის სახელი, მაგალითად gpt-3.5-turbo, gpt-4, gpt-4o, gpt-4o-mini", "user-custom-azureopenai-api-deployment-tooltip": "შეიყვანეთ გამოსაყენებელი გაშლის სახელი, მაგალითად gpt-4o", "ollama-ai-model-tooltip": "შეიყვანეთ გამოსაყენებელი მოდელის სახელი, მაგალითად llama3.2", "ollama-default-endpoint-is-http-localhost-11434": "Ollama-ს ნაგულისხმევი საბოლოო წერტილია http://localhost:11434", "your-azure-openai-instance-name": "თქვენი Azure OpenAI ეგზემპლარის სახელი", "align-center": "ცენტრში", "align-left": "მარცხნივ", "align-right": "მარჯვნივ", "alternate-text": "ალტერნატიული ტექსტი", "bold": "მუქი", "both": "ორივე", "check": "დავალებების სია", "close": "დახურვა", "code": "კოდის ბლოკი", "code-theme": "კოდის ბლოკის თემის წინასწარი ჩვენება", "column": "სვეტი", "comment": "კომენტარი", "confirm": "დადასტურება", "content-theme": "შინაარსის თემის წინასწარი ჩვენება", "copied": "დაკოპირებულია", "copy": "კოპირება", "delete-column": "რიგის წაშლა", "delete-row": "სვეტის წაშლა", "devtools": "დეველოპერის ხელსაწყოები", "down": "ქვემოთ", "download-tip": "ბრაუზერს არ აქვს ჩამოტვირთვის ფუნქციის მხარდაჭერა", "edit": "რედაქტირება", "edit-mode": "რედაქტირების რეჟიმის გადართვა", "emoji": "ემოჯი", "export": "ექსპორტი", "file-type-error": "ფაილის ტიპის შეცდომა", "footnote-ref": "სქოლიოს მითითება", "fullscreen": "სრულ ეკრანზე გადართვა", "generate": "გენერირება", "headings": "სათაურები", "heading1": "სათაური 1", "heading2": "სათაური 2", "heading3": "სათაური 3", "heading4": "სათაური 4", "heading5": "სათაური 5", "heading6": "სათაური 6", "help": "დახმარება", "image-url": "სურათის URL", "indent": "აბზაცი", "info": "ინფორმაცია", "inline-code": "ჩაშენებული კოდი", "insert-after": "ხაზის ჩასმა შემდეგ", "insert-before": "ხაზის ჩასმა წინ", "insert-column-left": "სვეტის ჩამატება მარცხნივ", "insert-column-right": "სვეტის ჩამატება მარჯვნივ", "insert-row-above": "მწკრივის ჩამატება ზემოთ", "insert-row-below": "მწკრივის ჩამატება ქვემოთ", "instant-rendering": "მყისიერი რენდერი", "italic": "დახრილი", "language": "ენა", "line": "ხაზი", "link": "ბმული", "link-ref": "ბმულის მითითება", "list": "სია", "more": "მეტი", "name-empty": "სახელი ცარიელია", "ordered-list": "დანომრილი სია", "outdent": "აბზაცის შემცირება", "outline": "სტრუქტურა", "over": "ზემოთ", "performance-tip": "რეალურ დროში გადახედვას სჭირდება ${x}მწმ, შეგიძლიათ გამორთოთ", "preview": "გადახედვა", "quote": "ციტატა", "record": "ჩაწერის დაწყება/დასრულება", "record-tip": "მოწყობილობა არ უჭერს მხარს ჩაწერას", "recording": "მიმდინარეობს ჩაწერა...", "redo": "გამეორება", "remove": "წაშლა", "row": "მწკრივი", "spin": "ბრუნვა", "split-view": "გაყოფილი ხედი", "strike": "გადახაზული", "table": "ცხრილი", "text-is-not-empty": "ტექსტი (არ არის ცარიელი)", "title": "სათაური", "tooltip-text": "მინიშნების ტექსტი", "undo": "გაუქმება", "up": "ზევით", "update": "განახლება", "upload": "სურათის ან ფაილის ატვირთვა", "upload-error": "ატვირთვის შეცდომა", "uploading": "მიმდინარეობს ატვირთვა...", "wysiwyg": "WYSIWYG რედაქტორი", "search-tags": "ტეგების ძიება", "insert-attachment-or-note": "დამატება მიმაგრებულ ფაილებში თუ ჩანაწერში?", "paste-to-note-or-attachment": "დარწმუნებული ხართ, რომ გსურთ კონტექსტში ან მიმაგრებულ ფაილში ჩასმა?", "context": "კონტექსტი", "attachment": "მიმაგრებული ფაილი", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "წაშლის შემდეგ, ყველა მომხმარებლის მონაცემი წაიშლება და აღდგენა შეუძლებელი იქნება.", "upload-completed": "ატვირთვა დასრულდა", "upload-cancelled": "ატვირთვა გაუქმდა", "upload-failed": "ატვირთვა ვერ მოხერხდა", "import-from-bko-tip": "s3-ზე ატვირთვა აღდგენისთვის ამჟამად არ არის მხარდაჭერილი. გთხოვთ დროებით გამორთოთ s3 ოფცია აღდგენის დროს.", "music-settings": "მუსიკის პარამეტრები", "spotify-consumer-key": "Spotify API გასაღები", "spotify-consumer-secret": "Spotify API საიდუმლო", "enter-spotify-consumer-key": "შეიყვანეთ Spotify API გასაღები", "enter-spotify-consumer-secret": "შეიყვანეთ Spotify მომხმარებლის საიდუმლო", "spotify-consumer-key-tip": "გამოიყენება mp3 მუსიკის ყდის მისაღებად", "spotify-consumer-key-tip-2": "მიიღეთ API გასაღები https://developer.spotify.com/-დან", "edit-time": "რედაქტირების დრო", "ai-write": "AI წერა", "download": "ჩამოტვირთვა", "rename": "სახელის შეცვლა", "move-up": "ზევით გადატანა", "cut": "ამოჭრა", "paste": "ჩასმა", "confirm-delete-content": "დარწმუნებული ხართ, რომ გსურთ {{name}}-ის წაშლა? ეს მოქმედება ვერ გაუქმდება.", "confirm-delete": "წაშლის დადასტურება", "folder-name": "საქაღალდის სახელი", "file-name": "ფაილის სახელი", "operation-success": "ოპერაცია წარმატებით შესრულდა", "cloud-file": "ღრუბლოვანი ფაილი", "move-to-parent": "მშობელ საქაღალდეში გადატანა", "no-resources-found": "რესურსები ვერ მოიძებნა", "operation-in-progress": "ოპერაცია მიმდინარეობს", "new-folder": "ახალი საქაღალდე", "folder-name-required": "საჭიროა საქაღალდის სახელი", "folder-name-exists": "საქაღალდის სახელი უკვე არსებობს", "show-all": "ყველას ჩვენება", "collapse": "ჩაკეცვა", "sun": "კვი", "mon": "ორშ", "tue": "სამ", "thu": "ხუთ", "fri": "პარ", "sat": "შაბ", "wed": "ოთხ", "heatMapTitle": "ჩანაწერების სიხშირის რუკა გასული წლიდან", "heatMapDescription": "აჩვენებს დღეში შექმნილი ჩანაწერების რაოდენობას", "select-month": "აირჩიეთ თვე", "note-count": "ჩანაწერების რაოდენობა", "total-words": "სიტყვების ჯამი", "max-daily-words": "მაქსიმალური დღიური სიტყვები", "active-days": "აქტიური დღეები", "analytics": "ანალიტიკა", "tag-distribution": "ტეგების განაწილება", "other-tags": "სხვა ტეგები", "offline-status": "ოფლაინ რეჟიმი", "offline-title": "თქვენ ხართ ოფლაინ", "offline-description": "გთხოვთ შეამოწმოთ ინტერნეტ კავშირი და სცადოთ ხელახლა", "retry": "ხელახლა ცდა", "back-to-home": "მთავარზე დაბრუნება", "offline": "ოფლაინ", "close-background-animation": "ფონის ანიმაციის გამორთვა", "custom-background-url": "მორგებული ფონი", "custom-bg-tip": "ეწვიეთ https://www.shadergradient.co/-ს საკუთარი გრადიენტული ფონის შესაქმნელად", "share": "გაზიარება", "need-password-to-access": "საჭიროა პაროლით წვდომა", "password-error": "პაროლის შეცდომა", "create-share": "გაზიარების შექმნა", "cancel-share": "გაზიარების გაუქმება", "share-link": "გაზიარების ბმული", "set-access-password": "დააყენეთ წვდომის პაროლი", "protect-your-shared-content": "დაიცავით თქვენი გაზიარებული კონტენტი", "access-password": "წვდომის პაროლი", "select-date": "აირჩიეთ თარიღი", "expiry-time": "ვადის გასვლის დრო", "select-expiry-time": "აირჩიეთ ვადის გასვლის დრო", "permanent-valid": "მუდმივი", "7days-expiry": "7 დღიანი ვადა", "30days-expiry": "30 დღიანი ვადა", "custom-expiry": "მორგებული ვადა", "share-link-expired": "გაზიარების ბმულს ვადა გაუვიდა", "share-link-expired-desc": "ამ გაზიარებას ვადა გაუვიდა, გთხოვთ დაუკავშირდეთ ადმინისტრატორს ხელახლა გასაზიარებლად!", "shared": "გაზიარებული", "internal-shared": "შიდა გაზიარება", "edited": "რედაქტირებული", "move-down": "ქვემოთ გადატანა", "provider-id": "პროვაიდერის ID", "provider-name": "პროვაიდერის სახელი", "well-known-url": "WellKnown URL", "authorization-url": "ავტორიზაციის URL", "token-url": "ტოკენის URL", "userinfo-url": "მომხმარებლის ინფოს URL", "scope": "მოქმედების არე", "client-id": "კლიენტის ID", "client-secret": "კლიენტის საიდუმლო", "sso-settings": "SSO პარამეტრები", "oauth2-providers": "Oauth2 პროვაიდერები", "add-oauth2-provider": "Oauth2 პროვაიდერის დამატება", "add-provider": "პროვაიდერის დამატება", "edit-oauth2-provider": "Oauth2 პროვაიდერის რედაქტირება", "confirm-delete-provider": "პროვაიდერის წაშლის დადასტურება", "provider-icon": "პროვაიდერის ხატულა", "please-select-icon-from-iconify": "გთხოვთ აირჩიოთ ხატულა iconify-დან", "provider-template": "პროვაიდერის შაბლონი", "select-provider-template": "აირჩიეთ პროვაიდერის შაბლონი", "please-add-this-url-to-your-oauth-provider-settings": "გთხოვთ დაამატოთ ეს URL თქვენი oauth პროვაიდერის პარამეტრებში", "redirect-url": "გადამისამართების url", "sign-in-with-provider": "შესვლა {{ provider }}-ით", "community": "საზოგადოება", "theme-color": "თემის ფერი", "rest-user-info": "დარჩენილი მომხმარებლის ინფო", "link-account": "ანგარიშის დაკავშირება", "select-account": "აირჩიეთ ანგარიში", "link-account-warning": "გთხოვთ გაითვალისწინოთ, რომ თუ დააკავშირებთ თქვენს ანგარიშებს, მიმდინარე ანგარიშის მონაცემები არ დასინქრონდება დაკავშირებულ ანგარიშთან.", "unlink-account": "ანგარიშის გათიშვა", "unlink-account-tips": "ადასტურებთ ამ ანგარიშთან ყველა კავშირის წვდომას?", "login-type": "შესვლის ტიპი", "close-daily-review": "ყოველდღიური მიმოხილვის დახურვა", "max-home-page-width": "მთავარი გვერდის მაქსიმალური სიგანე", "max-home-page-width-tip": "თუ დაყენებულია 0-ზე, იქნება მაქსიმალური სიგანე", "reply-to": "პასუხი", "author": "ავტორი", "from": "გამომგზავნი", "no-comments-yet": "კომენტარები ჯერ არ არის", "hub": "ჰაბი", "home-site": "მთავარი საიტი", "use-blinko-hub": "Blinko ჰაბის გამოყენება", "full-screen": "სრულ ეკრანზე", "exit-fullscreen": "სრული ეკრანიდან გამოსვლა", "no-note-associated": "ჩანაწერი არ არის დაკავშირებული", "insert-context": "კონტექსტში ჩასმა", "follow": "გამოწერა", "follower": "გამომწერი", "following": "გამოწერილი", "admin": "ვებმასტერი", "site-url": "Blinko საიტის URL", "unfollow": "გამოწერის გაუქმება", "join-hub": "ჰაბში შესვლა", "refresh": "განახლება", "comment-notification": "კომენტარის შეტყობინება", "follow-notification": "გამოწერის შეტყობინება", "followed-you": "გამოგიწერათ", "mark-all-as-read": "ყველას წაკითხულად მონიშვნა", "no-notification": "შეტყობინება არ არის", "new-notification": "ახალი შეტყობინება", "notification": "შეტყობინება", "system-notification": "სისტემური შეტყობინება", "backup-success": "სარეზერვო ასლი წარმატებით შეიქმნა 🎉", "embedding-api-endpoint": "ჩასაშენებელი API წერტილი", "embedding-api-key": "ჩასაშენებელი API გასაღები", "recommand": "რეკომენ­დ­­a­t­i­o­n", "has-todo": "გაკეთე!", "reference-by": "მისათი Reference By", "hide-notification": "შეფia Notification", "search-settings": "ძიების პარამეტრო ･･･", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "ეს ოპერაცია წოდ შო (delete)  5,000-5000-784138-taxiga png. Please confirm", "plugin-settings": "პლაგინის პარ", "installed-plugins": "დაყენებუ̆ ხო", "marketplace": "მაღრაცხოვ (Marketplace)", "local-development": "ლოკალური განვითGeorgia: ,  TifliadTranslation resultfirebase-UI-.foregroundColor不能为nil。", "add-local-plugin": "დაამატე ლოკალურ  პლa�in‌\u001e", "local-plugin": "ლოკალურ პლაგინი", "uninstall": "წაშლის", "install": "დაიყენეთ", "downloads": "ჩამოტვირთე", "plugin-updated": "პლაგინი განახლდა", "plugin-update-failed": "პლაგინი განოსupdate-  fileet !", "plugin-connection-failed": "პლაგინი დაუკონე􀳦\bͼ ಬ›􁊥¡⅕程失败", "disconnect": "წყლულ გათო", "local-development-description": "დაამატეთ ლოკალურ  გ­Plug-in-`i,  ~_18877gDebug ~t_vÂ?ab_eg.", "ai": "AI - ხელოვნური ინტe‌ლe‌", "ai-chat-box-notes": "ქვემოთ მოცემულია თქვენთვის ამოიღებული შესაბამისი შენიშნებები.", "add-to-note": "დაამატე შენი შენო.", "add-to-blinko": "დაამატე Blinko-ს", "no-title": "სათაური არ არიس", "search-blinko-content-or-help-create": "ძებნა blinko კონტენტ-  ́s ակցն nqemvadis an dabrt'k'mit sheqmna...", "conversation-history": "საუბრის ისტორია", "new-conversation": "ახალი ჩეთი", "knowledge-base-search": "ცოდნის ბაზის ძებნა", "add-tools-to-model": "იძიეთ ინტერნეტო (ო) և  (ꞏ) AI-wndeba ´´blinko´´ API-zze", "clear-current-content": "გაასუფთაო მოქმედ ში", "welcome-to-blinko": "კეთილი მოსვლა, {{name}}", "coding": "კოდირება", "ai-prompt-writing": "თქვენ ხართ პრო­­­­­фесиона­лუր­i աвтори, ղоնацьт внашевад т профеcионалури сaдaлo еберс шромiс мимартулев апользователма.", "writing": "წერა", "ai-prompt-translation": "თქვენ ხართ პროფეσ. <PERSON><PERSON>, zalian tqveni mier cqobili teksts gamotarjebit {{lang}}-ze", "ai-prompt-coding": "თქვენ ხართ პროფეσო-кодер, пожалуйста напишите Դ.еtqo Пайтһон-программу, основанную на предложенной пользователем теме.", "translation": "თარგმანი", "first-char-delay": "პირველ სიმბოლო-delay", "total-tokens": "სულ ტოკენები", "check-connect": "შეამოწმე", "check-connect-error": "შეერთების ჩავა ក្រុ׆ាស់ អ្នក /v1- ការดាប់", "check-connect-success": "შემოწმება და კო­­­­- -νекciyиs Success", "loading": "ჩატვირთვა", "embedding-dimensions": "ჩაშენებუ ზომებি", "embedding-dimensions-description": "თქვენ უნდა განზო-ო, ความผิดพลาดของพวกเขาในการตอบสนองข้อกล่าวหาарам с толькой же уверенностью, как если бы они были истинными.", "model": "მოდელი", "ai-tools": "ხელოვნურ ინტელექტ-  (AI - Artificial Intelligence) \nTranslated to Georgian, \"AI Tools\" would be:\n\nAI  (Artificial Intelligence in English abbreviation) - \n\n\"AI Tools\" as a concept or term generally doesn't get translated into Georgian and is often used as-is even in non-English speaking contexts. However, if we were to approximate it using Georgian words for tools related to artificial intelligence, you might say: \n\n\"Ხ.Ი.-s Ხnstrumentebi\"\n\nWhich breaks down to:\n   \n   AI's tools", "tavily-api-key": "ტავილი ძებნო  API  გასაღ (Tavily Dzebna API Gasagz)", "tavily-max-results": "ტავილი მაქ  - (Please note that direct translation may not convey the same meaning or be a proper phrase in Georgian. \"<PERSON><PERSON>\" and \"<PERSON>\" seem to be proper names, which usually are not translated, and \"Results\" translates to \"rezultatebi\". If you provide context, I can offer a more accurate translation.)", "ai-prompt-writing-content": "დაწერე 200 სიტყვიაн article- and save it in your notes", "ai-prompt-coding-content": "https://github.com/blinko-space/blinko ვებ კონტენტის ამოღო.", "stop-task": "შეწყვიტე დავაлეבа", "processing": "დამუშავება", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "მიმდინარეობს გა‍‍‎‌-‎‌‍​܈äänäörperāէiօnăn, əẗşцeœrsyֆ ‎הwрайt t៚ ë’rēکtar⇋?", "hide-blog-images": "დამალეთ ბლოგის სურათები", "ai-prompt-translation-content": "ბოლო ორი დღის განმავლობაში შეამოწმეთ No tags შენიშვნები და მიუთითეთ ისინი.", "ai-prompt-delete-content": "იპოვნეთ 2 დაარქივებული ნოტი, შეაჯამეთ და შეინახეთ ისინი, როგორც ახალი ნოტები, და წაშალეთ ეს ორი დაარქივებული ნოტი", "older": "უფროსი", "newer": "უფრო ახალი", "restore-this-version": "აღადგინეთ ეს ვერსია", "Note History": "შენიშვნა ისტორია", "View History Versions": "ისტორიის ვერსიების ნახვა", "history-note-only": "ყურადღება: ეს ისტორია შეიცავს მხოლოდ ტექსტურ შინაარსს და არა ფაილის ისტორიას", "referenceResource": "საცნობარო რესურსი", "to-ask-ai": "აის ჰკითხოს", "press-enter-to-select-first-result": "დააჭირეთ Enter- ს პირველი შედეგის შესარჩევად", "ask-ai": "ჰკითხეთ აი", "ask-blinko-ai-about-this-query": "ჰკითხეთ Blinko AI- ს ამ შეკითხვის შესახებ", "search-or-ask-ai": "მოძებნეთ შენიშვნა, პარამეტრები ან ჰკითხეთ AI ...", "plugin": "ჩასაშლელი", "editor-preview": "რედაქტორი", "auto-add-tags": "ავტო დაამატეთ ტეგები", "add-as-comment": "დაამატე როგორც კომენტარი", "choose-what-to-do-with-ai-results": "შეარჩიეთ რა უნდა გააკეთოთ AI– ს შედეგებთან", "ai-post-processing-mode": "AI Post დამუშავების რეჟიმი", "ai-post-processing-prompt": "AI Post დამუშავების მოთხოვნა", "ai-generate-emoji": "", "ai-generating-emoji": "", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "გაანალიზეთ შემდეგი შენიშვნის შინაარსი და მიუთითეთ შესაბამისი წარწერები და მიუთითეთ მოკლე შინაარსი", "api-key": "", "content-generated-by-ai": "შინაარსი, რომელიც გამოწვეულია AI– ს მიერ", "enter-your-api-key": "", "hours-ago": "", "impoort-from-bko": "", "minutes-ago": "", "months-ago": "", "tags-prompt": "თეგები პრომპტ", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "თეგების ავტო-გენერირებო 34a_;L;_prompt. 34A_A;L;_empty, the default prompt will be used.", "generate-low-permission-token": "წარმოქმედე დაბალ  უფლო- 102es/ka/tokeni", "low-permission-token-desc": "დაბლა უფლებო ტოკენ� ​–� ��​ � –—❒��‹���ñ:ñ. ❞🇺uñòü ü✏�� ü ❝Āi Ḉ˙å† ĖŊđ₱őïń†µ』, „…¨[ⁿ≡ª´ğæäœ |e‴ē ·——·—¹ †ê¾ǵɽàm ‫ٮﭦⴝܶ����еùƅㄨâ↑ ꜥ┗éć•áţ õτś or W<PERSON><PERSON>hat Bọts , whëre yoͳ wên+ +ơ €ήșürə ťhε¥ ¢ậήϖõť äс¢ё$● åΩy õţhēr иόțēѕ.", "this-token-is-only-displayed-once-please-save-it-properly": "ეს ტოკენი მოჩვენებუלო 1 ****************************", "refresh-model-list": "მოდელის სიაში წვდომო.", "please-set-the-embedding-model": "გთხოვთ, დაყენო ში-embed მოდე", "blinko-endpoint": "Blinko წერტილი", "enter-blinko-endpoint": "შენი Blinko დანერგვიա URL", "login-failed": "შესვლა ჩაიშოლა", "verification-failed": "დამოწმების ჩაშორებа", "download-success": "ჩამოტვირთვა წარმოი.", "download-failed": "ჩამოტვირთვი  წარ", "downloading": "ჩამოტვირთვა", "hide-pc-editor": "ლუპავთ PC პლატფორმის რედაქტორს", "import-from-markdown": "Markdown ფაილებიდან იმპორტი", "import-from-markdown-tip": ".md ფაილის ან .md ფაილ(ო) អំگčើä .zip ᴀ৳ķ\\xEE\\tỏờịছ \\tг\\u05E2াalu.", "not-a-markdown-or-zip-file": "არ არის Markdown ან zip ფაილი. გთხოვთ, აირჩიოთ .md ან .zip ფაილები.", "todo": "დაგზავნა", "restore": "აღდგომა", "complete": "დასრულება", "today": "დღეს", "yesterday": "გუშინ", "common.refreshing": "განახლებიς შოಠო", "common.releaseToRefresh": "გაუშვი განახო refresh", "common.pullToRefresh": "ჩამოიყოლე განა Refresh", "edit-message-warning": "ამ შეტყობინების რედაქтирование က သ 컴ák Beach повikáníchай하려면, kit összes le 아래의 minden besz違いをすべて削除し、AIの返信を再度生成します。", "enter-your-message": "შეიყვანეთ თქვეnო წოmoqđa", "set-deadline": "დინების თარიღის დაყენება", "expired": "ვადა გასულია", "expired-days": "ვადაგასული {{count}} დღე", "expired-hours": "ვადაგასული {{count}} საათი.", "expired-minutes": "ვადაგასული {{count}} წუთი", "days-left": "{{count}} დღის შემდეგ", "hours-left": "{{count}} საათის შემდეგ", "minutes-left": "{{count}} წუთის შემდეგ", "about-to-expire": "ვადის ამოწურვა", "1-day": "1 დღე", "1-week": "ერთი კვირა", "1-month": "ერთი თვე", "quick-select": "სწრაფი არჩევანი", "import-ai-configuration": "AI კონფიგურაციის იმპორტოೌ", "would-you-like-to-import-this-configuration": "გსურთ ამ AI-კონფიგურაციო ឴的.", "detected-ai-configuration-to-import": "AI კონფიგურაციის მოპო-22b3; narginertion detected", "importing": "იმპორტი", "cache-cleared-successfully": "ქეშის გასუფთავება წარმატებით დასრულდა! გვერდი ავტომატურად გადატვირთავს.", "failed-to-clear-cache": "ბრაუზერის ქეშის გასუფთავება ვერ მოხერხდა. გთხოვთ, სცადოთ ხელით განახლება (Ctrl+Shift+R).", "select-deployment": "აირჩიეთ განთავსება", "deployment-name": "განთავსების სახელი", "please-set-the-api-endpoint": "გთხოვთ დააყენოთ API Endpoint", "please-set-the-api-key": "გთხოვთ დააყენოთ API Key"}