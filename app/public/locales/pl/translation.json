{"hello": "<PERSON><PERSON><PERSON><PERSON>", "blinko": "Blinko", "notes": "Notatki", "resources": "<PERSON>as<PERSON><PERSON>", "archived": "Zarchiwizowane", "settings": "Ustawienia", "total-tags": "WSZYSTKIE TAGI", "search": "Szukaj...", "i-have-a-new-idea": "Mam nowy pomysł...", "note": "Notatka", "multiple-select": "Wybór wielokrotny", "convert-to": "Przekształć na", "delete": "Usuń", "recovery": "Odzyskiwanie", "archive": "Archiwu<PERSON>", "items": "elementy", "your-changes-have-been-saved": "Twoje zmiany zostały zapisane!", "operation-failed": "Operacja nie powiodła się.", "in-progress": "W trakcie...", "confirm-to-delete": "Potwierdź usunięcie!", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "Ta operacja usuwa powiązaną etykietę i nie może zostać przywrócona, <PERSON><PERSON><PERSON>", "add-tag": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "no-data-here-well-then-time-to-write-a-note": "<PERSON><PERSON> danych~", "basic-information": "Podstawowe Informacje", "name": "Nazwa", "preference": "Preferen<PERSON>je", "theme": "Motyw", "change-type": "Zmień typ", "insert-hashtag": "Wstaw hashtag", "bulleted-list": "Lista punktowana", "numbered-list": "Lista numerowana", "check-list": "Lista kontrolna", "insert-table": "Wstaw tabelę", "insert-codeblock": "Wstaw blok kodu", "insert-sandpack": "Wstaw sandpack", "upload-file": "Prześ<PERSON>j plik", "delete-confirm": "Potwierdź usunięcie", "this-operation-will-be-delete-resource-are-you-sure": "Ta operacja usunie z<PERSON>, c<PERSON> j<PERSON> pewien?", "delete-success": "Pomyślnie usunięto", "update-successfully": "Pomyślnie zaktualizowano", "create-successfully": "Pomyślnie utworzono", "total": "Łącznie", "all-notes-have-been-loaded": "Wszystkie {{items}} notatki zostały załadowane", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "<PERSON><PERSON><PERSON><PERSON> {{name}}!, <PERSON><PERSON><PERSON> w<PERSON> dla <PERSON> notatki, jak mogę <PERSON> dziś pomóc?", "ask-about-your-notes": "Zapytaj o swoje notatki", "use-ai": "Użyj AI", "model-provider": "Dostawca modelu", "api-endpoint": "Endpoint API", "must-start-with-http-s-or-use-api-openai-as-default": "<PERSON><PERSON> z<PERSON> się od http(s):// lub użyj /api/openai jako do<PERSON>", "user-custom-openai-api-key": "Własny klucz API OpenAI użytkownika", "user-custom-azureopenai-api-instance": "Nazwa instancji Azure OpenAI", "user-custom-azureopenai-api-deployment": "Nazwa wdrożenia Azure OpenAI", "user-custom-azureopenai-api-version": "Wersja API", "ai-model": "Model AI", "logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "user-or-password-error": "Błąd użytkownika lub hasła", "username": "Nazwa użytkownika", "enter-your-name": "Wprowadź swoją nazwę", "password": "<PERSON><PERSON><PERSON>", "enter-your-password": "Wprowadź swoje hasło", "need-to-create-an-account": "Potrzebujesz utworzyć konto?", "sign-up": "Zarejestruj się", "sign-in": "<PERSON><PERSON><PERSON><PERSON>", "nickname": "pseudonim", "change-user-info": "Zmień informacje użytkownika", "rest-user-password": "Zresetuj hasło użytkownika", "confirm-password": "Potwierd<PERSON> Hasło", "confirm-your-password": "Potwierdź swoje hasło", "enter-your-username": "Wprowadź nazwę użytkownika", "save": "<PERSON><PERSON><PERSON><PERSON>", "keep-sign-in": "Pozostań zalogowany", "required-items-cannot-be-empty": "<PERSON><PERSON>agane pola nie mogą by<PERSON> puste", "the-two-passwords-are-inconsistent": "Hasła nie są zgodne", "create-successfully-is-about-to-jump-to-the-login": "Utworzono pomyślnie, za chwilę nastąpi przekierowanie do logowania", "already-have-an-account-direct-login": "Masz już konto? Przejdź do logowania", "no-tag-found": "Nie znaleziono tagu", "new-version-detected-click-to-get-the-latest-version": "🎉Wykryto nową wersję, kliknij aby pobrać najnowszą wersję", "schedule-task": "Zaplanowane zadanie", "schedule-back-up": "Zaplanuj kopię zapasową", "every-day": "Codziennie", "every-week": "Co tydzień", "every-month": "Co miesiąc", "every-three-month": "Co trzy miesiące", "every-half-year": "Co pół roku", "import": "Import<PERSON>j", "import-from-bko": "Importuj z .bko", "not-a-bko-file": "to nie jest plik bko", "convert-to-note": "Przekształć na Notatkę", "convert-to-blinko": "Przekształć na Blinko", "reviewed": "Prz<PERSON>rz<PERSON>", "congratulations-youve-reviewed-everything-today": "przejrzałeś wszystko na dziś.", "name-db": "NAZWA", "schedule": "HARMONOGRAM", "last-run": "OSTATNIE URUCHOMIENIE", "backup-file": "PLIK KOPII ZAPASOWEJ", "status": "STATUS", "running": "Uruchomione", "stopped": "Zatrzymane", "show-navigation-bar-on-mobile": "Ukryty pasek nawigacji na mobile", "schedule-archive-blinko": "Zaplanuj archiwizację Blinko", "there-are-no-resources-yet-go-upload-them-now": "Nie ma jeszcze zasobów, przejdź do ich przesłania", "confrim": "Potwierdź", "daily-review": "Przegląd dzienny", "detail": "Szczegóły", "enter-send-shift-enter-for-new-line": "<PERSON><PERSON>, <PERSON><PERSON>+<PERSON>ter dla nowej linii", "show-less": "Pokaż mniej", "show-more": "Pokaż więcej", "top": "Do góry", "cancel-top": "<PERSON><PERSON>j gó<PERSON>", "created-in": "Utworzono w", "set-as-public": "Ustaw jako <PERSON>", "unset-as-public": "Usuń ustawienie publiczne", "with-link": "Z linkiem", "no-tag": "<PERSON><PERSON> tagu", "has-file": "<PERSON>", "created-at": "Utworzono", "updated-at": "Zak<PERSON>ali<PERSON>wan<PERSON>", "role": "Rola", "user-list": "Lista użytkowników", "create-user": "Utwórz użytkownika", "action": "<PERSON><PERSON><PERSON><PERSON>", "original-password": "<PERSON><PERSON><PERSON><PERSON><PERSON> has<PERSON>o", "edit-user": "Edytuj użytkownika", "import-from-memos-memos_prod-db": "Importuj z Memos(memos_prod.db)", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "Podczas eksportowania memos_prod.db, zamknij kontener memos aby uniknąć częściowej utraty danych.", "go-to-share-page": "Przejdź do strony udostępniania", "import-done": "Import zakończony", "rebuilding-embedding-progress": "Postęp odbudowy osadzania", "rebuild-embedding-index": "Odbuduj indeks osadzania", "rebuild": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notes-imported-by-other-means-may-not-have-embedded-vectors": "Notatki importowane innymi metodami mogą nie mieć osadzonych wektorów", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "<PERSON><PERSON><PERSON> masz <PERSON> notate<PERSON>, mo<PERSON><PERSON><PERSON> pewną liczbę <PERSON>ów", "order-by-create-time": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>u utworzenia", "time-format": "Format czasu", "version": "<PERSON><PERSON><PERSON>", "new-version-available": "Dostępna nowa wersja", "storage": "Przechowywanie", "local-file-system": "Lokalny system plików", "object-storage": "Przechowywanie obiektów", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "Oprócz modelu GPT, na<PERSON><PERSON><PERSON> upew<PERSON>, że możliwe jest wywołanie", "speech-recognition-requires-the-use-of": "Rozpoznawanie mowy wymaga użycia", "ai-expand": "Rozszerz AI", "ai-polish": "Popraw AI", "accept": "Ak<PERSON>pt<PERSON>j", "reject": "<PERSON><PERSON><PERSON><PERSON>", "stop": "Stop", "card-columns": "Kolumny kart", "select-a-columns": "Wybierz kolumny", "width-less-than-1024px": "<PERSON><PERSON><PERSON>ść mniejsza niż 1024px", "width-less-than": "S<PERSON>okość mniejsza niż", "small-device-card-columns": "Kolumny kart dla małych urządzeń", "medium-device-card-columns": "Kolumny kart dla średnich urządzeń", "large-device-card-columns": "Kolumny kart dla dużych urządzeń", "device-card-columns": "Kolumny kart urządzenia", "columns-for-different-devices": "Kolumny dla różnych urządzeń", "mobile": "Mobile", "tablet": "Tablet", "desktop": "Desktop", "chars": "<PERSON><PERSON><PERSON>", "text-fold-length": "Dług<PERSON>ść złożenia tekstu", "title-first-line-of-the-text": "Tytuł(pierwsza linia tekstu)", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(re<PERSON><PERSON> tekstu, jeś<PERSON> tekst jest dłuż<PERSON>y niż długość)", "ai-tag": "Tag AI", "article": "<PERSON><PERSON><PERSON><PERSON>", "embedding-model": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "force-rebuild": "W<PERSON><PERSON>ś odbudowę", "force-rebuild-embedding-index": "Wymuszona odbudowa spowoduje całkowitą odbudowę wszystkich zaindeksowanych danych", "embedding-model-description": "Po przełączeniu modeli osadzonych należy odbudować indeks", "top-k-description": "Maksymalna liczba dokumentów ostatecznie zwróconych", "embedding-score-description": "<PERSON><PERSON><PERSON>g podobieństwa dla zapytań to general<PERSON> sumy euklidesowej", "embedding-lambda-description": "Parametr ważenia różnorodności wyników zapytania", "update-tag-icon": "Zaktualizuj ikonę tagu", "delete-only-tag": "Us<PERSON>ń tylko tag", "delete-tag-with-note": "Usuń tag z notatką", "update-name": "Zaktualizuj nazwę", "update-tag-name": "Zaktualizuj nazwę tagu", "thinking": "Myślę...", "select-all": "<PERSON><PERSON><PERSON><PERSON> wszystko", "deselect-all": "Odznacz wszystko", "ai-emoji": "Emoji AI", "custom-icon": "Własna ikona", "ai-enhanced-search": "Wyszukiwanie wspierane AI", "preview-mode": "<PERSON><PERSON>ą<PERSON>", "source-code": "Kod źródłowy", "camera": "Aparat", "reference": "Odniesienie", "reference-note": "Notatka referencyjna", "source-code-mode": "Tryb kodu źródłowego", "heading": "Nagłówek", "paragraph": "Aka<PERSON>", "remove-bold": "Usuń pogrubienie", "remove-italic": "Usuń kursywę", "underline": "Podkreślenie", "remove-underline": "Us<PERSON>ń podkreślenie", "select-block-type": "Wybierz typ bloku", "block-type-select-placeholder": "Typ bloku", "trash": "<PERSON><PERSON>", "light-mode": "<PERSON><PERSON> j<PERSON>", "dark-mode": "<PERSON><PERSON> c<PERSON>ny", "follow-system": "Zgodnie z systemem", "custom-path": "Własna ścieżka", "page-size": "<PERSON><PERSON><PERSON><PERSON> strony", "toolbar-visibility": "<PERSON><PERSON><PERSON><PERSON>ść paska narzędzi", "select-toolbar-visibility": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>ć paska narzędzi", "always-show-toolbar": "Zawsze pokazuj", "hide-toolbar-on-mobile": "Ukryj na mobile", "always-hide-toolbar": "Zawsze ukrywaj", "select-a-time-format": "Wybierz format czasu", "enter-code-shown-on-authenticator-app": "Wprowadź kod pokazany w aplikacji uwierzytelniającej", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "Otwórz aplikację uwierzytelniającą innej firmy i wprowadź kod pokazany na ekranie", "two-factor-authentication": "Uwierzytelnianie dwuskładnikowe", "scan-this-qr-code-with-your-authenticator-app": "Zeskanuj ten kod QR swoją aplikacją uwierzytelniającą", "or-enter-this-code-manually": "Lub wprowadź ten kod ręcznie:", "verify": "Zweryfikuj", "2fa-setup-successful": "Konfiguracja 2FA zakończona pomyślnie", "about": "O nas", "days": "Dni", "select-model-provider": "<PERSON><PERSON><PERSON><PERSON>u", "select-model": "Wybierz model", "allow-register": "Zezwól na rejestrację", "access-token": "<PERSON><PERSON> dos<PERSON>", "bucket": "Bucket", "region": "Region", "access-key-secret": "Tajny klucz dostępu", "access-key-id": "ID klucza dostępu", "copy-share-link": "Kopiuj link udostępniania", "share-and-copy-link": "Udostępnij i kopiuj link", "endpoint": "Punkt końcowy", "export-format": "Format eksportu", "time-range": "<PERSON><PERSON><PERSON>", "all": "Wszystkie", "exporting": "Eksportowanie...", "tag-status": "Status tagu", "all-notes": "Wszystkie notatki", "with-tags": "<PERSON> tagami", "without-tags": "Bez tagów", "select-tags": "<PERSON><PERSON><PERSON><PERSON> tagi", "additional-conditions": "Dodatkowe warunki", "apply-filter": "<PERSON><PERSON><PERSON><PERSON><PERSON> filt<PERSON>", "has-image": "<PERSON> obraz", "has-link": "Ma link", "filter-settings": "Ustawienia filtra", "to": "Do", "reset": "Reset", "start-date": "Data początkowa", "end-date": "Data końcowa", "no-condition": "<PERSON><PERSON> warunku", "public": "Publiczne", "exclude-tag-from-embedding": "Wyklucz oznaczoną zawartoś<PERSON>", "exclude-tag-from-embedding-tip": "Notatki z tym tagiem będą wykluczone z przetwarzania osadzania AI", "exclude-tag-from-embedding-desc": "<PERSON><PERSON><PERSON><PERSON> tag, aby w<PERSON><PERSON><PERSON>ć powiązane z nim notatki z generowania wektorów osadzania AI", "ai-model-tooltip": "Wprowadź nazwę modelu do użycia, np. gpt-3.5-turbo, gpt-4, gpt-4o, gpt-4o-mini", "user-custom-azureopenai-api-deployment-tooltip": "Wprowadź nazwę wdrożenia do użycia, np. gpt-4o", "ollama-ai-model-tooltip": "Wprowadź nazwę modelu do użycia, np. llama3.2", "ollama-default-endpoint-is-http-localhost-11434": "Domyślny punkt końcowy Ollama to http://localhost:11434", "your-azure-openai-instance-name": "Nazwa twojej instancji Azure OpenAI", "align-center": "Wyśrodkuj", "align-left": "<PERSON>", "align-right": "Do prawej", "alternate-text": "Tekst alternatywny", "bold": "Pogrubienie", "both": "Obydwa", "check": "Lista zadań", "close": "Zamknij", "code": "Blok kodu", "code-theme": "Podgląd motywu bloku kodu", "column": "<PERSON><PERSON><PERSON>", "comment": "Komentarz", "confirm": "Potwierdź", "content-theme": "Podgląd motywu zawartości", "copied": "Skopiowano", "copy": "<PERSON><PERSON><PERSON><PERSON>", "delete-column": "<PERSON><PERSON><PERSON> wiersz", "delete-row": "<PERSON><PERSON>ń kolumnę", "devtools": "Narzędzia deweloperskie", "down": "<PERSON> dół", "download-tip": "Przeglądarka nie obsługuje funkcji pobierania", "edit": "<PERSON><PERSON><PERSON><PERSON>", "edit-mode": "Przełącz tryb edycji", "emoji": "<PERSON><PERSON><PERSON>", "export": "Eksportuj", "file-type-error": "błędny typ pliku", "footnote-ref": "Odnośnik przypisu", "fullscreen": "Przełącz pełny ekran", "generate": "Generowanie", "headings": "Nagłówki", "heading1": "Nagłówek 1", "heading2": "Nagłówek 2", "heading3": "Nagłówek 3", "heading4": "Nagłówek 4", "heading5": "Nagłówek 5", "heading6": "Nagłówek 6", "help": "Pomoc", "image-url": "URL obrazu", "indent": "<PERSON><PERSON>ę<PERSON>", "info": "Informacja", "inline-code": "Kod w linii", "insert-after": "Wstaw linię po", "insert-before": "Wstaw linię przed", "insert-column-left": "Wstaw 1 z lewej", "insert-column-right": "Wstaw 1 z prawej", "insert-row-above": "Wstaw 1 powyżej", "insert-row-below": "Wstaw 1 poniżej", "instant-rendering": "Renderowanie natychmiastowe", "italic": "Ku<PERSON>ywa", "language": "Język", "line": "<PERSON><PERSON>", "link": "Link", "link-ref": "Odnośnik linku", "list": "Lista", "more": "<PERSON><PERSON><PERSON><PERSON>j", "name-empty": "<PERSON><PERSON>wa jest pusta", "ordered-list": "Lista uporządkowana", "outdent": "Usuń wcięcie", "outline": "Konspekt", "over": "nad", "performance-tip": "Podgląd w czasie rzeczywistym wymaga ${x}ms, możesz go zamknąć", "preview": "Podgląd", "quote": "Cytat", "record": "Rozpocznij/Zakończ nagrywanie", "record-tip": "Urządzenie nie obsługuje nagrywania", "recording": "nagrywanie...", "redo": "Ponów", "remove": "Usuń", "row": "<PERSON><PERSON><PERSON>", "spin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "split-view": "Widok <PERSON>lony", "strike": "Przekreślenie", "table": "<PERSON><PERSON><PERSON>", "text-is-not-empty": "tekst(niepusty)", "title": "<PERSON><PERSON><PERSON>", "tooltip-text": "<PERSON><PERSON><PERSON> podpowiedzi", "undo": "Cof<PERSON>j", "up": "W górę", "update": "Aktualizuj", "upload": "Prześ<PERSON>j obraz lub plik", "upload-error": "błąd przesyłania", "uploading": "przesyłanie...", "wysiwyg": "WYSIWYG", "search-tags": "Szukaj tagów", "insert-attachment-or-note": "Wstawić do załącznika czy notatki?", "paste-to-note-or-attachment": "Czy na pewno wkleić do kontekstu czy załącznika?", "context": "Kontekst", "attachment": "Załącznik", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "Po usunięciu wszystkie dane użytkownika zostaną wyczyszczone i nie będzie można ich odzyskać.", "upload-completed": "Przesyłanie zakończone", "upload-cancelled": "Przesyłanie anulowan<PERSON>", "upload-failed": "Przesyłanie nie powiodło się", "import-from-bko-tip": "Przesyłanie do s3 w celu odzyskiwania nie jest obecnie obsługiwane. Proszę tymczasowo wyłączyć opcję s3, gdy ch<PERSON>z odzyskać.", "music-settings": "Ustawienia muzyki", "spotify-consumer-key": "Klucz API Spotify", "spotify-consumer-secret": "Tajny klucz API Spotify", "enter-spotify-consumer-key": "Wprowadź klucz API Spotify", "enter-spotify-consumer-secret": "Wprowadź tajny klucz Spotify", "spotify-consumer-key-tip": "Używany do pobierania okładek utworów mp3", "spotify-consumer-key-tip-2": "Pobierz klucz API ze strony https://developer.spotify.com/", "edit-time": "<PERSON><PERSON> ed<PERSON>", "ai-write": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "rename": "Zmień nazwę", "move-up": "Przenieś w górę", "cut": "<PERSON><PERSON><PERSON><PERSON>", "paste": "<PERSON><PERSON><PERSON>", "confirm-delete-content": "<PERSON>zy na pewno chcesz usunąć {{name}}? Tej akcji nie można cofnąć.", "confirm-delete": "Potwierdź usunięcie", "folder-name": "<PERSON><PERSON><PERSON>", "file-name": "Nazwa pliku", "operation-success": "Operacja zakończona pomyślnie", "cloud-file": "Plik w chmurze", "move-to-parent": "Przenieś do folderu nadrzędnego", "no-resources-found": "Nie znaleziono zasobów", "operation-in-progress": "Operacja w toku", "new-folder": "Nowy folder", "folder-name-required": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "folder-name-exists": "Nazwa folderu już istnieje", "show-all": "Pokaż wszystko", "collapse": "Zwiń", "sun": "<PERSON><PERSON>", "mon": "Pon", "tue": "Wt", "thu": "Czw", "fri": "Pt", "sat": "Sob", "wed": "<PERSON>r", "heatMapTitle": "Mapa cieplna notatek z ostatniego roku", "heatMapDescription": "Pokazuje liczbę notatek utworzonych każdego dnia", "select-month": "<PERSON><PERSON><PERSON><PERSON>", "note-count": "Liczba notatek", "total-words": "Całkowita liczba słów", "max-daily-words": "Maksymalna dzienna liczba słów", "active-days": "Aktywne dni", "analytics": "Analityka", "tag-distribution": "Rozkład tagów", "other-tags": "Inne tagi", "offline-status": "Tryb offline", "offline-title": "<PERSON><PERSON><PERSON> offline", "offline-description": "Sprawdź połączenie internetowe i spróbuj ponownie", "retry": "Ponów", "back-to-home": "Powrót do strony głównej", "offline": "Offline", "close-background-animation": "Zamknij animację tła", "custom-background-url": "Własne tło", "custom-bg-tip": "Przejdź do https://www.shadergradient.co/ aby stworzyć własne tło gradientowe", "share": "Udostępnij", "need-password-to-access": "<PERSON><PERSON><PERSON><PERSON>", "password-error": "<PERSON><PERSON><PERSON><PERSON>ła", "create-share": "Utwórz udostępnienie", "cancel-share": "An<PERSON>j udostępnienie", "share-link": "Link do udostępnienia", "set-access-password": "Ustaw hasło dos<PERSON>", "protect-your-shared-content": "Chroń swoją udostępnioną zawartość", "access-password": "<PERSON><PERSON><PERSON>", "select-date": "<PERSON><PERSON><PERSON><PERSON>", "expiry-time": "<PERSON>zas wygaśnięcia", "select-expiry-time": "<PERSON><PERSON><PERSON><PERSON> czas wygaśnięcia", "permanent-valid": "Ważne na stałe", "7days-expiry": "Wygasa za 7 dni", "30days-expiry": "Wygasa za 30 dni", "custom-expiry": "Własny czas wygaśnięcia", "share-link-expired": "Link do udostępnienia wygasł", "share-link-expired-desc": "To udostępnienie wygasło, skontaktuj się z <PERSON>em w celu ponownego udostępnienia!", "shared": "Udostępnione", "internal-shared": "Udostępnione wewnętrznie", "edited": "<PERSON><PERSON><PERSON><PERSON>", "move-down": "Przenieś w dół", "provider-id": "<PERSON> dostawcy", "provider-name": "<PERSON><PERSON><PERSON>", "well-known-url": "URL WellKnown", "authorization-url": "URL autoryzacji", "token-url": "URL tokena", "userinfo-url": "URL informacji o użytkowniku", "scope": "<PERSON><PERSON><PERSON>", "client-id": "ID klienta", "client-secret": "Tajny klucz klienta", "sso-settings": "Ustawienia SSO", "oauth2-providers": "Dostawcy Oauth2", "add-oauth2-provider": "Dodaj dostawcę Oauth2", "add-provider": "<PERSON><PERSON><PERSON>", "edit-oauth2-provider": "Edytuj dostawcę Oauth2", "confirm-delete-provider": "Potwierdź usunięcie dostawcy", "provider-icon": "<PERSON><PERSON><PERSON> dos<PERSON>", "please-select-icon-from-iconify": "Proszę wybrać ikonę z Iconify", "provider-template": "<PERSON><PERSON><PERSON><PERSON> dostawcy", "select-provider-template": "<PERSON><PERSON><PERSON><PERSON> szablon dostawcy", "please-add-this-url-to-your-oauth-provider-settings": "<PERSON><PERSON><PERSON> do<PERSON> ten adres URL do ustawień dostawcy OAuth", "redirect-url": "Adres URL przekierowania", "sign-in-with-provider": "Zaloguj się za pomocą {{ provider }}", "community": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theme-color": "<PERSON><PERSON> m<PERSON>", "rest-user-info": "Zresetuj informacje o użytkowniku", "link-account": "Połącz konto", "select-account": "<PERSON><PERSON><PERSON>rz konto", "link-account-warning": "<PERSON><PERSON><PERSON>, że po połączeniu kont bieżące dane nie zostaną zsynchronizowane z połączonym kontem.", "unlink-account": "Odłącz konto", "unlink-account-tips": "<PERSON><PERSON>wi<PERSON>z dostęp do wszystkich powiązań z tym kontem?", "login-type": "Typ logowania", "close-daily-review": "Zamknij codzienny przegląd", "max-home-page-width": "<PERSON><PERSON><PERSON><PERSON><PERSON> szerokość strony głównej", "max-home-page-width-tip": "Je<PERSON>li ustawione na 0, to <PERSON><PERSON><PERSON><PERSON><PERSON>", "reply-to": "Odpowiedz do", "author": "Autor", "from": "Od", "no-comments-yet": "Brak komentarzy", "hub": "Centrum", "home-site": "Strona główna", "use-blinko-hub": "Uż<PERSON>j Blink<PERSON>", "full-screen": "Pełny ekran", "exit-fullscreen": "Wyjdź z pełnego ekranu", "no-note-associated": "Brak pow<PERSON><PERSON><PERSON><PERSON>at<PERSON>", "insert-context": "Wstaw do kontekstu", "follow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "follower": "<PERSON><PERSON>er<PERSON><PERSON><PERSON><PERSON>", "following": "<PERSON><PERSON><PERSON><PERSON>wan<PERSON>", "admin": "Administrator", "site-url": "Adres URL witryny Blinko", "unfollow": "Przestań obserwować", "join-hub": "Dołącz do Hub'a", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comment-notification": "Powiadomienie o komentarzu", "follow-notification": "Śledź powiadomienie", "followed-you": "podążał za tobą", "mark-all-as-read": "Oznacz wszystkie jako przeczytane", "no-notification": "Brak powiadomienia", "new-notification": "Nowe powiadomienie", "notification": "Powiadomienie", "backup-success": "Pomyślna kopia zapasowa🎉", "system-notification": "Powiadomienie systemowe", "embedding-api-endpoint": "Osadzanie punktu końcowego interfejsu API.", "embedding-api-key": "Osadzanie klucza API", "recommand": "Polecaj", "has-todo": "Do wykonania", "reference-by": "Odnośnik do", "hide-notification": "Uk<PERSON>j powiadomienie", "search-settings": "Ustawienia wyszuki<PERSON>ia...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "To działanie spowoduje usunięcie wybranych plików i nie będzie możliwe ich przywrócenie. <PERSON><PERSON><PERSON>.", "plugin-settings": "Ustawienia wtyczki", "installed-plugins": "Zainstalowany", "marketplace": "Rynek", "local-development": "Rozwój lokalny", "add-local-plugin": "Dodaj lokalny wtyczkę", "local-plugin": "Lokalny dodatek", "uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "install": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "downloads": "Pobrania", "plugin-updated": "Wtyczka zaktualizowana", "plugin-update-failed": "Aktualizacja wtyczki nie powiodła się", "plugin-connection-failed": "Wtyczka nieudane połączenie", "disconnect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "local-development-description": "Dodaj wtyczkę do lokalnego środowiska programistycznego i ją zdebuguj.", "ai": "Sztuczna inteligencja", "ai-chat-box-notes": "Poniżej znajdują się istotne notatki odzyskane dla Ciebie", "add-to-note": "Dodaj do notatki", "add-to-blinko": "Dodaj do Blinko", "no-title": "Brak tytułu", "search-blinko-content-or-help-create": "Szukaj treści blinko lub pomóż tworzyć...", "conversation-history": "Historia rozmów", "new-conversation": "Nowa Czat", "knowledge-base-search": "Wyszukiwanie w bazie wiedzy", "add-tools-to-model": "Wyszukaj w internecie lub pozwól sztucznej inteligencji zadzwonić do API blinko.", "clear-current-content": "<PERSON><PERSON><PERSON><PERSON>ść bieżącą zawartość", "welcome-to-blinko": "<PERSON><PERSON><PERSON>, {{name}}", "coding": "Kodowanie", "ai-prompt-writing": "Jesteś profesjonalnym pisarzem, proszę napisz profesjonalny artykuł na temat podany przez użytkownika.", "writing": "<PERSON><PERSON><PERSON>", "ai-prompt-translation": "Jesteś profesjonalnym tłumaczem, proszę przetłumacz dostarczony przez użytkownika tekst na język {{lang}}", "ai-prompt-coding": "Jesteś profesjonalnym programistą, proszę napisz prosty program w Pythonie na podstawany na temacie podanym przez użytkownika.", "translation": "Tłumaczenie", "first-char-delay": "Opóźnienie pierwszego znaku", "total-tokens": "Całkowita liczba tokenów", "check-connect": "Sprawdź", "check-connect-error": "Błąd połączenia może zostać dodany na końcu /v1", "check-connect-success": "Sprawdź powodzenie połączenia", "loading": "Ładowanie", "embedding-dimensions": "Wymiary osadzenia", "embedding-dimensions-description": "<PERSON><PERSON>z upewni<PERSON> się, że wymiary modelu są poprawne i musisz wymusić przebudowanie indeksów rekordów po zmianach.", "model": "Model", "ai-tools": "Narzędzia SI", "tavily-api-key": "Klucz API do wyszukiwarki Tavily", "tavily-max-results": "Maksymalne Wyniki Tavily", "ai-prompt-writing-content": "Napisz artykuł o długości 200 słów i zapisz go w swoich notatkach.", "ai-prompt-coding-content": "Wyodrębnianie zawartości strony internetowej https://github.com/blinko-space/blinko", "stop-task": "Zatrzymaj zadanie", "processing": "Przetwarzanie", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "Trwa zadanie przebudowy, czy ch<PERSON>z zrestartować?", "hide-blog-images": "Uk<PERSON>j obrazy na blogu", "ai-prompt-translation-content": "Sprawdź notatki No Tags w ciągu ostatnich dwóch dni i oznacz je.", "ai-prompt-delete-content": "Znajdź 2 zarchiwizowane notatki, podsumuj i zapisz je jako nowe notatki i usuń te dwie zarchiwizowane notatki", "older": "<PERSON><PERSON>", "newer": "Nowsze", "restore-this-version": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tę wersję", "Note History": "Uwaga historia", "View History Versions": "Zobacz wersje historii", "history-note-only": "Uwaga: ta historia zawiera tylko treść tekstową, a nie historię plików", "referenceResource": "Zasób referencyjny", "to-ask-ai": "Zapytać AI", "press-enter-to-select-first-result": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby w<PERSON><PERSON><PERSON> pierwszy wynik", "ask-ai": "Zapytaj AI", "ask-blinko-ai-about-this-query": "Zapytaj Blinko AI o to zapytanie", "search-or-ask-ai": "<PERSON><PERSON><PERSON>, ustawienia lub zapytaj AI ...", "plugin": "W<PERSON>cz<PERSON>", "editor-preview": "redaktor", "auto-add-tags": "Auto Dodaj tagi", "add-as-comment": "<PERSON><PERSON><PERSON> jako k<PERSON>", "choose-what-to-do-with-ai-results": "<PERSON><PERSON><PERSON><PERSON>, co zrobić z wynikami AI", "ai-post-processing-mode": "AI Tryb przetwarzania końcowego", "ai-post-processing-prompt": "Komentarz zachęcający do post-processingu AI", "ai-generate-emoji": "", "ai-generating-emoji": "", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "Przeanalizuj następującą zawartość notatki i zasugeruj odpowiednie znaczniki i podaj krótkie podsumowanie", "api-key": "", "content-generated-by-ai": "<PERSON><PERSON><PERSON>ć generowana przez AI", "date-range": "", "days-ago": "", "define-custom-prompt-for-ai-to-process-notes": "Obsługuj AI, aby sko<PERSON>ć aktualną notatkę. Na przykład: Podsumuj zawartość notatki. <PERSON><PERSON><PERSON> zawartość notatki ma mniej niż 10 słów, proszę ją dla mnie poprawić.", "enter-custom-prompt-for-post-processing": "Wprowadź niestandardową monit o przetwarzanie końcowe", "enter-your-api-key": "", "hours-ago": "", "impoort-from-bko": "", "minutes-ago": "", "months-ago": "", "prompt-used-for-post-processing-notes": "Poniższy używany do notatek po przetwarzaniu", "weeks-ago": "", "years-ago": "", "to-search-tags": "S<PERSON>ć tagów", "app-upgrade-required": "Wymagana aktualizacja aplikacji", "current-app-version": "Aktualna wersja aplikacji", "required-app-version": "<PERSON><PERSON><PERSON>a wersja a<PERSON>likacji", "upgrade": "Aktualizacja", "online-search": "Wyszukiwanie online", "smart-edit": "Inteligentna <PERSON>ycja", "function-call-required": "Wymagane Wywołanie Funkcji", "smart-edit-prompt": "Inteligentna Zachęta do Edycji", "define-instructions-for-ai-to-edit-your-notes": "Możesz używać poleceń do manipulowania notatkami, na przykład: <PERSON><PERSON><PERSON> notatka zawiera link, podsumuj zawartość linku poniżej oryginalnej notatki i wygeneruj etykietę.", "rebuild-started": "Roz<PERSON>częto Odbudowę", "rebuild-stopped-by-user": "Odbudowa zatrzymana przez użytkownika", "random-mode": "Losowy Spacer", "related-notes": "Powiązane notatki", "no-related-notes-found": "Nie znaleziono powiązanych notatek", "advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rerank-model-description": "Określ model do zmiany kolejności wyników wektorowych w celu poprawy dokładności wyszukiwania", "rerank-model": "Model przesortowania", "rerank": "Przegrupuj", "use-custom-rerank-endpoint-description": "<PERSON><PERSON> wł<PERSON>, punkty końcowe i klucze API wbudowanego modelu zostaną przeporządkowane.", "use-embedding-endpoint": "Użyj punktu końcowego osadzania", "rerank-score-description": "Ustaw próg punktacji dla modelu przestawień, poni<PERSON><PERSON> którego wyniki będą filtrowane.", "public-share": "Udział publiczny", "internal-share": "<PERSON><PERSON><PERSON><PERSON>", "no-team-members-found": "Nie znaleziono członków zespołu", "selected-users": "Wybrani użytkownicy", "tags-prompt": "Tagi Wskazówka", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "Komunikat używany do automatycznego generowania tagów. Je<PERSON>li zostawiony pusty, zostanie użyty domyślny komunikat.", "generate-low-permission-token": "Wygeneruj token o niskich uprawnieniach", "low-permission-token-desc": "Tokeny o niskich uprawnieniach mogą uzyskać dostęp tylko do punktu końcowego ​upsertNote i punktu końcowego czatu AI. Nie mają one dostępu do informacji o twoim koncie ani do innych notatek. Jest to idealne rozwiązanie na przykład dla botów Telegramu lub botów WeChat, gdy chcesz upewnić się, że nie mają one dostępu do żadnych innych notatek.", "this-token-is-only-displayed-once-please-save-it-properly": "Ten token jest wy<PERSON><PERSON><PERSON><PERSON>y tylko raz, proszę go od<PERSON>wiednio zap<PERSON>ć.", "refresh-model-list": "<PERSON><PERSON><PERSON> modeli", "please-set-the-embedding-model": "<PERSON><PERSON><PERSON> model <PERSON><PERSON><PERSON><PERSON>", "blinko-endpoint": "Blinko punkt końcowy", "enter-blinko-endpoint": "URL Twojego wdrożenia Blinko", "login-failed": "<PERSON><PERSON><PERSON><PERSON>", "verification-failed": "uwierzytelnianie nie powiodło się", "download-success": "Pobieranie udane", "download-failed": "Pobieranie nie powiodło się", "downloading": "Pobieranie", "hide-pc-editor": "<PERSON>k<PERSON>j edytor na <PERSON>.", "import-from-markdown": "Importuj z pliku Markdown", "import-from-markdown-tip": "Importuj pojedynczy plik .md lub archiwum .zip zawierające pliki .md.", "not-a-markdown-or-zip-file": "To nie jest plik Markdown ani zip. Proszę wybrać plik .md lub .zip.", "todo": "Pełnomocnictwo / Załatwienie w czyimś imieniu", "restore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "complete": "Zakończone", "today": "D<PERSON>ś", "yesterday": "<PERSON><PERSON><PERSON><PERSON>", "common.refreshing": "Odświeżanie", "common.releaseToRefresh": "Z<PERSON>lnij, aby odświeżyć", "common.pullToRefresh": "Przeciągnij do odświeżenia", "edit-message-warning": "Edycja tej wiadomości usunie wszystkie następujące po niej rekordy rozmów i ponownie wygeneruje odpowiedź AI.", "enter-your-message": "Wprowadź swoją wiadomość", "set-deadline": "Ustawienie terminu zakończenia.", "expired": "Wygasło.", "expired-days": "Przeterminowane o {{count}} dni.", "expired-hours": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} god<PERSON>.", "expired-minutes": "Przedawnione o {{count}} minut.", "days-left": "{{count}} dni później", "hours-left": "{{count}} <PERSON><PERSON> p<PERSON>", "minutes-left": "{{count}} minut p<PERSON>", "about-to-expire": "Wkrótce wygaśnie.", "1-day": "1 dzień", "1-week": "<PERSON><PERSON>", "1-month": "<PERSON><PERSON>", "quick-select": "Szybki wybór", "import-ai-configuration": "Importuj konfigurację AI", "would-you-like-to-import-this-configuration": "<PERSON><PERSON> ch<PERSON> z<PERSON>mpo<PERSON> tę konfigurację AI?", "detected-ai-configuration-to-import": "Wykryto konfigurację AI do zaimportowania", "importing": "Importowanie", "cache-cleared-successfully": "Pamięć podręczna została pomyślnie wyczyszczona! Strona zostanie automatycznie przeładowana.", "failed-to-clear-cache": "Nie udało się wyczyścić pamięci podręcznej przeglądarki. Spróbuj odświeżyć ręcznie (Ctrl+Shift+R).", "select-deployment": "Wybierz wdrożenie", "deployment-name": "Nazwa wdrożenia", "please-set-the-api-endpoint": "<PERSON><PERSON><PERSON> us<PERSON>wi<PERSON> punkt końcowy API", "please-set-the-api-key": "<PERSON><PERSON><PERSON> us<PERSON> k<PERSON>"}