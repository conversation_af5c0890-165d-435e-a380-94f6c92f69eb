{"blinko": "閃念", "notes": "筆記", "resources": "資源", "archived": "歸檔", "settings": "設置", "total-tags": "所有標籤", "search": "搜索...", "i-have-a-new-idea": "我有一個新想法...", "note": "筆記", "archive": "歸檔", "convert-to": "轉換為", "delete": "刪除", "edit": "編輯", "hello": "你好", "multiple-select": "多選", "recovery": "恢復", "items": "項目", "confirm-to-delete": "確認刪除！", "in-progress": "進行中...", "operation-failed": "操作失敗。", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "此操作會刪除相關標籤，且無法恢復，請確認", "your-changes-have-been-saved": "您的更改已保存！", "add-tag": "添加標籤", "cancel": "取消", "confrim": "確認", "no-data-here-well-then-time-to-write-a-note": "沒有數據哦~", "basic-information": "基本資訊", "language": "語言", "name": "名稱", "preference": "偏好", "theme": "主題", "change-type": "更改類型", "insert-hashtag": "插入標籤", "bulleted-list": "列表", "insert-codeblock": "插入程式碼塊", "insert-sandpack": "插入沙袋", "insert-table": "插入表格", "numbered-list": "編號列表", "check-list": "選擇列表", "upload-file": "上傳文件", "delete-confirm": "刪除確認", "delete-success": "刪除成功", "this-operation-will-be-delete-resource-are-you-sure": "該操作將刪除資源，你確定嗎？", "confirm": "確認", "update-successfully": "更新成功", "create-successfully": "成功創建", "all-notes-have-been-loaded": "已全部加載{{items}}條筆記", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "嗨，{{name}}！我可以為您搜索筆記，今天有什麼可以幫您？", "ask-about-your-notes": "輸入關於筆記的問題", "use-ai": "使用Blinko ai", "model-provider": "AI服務商", "api-endpoint": "接口地址", "must-start-with-http-s-or-use-api-openai-as-default": "必須以 http(s):// 開頭，或默認使用 /api/openai", "user-custom-openai-api-key": "用戶自定義 OpenAI Api 密鑰", "user-custom-azureopenai-api-instance": "Azure OpenAI 虛擬實例名稱", "user-custom-azureopenai-api-deployment": "Azure OpenAI 部署名稱", "user-custom-azureopenai-api-version": "API 版本", "ai-model": "人工智能模型", "logout": "註銷", "user-or-password-error": "用戶或密碼錯誤", "username": "用戶名", "enter-your-name": "輸入您的姓名", "password": "密碼", "need-to-create-an-account": "需要創建帳戶？", "sign-up": "註冊", "sign-in": "登錄", "enter-your-password": "輸入密碼", "nickname": "暱稱", "change-user-info": "更改用戶信息", "rest-user-password": "重置用戶密碼", "confirm-password": "確認密碼", "confirm-your-password": "確認密碼", "enter-your-username": "輸入您的用戶名", "keep-sign-in": "保持登錄", "recording": "錄音", "required-items-cannot-be-empty": "所需項目不能為空", "the-two-passwords-are-inconsistent": "兩個密碼不一致", "create-successfully-is-about-to-jump-to-the-login": "創建成功，即將跳轉到登錄頁面", "already-have-an-account-direct-login": "已有帳戶？直接登錄", "no-tag-found": "未找到標籤", "new-version-detected-click-to-get-the-latest-version": "🎉檢測到新版本，點擊獲取最新版本", "schedule-task": "定時任務", "schedule-back-up": "定時備份", "every-half-year": "每半年", "every-three-month": "每三個月", "every-month": "每月", "every-week": "每周", "every-day": "每天", "import": "導入", "import-from-bko": "從.bko文件導入", "not-a-bko-file": "不是 bko 文件", "convert-to-note": "轉換為筆記", "convert-to-blinko": "轉換為閃念", "reviewed": "已回顧", "congratulations-youve-reviewed-everything-today": "恭喜你，今天你已經回顧了所有內容", "name-db": "名稱", "schedule": "計劃", "last-run": "最後一次運行", "backup-file": "備份文件", "status": "狀態", "running": "運行中", "stopped": "已停止", "show-navigation-bar-on-mobile": "移動端隱藏導航欄", "schedule-archive-blinko": "定時歸檔閃念", "there-are-no-resources-yet-go-upload-them-now": "還沒有資源，現在就去上傳吧", "daily-review": "每日回顧", "detail": "詳情", "total": "全部", "enter-send-shift-enter-for-new-line": "Enter發送，Shift+Enter 換行", "show-less": "收起", "show-more": "展開", "top": "置頂", "cancel-top": "取消置頂", "save": "保存", "created-in": "创建于", "set-as-public": "设置为公开", "unset-as-public": "取消公开", "no-tag": "无标签", "with-link": "带链接", "has-file": "有文件", "created-at": "在", "create-user": "创建用户", "action": "操作", "edit-user": "编辑用户", "import-from-memos-memos_prod-db": "从备忘录导入（memos_prod.db）", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "导出 memos_prod.db 时，请关闭备忘录容器，以免丢失部分数据。", "go-to-share-page": "转到分享页面", "import-done": "导入完成", "rebuilding-embedding-progress": "重建 嵌入 进展", "rebuild-embedding-index": "重建嵌入索引", "rebuild": "重建", "notes-imported-by-other-means-may-not-have-embedded-vectors": "通过其他方式导入的票据可能没有嵌入向量", "order-by-create-time": "按创建时间排序", "time-format": "时间格式", "version": "版本", "new-version-available": "新版本可用", "storage": "存储", "local-file-system": "本地文件系统", "object-storage": "对象存储", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "除 GPT 模型外，还需要确保可以调用", "speech-recognition-requires-the-use-of": "语音识别需要使用", "ai-expand": "AI扩写", "ai-polish": "AI润色", "accept": "接受", "reject": "拒绝", "stop": "停止", "card-columns": "卡片列", "select-a-columns": "选择列", "width-less-than-1024px": "宽度小于 1024px", "width-less-than": "宽度小于", "small-device-card-columns": "小型设备卡列", "medium-device-card-columns": "中尺寸设备卡列", "large-device-card-columns": "大尺寸设备卡列", "device-card-columns": "设备卡列", "columns-for-different-devices": "适用于不同设备的栏目", "mobile": "移动电话", "tablet": "平板电脑", "desktop": "台式机", "chars": "字符数", "text-fold-length": "文本折叠长度", "title-first-line-of-the-text": "标题（文本第一行）", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "内容（文本的其余部分，如果文本长度超过规定长度）", "ai-tag": "AI标签", "article": "文章", "embedding-model": "嵌入模式", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "如果您有大量笔记，您可能会消耗一定数量的代币", "force-rebuild": "强制重建", "force-rebuild-embedding-index": "强制重建将完全重建已编制索引的所有数据", "embedding-model-description": "切换嵌入式模型后必须重建索引", "top-k-description": "最终返回文件的最大数量", "embedding-score-description": "查询的相似性阈值通常是欧氏总和距离", "embedding-lambda-description": "查询结果多样性加权参数", "update-tag-icon": "更新标签图标", "delete-only-tag": "只删除标签", "delete-tag-with-note": "删除标签和笔记", "update-tag-name": "更新标签名称", "thinking": "思考中...", "select-all": "全部选择", "deselect-all": "取消全选", "insert-before": "插入前", "insert-after": "插入内容后", "update-name": "更新名称", "ai-emoji": "AI表情", "custom-icon": "自定义图标", "ai-enhanced-search": "人工智能增强搜索", "preview-mode": "预览模式", "source-code": "源码", "camera": "照相机", "reference": "引用", "reference-note": "参考说明", "source-code-mode": "源码模式", "heading": "标题", "paragraph": "段落", "quote": "引用", "bold": "粗体", "remove-italic": "删除斜体", "underline": "下划线", "remove-bold": "删除粗体", "italic": "斜体", "remove-underline": "删除下划线", "select-block-type": "选择区块类型", "block-type-select-placeholder": "区块类型", "trash": "回收站", "custom-path": "自定义路径", "page-size": "每页加载数", "toolbar-visibility": "工具栏可见性", "always-hide-toolbar": "始终隐藏", "always-show-toolbar": "始终显示", "hide-toolbar-on-mobile": "在移动设备上隐藏", "select-toolbar-visibility": "选择工具栏可见性", "select-a-time-format": "选择时间格式", "enter-code-shown-on-authenticator-app": "输入验证器应用程序上显示的代码", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "打开第三方身份验证应用程序，输入屏幕上显示的密码", "two-factor-authentication": "双因素验证", "scan-this-qr-code-with-your-authenticator-app": "使用身份验证程序扫描此 QR 码", "or-enter-this-code-manually": "或手动输入此代码：", "verify": "验证", "about": "关于", "upload": "上传", "days": "天", "select-model": "选择型号", "select-deployment": "选择部署", "select-model-provider": "选择模型提供商", "allow-register": "允许注册", "access-token": "访问令牌", "bucket": "桶", "region": "地区", "access-key-secret": "访问密钥", "access-key-id": "访问密钥ID", "share-and-copy-link": "共享和复制链接", "copy-share-link": "复制共享链接", "endpoint": "端点", "export-format": "导出格式", "export": "导出", "time-range": "时间范围", "all": "全部", "exporting": "正在导出...", "has-image": "有图像", "has-link": "有链接", "filter-settings": "过滤器设置", "tag-status": "标签状态", "all-notes": "所有说明", "with-tags": "带标签", "without-tags": "无标签", "select-tags": "选择标签", "additional-conditions": "附加条件", "apply-filter": "应用过滤器", "to": "至", "start-date": "开始日期", "end-date": "结束日期", "reset": "重置", "no-condition": "无条件", "public": "公开的", "ai-model-tooltip": "输入要使用的型号名称，例如 gpt-3.5-turbo、gpt-4、gpt-4o、gpt-4o-mini", "user-custom-azureopenai-api-deployment-tooltip": "输入要使用的部署名称，如 gpt-4o", "ollama-ai-model-tooltip": "输入要使用的模型名称，如 llama3.2", "ollama-default-endpoint-is-http-localhost-11434": "Ollama 默认端点为 http://localhost:11434", "your-azure-openai-instance-name": "您的 Azure OpenAI 实例名称", "ai-generate-emoji": "", "ai-generating-emoji": "", "api-key": "", "date-range": "", "days-ago": "", "enter-your-api-key": "", "hours-ago": "", "impoort-from-bko": "", "minutes-ago": "", "months-ago": "", "superadmin": "", "user": "", "weeks-ago": "", "years-ago": "", "2fa-setup-successful": "2FA 设置成功", "align-center": "中心", "align-left": "左侧", "align-right": "对", "alternate-text": "备用文本", "both": "兩個都", "check": "任务清单", "close": "关闭", "code": "代码块", "code-theme": "代码块主题预览", "column": "专栏", "comment": "评论", "content-theme": "内容主题预览", "copied": "复制的", "copy": "复制", "dark-mode": "黑暗模式", "delete-column": "删除行", "delete-row": "删除栏", "devtools": "开发工具", "down": "向下", "download-tip": "浏览器不支持下载功能", "edit-mode": "切换编辑模式", "emoji": "表情符号", "exclude-tag-from-embedding": "排除标记内容", "exclude-tag-from-embedding-desc": "选择一个标记，将其相关笔记排除在人工智能嵌入向量生成之外", "exclude-tag-from-embedding-tip": "带有此标记的笔记将被排除在人工智能嵌入处理之外", "file-type-error": "文件类型错误", "follow-system": "遵循系统", "footnote-ref": "参考脚注", "fullscreen": "切换全屏", "generate": "生成", "heading1": "标题 1", "heading2": "标题 2", "heading3": "标题 3", "heading4": "标题 4", "heading5": "标题 5", "heading6": "标题 6", "headings": "标题", "help": "帮助", "image-url": "图像 URL", "indent": "缩进", "info": "信息", "inline-code": "内联代码", "insert-column-left": "左侧插入 1", "insert-column-right": "插入 1 个右", "insert-row-above": "插入上文 1", "insert-row-below": "下面插入 1", "instant-rendering": "即时渲染", "light-mode": "浅色模式", "line": "线路", "link": "链接", "link-ref": "链接参考", "list": "列表", "more": "更多信息", "name-empty": "名称为空", "ordered-list": "订单列表", "original-password": "原始密码", "outdent": "外齿", "outline": "概要", "over": "越过", "performance-tip": "实时预览需要 ${x}ms，您可以关闭它", "preview": "预览", "record": "开始记录/结束记录", "record-tip": "设备不支持录音", "redo": "重做", "remove": "移除", "role": "角色", "row": "行", "spin": "旋转", "split-view": "分割视图", "strike": "下划线", "table": "表格", "text-is-not-empty": "文本（无空）", "title": "标题", "tooltip-text": "工具提示文本", "undo": "撤消", "up": "向上", "update": "更新", "updated-at": "更新于", "upload-error": "上传错误", "uploading": "上传...", "user-list": "用户列表", "wysiwyg": "所见即所得", "search-tags": "搜尋標籤", "insert-attachment-or-note": "插入至附件或備註？", "context": "正文", "paste-to-note-or-attachment": "您確定要貼上到內容或附件嗎？", "attachment": "附件", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "刪除後，所有用戶資料將被清空且無法恢復。", "upload-completed": "上傳完成", "upload-cancelled": "上傳已取消", "upload-failed": "上傳失敗", "import-from-bko-tip": "目前不支援上傳至 s3 進行還原。當您想要進行恢復操作時，請暫時停用 s3 選項。", "music-settings": "音樂設定", "enter-spotify-consumer-secret": "輸入 Spotify 用戶秘密", "spotify-consumer-secret": "Spotify 用戶端秘鑰", "enter-spotify-consumer-key": "輸入 Spotify 使用者金鑰", "spotify-consumer-key-tip": "以前用來取得 MP3 音樂封面", "spotify-consumer-key-tip-2": "從 https://developer.spotify.com/ 取得 API 金鑰", "edit-time": "編輯時間", "ai-write": "AI 寫", "download": "下載", "rename": "重新命名", "move-up": "提升", "cut": "剪辑", "paste": "貼上", "confirm-delete": "確認刪除", "confirm-delete-content": "您確定要刪除{{name}}嗎？此操作無法撤銷。", "folder-name": "資料夾名稱", "file-name": "檔案名稱", "operation-success": "操作成功", "cloud-file": "雲端檔案", "move-to-parent": "移至上層目錄", "no-resources-found": "找不到資源", "operation-in-progress": "正在進行操作", "new-folder": "新資料夾", "folder-name-required": "需要資料夾名稱", "folder-name-exists": "資料夾名稱已存在", "collapse": "收起", "show-all": "顯示全部", "sun": "日", "mon": "一", "thu": "四", "wed": "三", "fri": "五", "sat": "六", "heatMapTitle": "過去一年來的筆記熱點圖", "heatMapDescription": "顯示每天創建的筆記數量", "select-month": "選擇月份", "note-count": "筆記計數", "total-words": "總字數", "active-days": "積極的日子", "max-daily-words": "每日最大字數", "analytics": "分析", "tag-distribution": "標籤分佈", "other-tags": "其他標籤", "tue": "週二", "offline-status": "離線模式", "offline-title": "您已離線", "offline-description": "請檢查您的網際網路連接，然後再試一次。", "retry": "重試", "back-to-home": "返回首頁", "offline": "離線", "close-background-animation": "關閉背景動畫", "custom-bg-tip": "前往https://www.shadergradient.co/ 創建您自己的漸層背景。", "custom-background-url": "自訂背景", "share": "分享", "need-password-to-access": "需要密碼訪問", "password-error": "密碼錯誤", "cancel-share": "取消分享", "create-share": "創建分享", "share-link": "分享連結", "set-access-password": "設置存取密碼", "protect-your-shared-content": "保護您的共享內容", "access-password": "存取密碼", "select-date": "選擇日期", "expiry-time": "到期時間", "select-expiry-time": "選擇到期時間", "permanent-valid": "永久有效", "7days-expiry": "7天到期", "custom-expiry": "自訂到期", "30days-expiry": "30天到期", "share-link-expired": "分享連結已過期", "share-link-expired-desc": "此分享已過期，請聯繫管理員重新分享！", "shared": "共享", "internal-shared": "內部共享", "edited": "已編輯", "move-down": "向下移動", "provider-id": "提供者ID", "provider-name": "供應商名稱", "well-known-url": "知名的URL", "authorization-url": "授權網址", "token-url": "令牌網址", "userinfo-url": "使用者資訊網址", "scope": "範圍", "client-id": "客戶 ID", "client-secret": "客戶端秘密", "sso-settings": "SSO 設定", "oauth2-providers": "Oauth2 提供者", "add-oauth2-provider": "新增 Oauth2 供應商", "add-provider": "新增提供者", "edit-oauth2-provider": "編輯 Oauth2 提供者", "confirm-delete-provider": "確認刪除提供者", "please-select-icon-from-iconify": "請從Iconify中選擇圖示。", "provider-icon": "提供者圖示", "select-provider-template": "選擇供應商模板", "provider-template": "提供者範本", "please-add-this-url-to-your-oauth-provider-settings": "請將此URL新增到您的OAuth提供者設定中。", "redirect-url": "重定向URL", "sign-in-with-provider": "使用{{ provider }}登入", "community": "社區", "theme-color": "主題顏色", "link-account": "連結帳號", "select-account": "選擇帳戶", "link-account-warning": "請注意，如果您連結您的帳戶，當前帳戶中的任何數據將不會同步至已連結的帳戶。", "unlink-account": "解除帳戶連結", "unlink-account-tips": "您確認透過此帳戶可以存取所有聯繫嗎？", "login-type": "登入類型", "close-daily-review": "結束每日檢查", "max-home-page-width": "最大首頁寬度", "max-home-page-width-tip": "如果設為 0，則是最大寬度。", "no-comments-yet": "目前尚無評論", "author": "作者", "from": "從", "reply-to": "回覆", "hub": "據點", "home-site": "首頁", "use-blinko-hub": "使用 <PERSON><PERSON><PERSON>", "full-screen": "全螢幕", "exit-fullscreen": "退出全螢幕", "no-note-associated": "沒有附帶的備註", "insert-context": "插入至內文", "follow": "跟隨", "follower": "追隨者", "following": "遵循", "admin": "網站管理員", "site-url": "Blinko 網站網址", "unfollow": "取消關注", "join-hub": "加入 Hub", "refresh": "更新", "comment-notification": "評論通知", "follow-notification": "跟隨通知", "followed-you": "跟隨了你", "mark-all-as-read": "將全部標記為已讀", "no-notification": "沒有通知", "new-notification": "新通知", "notification": "通知", "backup-success": "備份成功🎉", "system-notification": "系統通知", "embedding-api-endpoint": "嵌入 API 端點", "embedding-api-key": "嵌入式 API 金鑰", "recommand": "推薦", "has-todo": "有待辦事項", "reference-by": "被引用", "hide-notification": "隱藏通知", "search-settings": "搜尋設定...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "這個操作將刪除選定的檔案，無法復原，請確認。", "plugin-settings": "外掛程式設定", "installed-plugins": "已安裝", "marketplace": "市場", "local-development": "本地開發", "add-local-plugin": "新增本地插件", "local-plugin": "本地插件", "uninstall": "解除安裝", "install": "安裝", "downloads": "下載", "plugin-updated": "插件已更新", "plugin-update-failed": "插件更新失敗", "plugin-connection-failed": "外掛程式連線失敗", "disconnect": "中文（台灣）：斷開連線", "local-development-description": "新增本地開發外掛程式並進行除錯。", "ai": "人工智慧", "ai-chat-box-notes": "以下是为您检索到的相关笔记", "add-to-note": "添加到筆記", "add-to-blinko": "加入到Blinko", "no-title": "無標題", "search-blinko-content-or-help-create": "搜索blinko内容或帮助创建...", "conversation-history": "對話歷史", "new-conversation": "新聊天", "knowledge-base-search": "知識庫搜尋", "add-tools-to-model": "在線搜索或允許AI呼叫blinko api", "clear-current-content": "清除當前內容", "welcome-to-blinko": "歡迎，{{name}}", "coding": "編碼", "ai-prompt-writing": "您是一名專業作家，請根據用戶提供的主題撰寫一篇專業文章。", "writing": "寫作", "ai-prompt-translation": "您是一名專業的翻譯員，請將用戶提供的文本翻譯成{{lang}}。", "ai-prompt-coding": "您是一名專業的程式設計師，請根據用戶提供的主題編寫一個簡單的Python程式。", "translation": "翻譯", "first-char-delay": "首個字符延遲", "total-tokens": "總代幣", "check-connect": "查看", "check-connect-error": "連接失敗!", "check-connect-success": "檢查連線成功", "loading": "載入中", "embedding-dimensions": "嵌入維度", "embedding-dimensions-description": "您需要确保模型尺寸正确无误，并且在更改后您需要强制重建索引记录。", "deployment-name": "部署名稱", "ai-tools": "AI 工具", "tavily-api-key": "Tavily搜尋API密鑰", "tavily-max-results": "塔維利最大成果", "ai-prompt-writing-content": "撰寫一篇200字的文章並將其儲存在你的筆記中", "ai-prompt-coding-content": "提取https://github.com/blinko-space/blinko 網頁內容", "stop-task": "停止任務", "processing": "處理中", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "正在進行重建任務，您要重新啟動嗎？", "hide-blog-images": "隱藏博客圖像", "ai-prompt-translation-content": "在過去兩天中檢查無標籤筆記並標記它們。", "ai-prompt-delete-content": "查找2個存檔的筆記，總結並保存為新筆記，然後刪除這兩個存檔的筆記", "older": "较旧", "newer": "較新", "restore-this-version": "還原此版本", "Note History": "歷史記錄", "View History Versions": "查看歷史版本", "history-note-only": "注意：該歷史記錄僅包含文本內容，不包含文件歷史", "referenceResource": "參考資源", "to-ask-ai": "問AI", "press-enter-to-select-first-result": "按Enter選擇第一個結果", "ask-ai": "問AI", "ask-blinko-ai-about-this-query": "向Blinko AI詢問此查詢", "search-or-ask-ai": "搜索說明，設置或詢問AI ...", "plugin": "插件", "editor-preview": "編輯", "auto-add-tags": "自動添加標籤", "add-as-comment": "添加為評論", "choose-what-to-do-with-ai-results": "選擇如何使用AI結果", "ai-post-processing-mode": "AI後處理模式", "ai-post-processing-prompt": "AI 後處理評論提示", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "分析以下註釋內容並建議適當的標籤，並提供簡短的摘要", "content-generated-by-ai": "AI產生的內容", "define-custom-prompt-for-ai-to-process-notes": "操作AI對當前筆記進行評論。例如：請總結筆記的內容。如果筆記內容少於10個字，請替我改寫一下。", "enter-custom-prompt-for-post-processing": "輸入自定義提示進行發布處理", "prompt-used-for-post-processing-notes": "用於後處理筆記的提示词", "rebuild-in-progress": "重建正在進行中", "rest-user-info": "修改用戶信息", "setting": "", "spotify-consumer-key": "Spotify API密鑰", "enable-ai-post-processing": "啟用AI後處理", "automatically-process-notes-after-creation-or-update": "創建後自動處理筆記", "can-generate-summaries-tags-or-perform-analysis": "可以在性能分析上生成摘要標籤", "ai-post-processing": "AI後處理", "model-list-updated": "模型列表已更新", "to-search-tags": "搜尋標籤", "app-upgrade-required": "需要升級應用程式", "current-app-version": "當前APP版本", "required-app-version": "需要的APP版本", "upgrade": "升級", "online-search": "線上搜尋", "smart-edit": "智能編輯", "function-call-required": "需要調用函數", "smart-edit-prompt": "智能編輯提示", "define-instructions-for-ai-to-edit-your-notes": "您可以使用提示來操作筆記，例如：如果答案中包含連接網址，將連結內容摘要放在原始答案下方並產生標籤。", "rebuild-started": "重建開始", "random-mode": "隨機漫步", "related-notes": "相關筆記", "no-related-notes-found": "未找到相關筆記", "advanced": "先進", "rerank-model-description": "指定一个模型以重新排序向量结果，以提高搜索精度", "rerank-model": "<PERSON><PERSON>模型", "rerank": "重新排名", "use-custom-rerank-endpoint-description": "啟用後，嵌入式模型的端點和API密鑰將會被重新排序。", "use-embedding-endpoint": "使用嵌入端點", "rerank-score-description": "為重新排序模型設定一個分數門檻，低於此門檻的結果將被過濾。", "public-share": "公開分享", "internal-share": "內部分享", "no-team-members-found": "找不到團隊成員", "selected-users": "選定的使用者", "tags-prompt": "標籤提示", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "用於自動生成標籤的提示。如果設置為空，將使用預設的提示。", "generate-low-permission-token": "生成低權限令牌", "low-permission-token-desc": "低權限令牌只能訪問upsertNote端點和AI聊天端點。它們無法訪問您的帳戶信息或其他筆記。這非常適合於Telegram機器人或微信機器人等使用案例，您想要確保它們無法訪問任何其他筆記。", "this-token-is-only-displayed-once-please-save-it-properly": "此令牌只顯示一次，請妥善保存它", "refresh-model-list": "獲取模型列表", "please-set-the-embedding-model": "請設定嵌入模型", "please-set-the-api-endpoint": "請設定 API 端點", "please-set-the-api-key": "請設定 API 金鑰", "blinko-endpoint": "Blinko端點", "enter-blinko-endpoint": "你的Blinko部署的網址", "login-failed": "登入失敗", "verification-failed": "認證失敗", "download-failed": "下載失敗", "downloading": "下載中", "hide-pc-editor": "隱藏PC端編輯器", "import-from-markdown": "從Markdown文件導入", "import-from-markdown-tip": "從單一.md 文件或包含 .md 文件的 .zip 存檔匯入", "not-a-markdown-or-zip-file": "不是Markdown或zip檔案。請選擇.md或.zip檔案。", "server": "伺服器", "client": "客戶端", "new-server-version-available": "伺服器有新版本可用", "new-client-version-available": "客戶端有新版本可用", "todo": "代辦", "restore": "恢復", "complete": "完成", "today": "今天", "yesterday": "昨天", "common.refreshing": "正在更新中", "common.releaseToRefresh": "鬆開以更新", "common.pullToRefresh": "下拉更新", "edit-message-warning": "編輯此訊息將清除後續的所有對話記錄，並重新生成AI回覆。", "enter-your-message": "輸入您的訊息", "set-deadline": "設定截止日期", "expired": "已過期", "expired-days": "過期{{count}}天", "expired-hours": "過期{{count}}小時", "days-left": "{{count}}天後", "hours-left": "{{count}}小時後", "minutes-left": "{{count}}分鐘後", "about-to-expire": "即將到期", "1-day": "1天", "1-week": "一週", "1-month": "一個月", "quick-select": "快捷選擇", "import-ai-configuration": "匯入AI配置", "would-you-like-to-import-this-configuration": "你是否想要匯入該AI配置？", "detected-ai-configuration-to-import": "偵測到AI配置待導入", "importing": "匯入中", "cache-cleared-successfully": "快取已清除成功！頁面將自動重新載入。", "failed-to-clear-cache": "清除瀏覽器快取失敗。請嘗試手動重新整理 (Ctrl+Shift+R)。"}