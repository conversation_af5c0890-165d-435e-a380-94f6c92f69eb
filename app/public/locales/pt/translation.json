{"enter-spotify-consumer-key": "Insira a Chave do Consumidor do Spotify", "spotify-consumer-key-tip": "Costumava obter a capa de músicas em mp3.", "spotify-consumer-key-tip-2": "Obtenha a Chave da API em https://developer.spotify.com/", "blinko": "Blinko", "notes": "Notas", "resources": "Recursos", "archived": "Arquivado", "settings": "Definições", "total-tags": "TOTAL TAGS", "search": "Procurar...", "i-have-a-new-idea": "Tenho uma nova ideia...", "ai-model": "Modelo de IA", "all-notes-have-been-loaded": "<PERSON><PERSON> as notas {{items}} foram carregadas", "archive": "Arquivo", "ask-about-your-notes": "Pergun<PERSON> sobre as suas notas", "backup-file": "FICHEIRO DE CÓPIA DE SEGURANÇA", "bulleted-list": "Lista com marcadores", "cancel": "<PERSON><PERSON><PERSON>", "cancel-top": "Cancelar topo", "change-type": "Tipo de modificação", "change-user-info": "Alterar informações do utilizador", "confirm-to-delete": "Confirmar a eliminação!", "congratulations-youve-reviewed-everything-today": "já reviu tudo hoje.", "convert-to-note": "Converter em nota", "create-successfully": "Criar com êxito", "daily-review": "Revisão diária", "delete": "Eliminar", "delete-confirm": "<PERSON><PERSON><PERSON> Confirmar", "delete-success": "Eliminar com êxito", "edit": "<PERSON><PERSON>", "enter-send-shift-enter-for-new-line": "Introdu<PERSON>r enviar, Shift+Enter para nova linha", "enter-your-name": "Introduza o seu nome", "enter-your-password": "Introduza a sua palavra-passe", "enter-your-username": "Introduza o seu nome de utilizador", "every-day": "Todos os dias", "every-half-year": "Todos os semestres", "every-month": "Todos os meses", "every-week": "<PERSON><PERSON> as semanas", "hello": "O<PERSON><PERSON>", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "<PERSON><PERSON><PERSON> {{nome}}, posso procurar as notas para si, como posso ajudá-lo hoje?", "import-from-bko": "Importar de .bko", "import": "Importação", "in-progress": "Em curso...", "insert-codeblock": "Inserir codeBlock", "insert-hashtag": "Inserir hashtag", "insert-sandpack": "Inserir saco de areia", "insert-table": "<PERSON><PERSON><PERSON> tabela", "items": "artigos", "keep-sign-in": "<PERSON>ter o registo", "language": "Língua", "last-run": "ÚLTIMA CORRIDA", "logout": "<PERSON><PERSON><PERSON><PERSON>", "multiple-select": "<PERSON><PERSON><PERSON>", "must-start-with-http-s-or-use-api-openai-as-default": "Deve começar com http(s):// ou utilizar /api/openai como predefinição", "name": "Nome", "name-db": "NOME", "need-to-create-an-account": "Precisa de criar uma conta?", "new-version-detected-click-to-get-the-latest-version": "Nova versão detectada, clique para obter a versão mais recente", "nickname": "alcunha", "no-data-here-well-then-time-to-write-a-note": "Não há dados aqui? Então, é altura de escrever uma nota!", "no-tag-found": "Nenhuma etiqueta encontrada", "not-a-bko-file": "não é um ficheiro bko", "note": "<PERSON>a", "numbered-list": "Lista numerada", "operation-failed": "A operação falhou.", "password": "palavra-passe", "preference": "Preferência", "recording": "Registo", "required-items-cannot-be-empty": "Os itens obrigatórios não podem estar vazios", "rest-user-password": "Palavra-passe do utilizador restante", "running": "Em curso", "save": "Guardar", "schedule": "CALENDÁRIO", "schedule-archive-blinko": "Arquivo de horários Blinko", "schedule-back-up": "Programar a cópia de segurança", "schedule-task": "Programar tarefa", "show-less": "<PERSON><PERSON> menos", "show-more": "<PERSON><PERSON> mais", "show-navigation-bar-on-mobile": "Barra de navegação oculta no telemóvel", "sign-in": "<PERSON><PERSON><PERSON>", "sign-up": "Inscrever-se", "status": "ESTADO", "stopped": "Parado", "the-two-passwords-are-inconsistent": "As duas palavras-passe são inconsistentes", "theme": "<PERSON><PERSON>", "there-are-no-resources-yet-go-upload-them-now": "Ainda não existem recursos, carregue-os agora", "this-operation-will-be-delete-resource-are-you-sure": "Esta operação será um recurso de eliminação, tem a certeza?", "top": "Topo", "total": "Total", "update-successfully": "Atualizar com êxito", "upload-file": "<PERSON><PERSON><PERSON>", "use-ai": "Util<PERSON>r ai", "user-custom-openai-api-key": "Chave de Api OpenAI personalizada pelo utilizador", "user-custom-azureopenai-api-instance": "Nome da instância do Azure OpenAI", "user-custom-azureopenai-api-deployment": "Nome da implantação do Azure OpenAI", "user-custom-azureopenai-api-version": "Vers<PERSON> da API", "user-or-password-error": "Erro de utilizador ou de palavra-passe", "username": "nome de utilizador", "your-changes-have-been-saved": "As suas alterações foram guardadas!", "add-tag": "Adicionar etiqueta", "already-have-an-account-direct-login": "Já tem uma conta? Início de sessão direto", "api-endpoint": "Ponto final da API", "check-list": "Lista de controlo", "confirm": "Confirmar", "confirm-password": "Confirmar a palavra-passe", "confirm-your-password": "Confirmar a sua palavra-passe", "confrim": "Confirmar", "convert-to": "Converter para", "create-successfully-is-about-to-jump-to-the-login": "Criar com êxito, está prestes a passar para o início de sessão", "every-three-month": "De três em três meses", "model-provider": "<PERSON><PERSON> de fornecedor", "reviewed": "<PERSON><PERSON><PERSON>", "basic-information": "Informações básicas", "convert-to-blinko": "Converter para Blinko", "recovery": "Recuperação", "detail": "<PERSON><PERSON><PERSON>", "created-in": "C<PERSON><PERSON> em", "set-as-public": "Definir como público", "unset-as-public": "Não definido como público", "no-tag": "Sem etiqueta", "with-link": "Com ligação", "has-file": "<PERSON><PERSON>", "created-at": "<PERSON><PERSON><PERSON> em", "role": "Papel", "user-list": "Lista de utilizadores", "create-user": "<PERSON><PERSON>r utilizador", "action": "Ação", "original-password": "Palavra-passe original", "edit-user": "Editar utilizador", "import-from-memos-memos_prod-db": "Importar de Memos(memos_prod.db)", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "Ao exportar memos_prod.db, feche o contentor de memos para evitar a perda parcial de dados.", "go-to-share-page": "Ir para a página de partilha", "rebuild-embedding-index": "Reconstruir o índice de incorporação", "notes-imported-by-other-means-may-not-have-embedded-vectors": "As notas importadas por outros meios podem não ter vectores incorporados", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "Se tiver muitas notas, pode consumir um determinado número de fichas", "time-format": "Formato da hora", "version": "Vers<PERSON>", "new-version-available": "Nova versão disponível", "storage": "Armazenamento", "local-file-system": "Sistema de ficheiros local", "object-storage": "Armazenamento de objectos", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "Para além do modelo GPT, é necessário garantir que é possível invocar o modelo", "speech-recognition-requires-the-use-of": "O reconhecimento de voz requer a utilização de", "ai-expand": "Expandir a IA", "ai-polish": "Polaco AI", "accept": "Aceitar", "reject": "<PERSON><PERSON><PERSON><PERSON>", "stop": "<PERSON><PERSON>", "card-columns": "Colunas de cartões", "select-a-columns": "Selecionar uma coluna", "width-less-than-1024px": "Largura inferior a 1024px", "width-less-than": "Largura inferior a", "small-device-card-columns": "Colunas do cartão de dispositivo pequeno", "medium-device-card-columns": "Colunas do cartão de dispositivo médio", "large-device-card-columns": "Colunas grandes do cartão de dispositivo", "device-card-columns": "Colunas do cartão de dispositivo", "columns-for-different-devices": "Colunas para diferentes dispositivos", "mobile": "Telemóvel", "tablet": "Tablet", "desktop": "Ambiente de trabalho", "chars": "Caracteres", "text-fold-length": "Comprimento da dobra do texto", "title-first-line-of-the-text": "<PERSON><PERSON><PERSON><PERSON> (primeira linha do texto)", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "Conteúdo (resto do texto, se o texto for mais longo do que o comprimento)", "ai-tag": "Etiqueta AI", "article": "Artigo", "embedding-model": "Modelo de incorporação", "force-rebuild": "Reconstrução da força", "force-rebuild-embedding-index": "A reconstrução forçada irá reconstruir completamente todos os dados que foram indexados", "embedding-model-description": "O índice deve ser reconstruído após a mudança de modelos incorporados", "top-k-description": "Número máximo de documentos eventualmente devolvidos", "embedding-lambda-description": "Resultado da consulta Parâmetro de ponderação da diversidade", "update-tag-icon": "Atualizar o ícone da etiqueta", "delete-only-tag": "Eliminar apenas a etiqueta", "delete-tag-with-note": "Eliminar etiqueta com nota", "update-tag-name": "Atualizar o nome da etiqueta", "thinking": "A pensar...", "select-all": "Selecionar tudo", "deselect-all": "<PERSON><PERSON><PERSON> tudo", "insert-before": "Inserir antes de", "insert-after": "Inserir a<PERSON>ós", "update-name": "Atualizar nome", "ai-emoji": "<PERSON>", "custom-icon": "Ícone personalizado", "ai-enhanced-search": "Pesquisa optimizada por IA", "preview-mode": "Modo de pré-visualização", "source-code": "<PERSON><PERSON><PERSON> fonte", "reference": "Referência", "reference-note": "Nota de referência", "source-code-mode": "Modo de có<PERSON> fonte", "heading": "<PERSON><PERSON><PERSON><PERSON>", "paragraph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quote": "Citação", "bold": "Negrito", "remove-italic": "Remover itálico", "underline": "Sublinhar", "remove-bold": "Remover negrito", "italic": "Itálico", "remove-underline": "Remover o sublinhado", "select-block-type": "Selecionar o tipo de bloco", "block-type-select-placeholder": "Tipo de bloco", "trash": "lixo", "page-size": "<PERSON><PERSON><PERSON>", "toolbar-visibility": "Visibilidade da barra de ferramentas", "always-hide-toolbar": "Esconder-se sempre", "always-show-toolbar": "Mostrar sempre", "hide-toolbar-on-mobile": "Ocultar no telemóvel", "select-toolbar-visibility": "Selecionar a visibilidade da barra de ferramentas", "select-a-time-format": "Selecionar um formato de hora", "enter-code-shown-on-authenticator-app": "Introduzir o código apresentado na aplicação de autenticação", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "Abra a sua aplicação de autenticação de terceiros e introduza os códigosmostrados no ecrã", "two-factor-authentication": "Autenticação de dois factores", "scan-this-qr-code-with-your-authenticator-app": "Digitalize este código QR com a sua aplicação de autenticação", "or-enter-this-code-manually": "Ou introduza este código manualmente:", "verify": "Verificar", "about": "Sobre", "upload": "<PERSON><PERSON><PERSON>", "days": "<PERSON><PERSON>", "select-model": "Selecionar modelo", "select-model-provider": "Selecionar fornecedor de modelos", "allow-register": "<PERSON><PERSON><PERSON> registo", "access-token": "<PERSON><PERSON> de acesso", "bucket": "Balde", "region": "Região", "access-key-secret": "Chave de acesso secreta", "access-key-id": "ID da chave de acesso", "share-and-copy-link": "Partilhar e copiar a ligação", "copy-share-link": "Copiar a ligação de partilha", "endpoint": "Ponto final", "export-format": "Formato de exportação", "export": "Exportação", "time-range": "Intervalo de tempo", "all": "Todos", "exporting": "Exportar...", "has-image": "Tem imagem", "has-link": "Tem ligação", "filter-settings": "Definições do filtro", "tag-status": "Estado da etiqueta", "all-notes": "<PERSON><PERSON> as notas", "with-tags": "Com etiquetas", "without-tags": "Sem etiquetas", "select-tags": "Selecionar etiquetas", "additional-conditions": "Condições adicionais", "apply-filter": "Aplicar filtro", "to": "Para", "start-date": "Data de início", "end-date": "Data final", "reset": "Reiniciar", "no-condition": "Sem condição", "public": "Público", "ai-model-tooltip": "Introduza o nome do modelo a utilizar, por exemplo, gpt-3.5-turbo, gpt-4, gpt-4o, gpt-4o-mini", "user-custom-azureopenai-api-deployment-tooltip": " Introduza o nome da implantação a utilizar, por exemplo, gpt-4o", "ollama-ai-model-tooltip": "Introduza o nome do modelo a utilizar, por exemplo, llama3.2", "ollama-default-endpoint-is-http-localhost-11434": "O ponto de extremidade predefinido da Ollama é http://localhost:11434", "your-azure-openai-instance-name": "O nome da sua instância do Azure OpenAI", "search-tags": "Tags de pesquisa", "insert-attachment-or-note": "Inserir em anexo ou nota?", "context": "Contexto", "paste-to-note-or-attachment": "Tem certeza de que deseja colar no contexto ou no anexo?", "attachment": "Anexo", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "Após a exclusão, todos os dados do usuário serão apagados e não poderão ser recuperados.", "upload-completed": "Carregamento concluído", "upload-cancelled": "<PERSON><PERSON>", "upload-failed": "<PERSON>al<PERSON> a<PERSON>ar", "import-from-bko-tip": "O envio para o s3 para recuperação não é suportado neste momento. Por favor, desative temporariamente a opção s3 quando desejar recuperar.", "edit-time": "Tempo de Edição", "ai-write": "IA Escreva", "download": "Baixar", "rename": "Renomear", "move-up": "Subir", "cut": "Corte", "paste": "Colar", "confirm-delete": "Confirmar Excluir", "confirm-delete-content": "Tem certeza de que deseja excluir {{name}}? Essa ação não pode ser desfeita.", "folder-name": "<PERSON>me da Pasta", "file-name": "Nome do arquivo", "operation-success": "Operação bem-sucedida", "cloud-file": "Arquivo na Nuvem", "move-to-parent": "Mover Para o Pai", "no-resources-found": "Recursos não encontrados", "operation-in-progress": "Operação em andamento", "new-folder": "Nova Pasta", "folder-name-exists": "Nome da Pasta Já Existe", "folder-name-required": "Nome da pasta obrigatório", "collapse": "Colapso", "show-all": "<PERSON><PERSON>r tudo", "sun": "Sol", "mon": "Segunda-feira", "thu": "<PERSON>ui", "wed": "<PERSON>ua", "fri": "Sex", "sat": "Sábado", "heatMapTitle": "Mapa de calor das notas do último ano", "heatMapDescription": "Mostra o número de notas criadas por dia", "select-month": "Se<PERSON><PERSON>e o mês", "note-count": "Contagem de notas", "total-words": "<PERSON><PERSON><PERSON>", "active-days": "Dias Ativos", "max-daily-words": "<PERSON><PERSON><PERSON> Diá<PERSON>", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "tag-distribution": "Distribuição de Tags", "other-tags": "Outras etiquetas", "tue": "<PERSON>r.", "offline-status": "Modo offline", "offline-title": "Você está offline", "offline-description": "Por favor, verifique a sua conexão à internet e tente novamente.", "retry": "Tentar novamente", "back-to-home": "De volta para casa", "offline": "Offline", "close-background-animation": "Fechar Animação de Fundo", "custom-bg-tip": "Acesse https://www.shadergradient.co/ para criar seu próprio fundo de gradiente.", "custom-background-url": "Fundo Personalizado", "share": "Compartilhar", "need-password-to-access": "Acesso com senha é necessário", "password-error": "<PERSON><PERSON> <PERSON>", "cancel-share": "<PERSON><PERSON><PERSON>", "create-share": "<PERSON><PERSON><PERSON>", "share-link": "Compartilhar Link", "set-access-password": "<PERSON><PERSON><PERSON> se<PERSON> de <PERSON>", "protect-your-shared-content": "Proteja seu conteúdo compartilhado", "access-password": "<PERSON><PERSON>", "select-date": "Selecionar data", "expiry-time": "Tempo de validade", "select-expiry-time": "Selecionar Tempo de Expiração", "permanent-valid": "Válido <PERSON>e.", "7days-expiry": "Validade de 7 dias", "custom-expiry": "Validade personalizada", "30days-expiry": "30 Dias de Validade", "share-link-expired": "O link compartilhado expirou", "share-link-expired-desc": "Esta partilha expirou, por favor entre em contato com o administrador para recriar a partilha!", "shared": "Comp<PERSON><PERSON><PERSON><PERSON>", "internal-shared": "Compartilhado internamente", "edited": "Editado", "move-down": "Mover para baixo", "provider-id": "ID do Fornecedor", "provider-name": "Nome do Provedor", "well-known-url": "URL conhecido", "authorization-url": "URL de autorização", "token-url": "URL do token", "userinfo-url": "URL de Informações do Usuário", "scope": "Escopo", "client-id": "Identificação do cliente", "client-secret": "Segredo do Cliente", "sso-settings": "Configurações de SSO", "oauth2-providers": "Fornecedores do Oauth2", "add-oauth2-provider": "Adicionar Provedor <PERSON>h2", "add-provider": "<PERSON><PERSON><PERSON><PERSON>", "edit-oauth2-provider": "Editar Fornecedor Oauth2", "confirm-delete-provider": "Confirmar <PERSON><PERSON><PERSON>ir <PERSON>", "please-select-icon-from-iconify": "Por favor, selecione o ícone no Iconify.", "provider-icon": "Ícone do fornecedor", "select-provider-template": "Selecionar Modelo de Provedor", "provider-template": "<PERSON><PERSON>", "please-add-this-url-to-your-oauth-provider-settings": "Por favor, adicione este URL às configurações do seu provedor de OAuth.", "redirect-url": "Redirecionar url.", "sign-in-with-provider": "Entrar com {{provider}}", "community": "Comunidade", "theme-color": "<PERSON><PERSON> <PERSON>", "link-account": "Vincular Conta", "select-account": "Selecionar Conta", "link-account-warning": "Por favor, observe que se você vincular suas contas, os dados da conta atual não serão sincronizados com a conta vinculada.", "unlink-account": "Des<PERSON><PERSON>", "unlink-account-tips": "Você confirma o acesso a todas as associações com esta conta?", "login-type": "<PERSON><PERSON><PERSON>", "close-daily-review": "Revisão Diária <PERSON>", "max-home-page-width": "<PERSON><PERSON><PERSON> Máxi<PERSON> da Página Inicial", "max-home-page-width-tip": "Se configurado como 0, é a largura máxima.", "no-comments-yet": "<PERSON>da sem comentários", "author": "Autor", "from": "De", "comment": "<PERSON><PERSON><PERSON><PERSON>", "reply-to": "Responder a", "hub": "Concentrador", "home-site": "Site Inicial", "use-blinko-hub": "Use Blinko Hub\n\nUtilize o Blinko Hub", "full-screen": "Tela cheia", "exit-fullscreen": "Sair do modo de tela cheia", "no-note-associated": "Nenhuma nota associada", "insert-context": "Inserir no contexto", "follow": "<PERSON><PERSON><PERSON>", "follower": "<PERSON><PERSON><PERSON><PERSON>", "following": "<PERSON><PERSON><PERSON>", "admin": "Webmaster\n\nAdministrador de site", "site-url": "URL do site Blinko", "unfollow": "<PERSON><PERSON><PERSON>", "join-hub": "Participar no Hub", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "comment-notification": "Notificação de Comentário", "follow-notification": "Seguir notific<PERSON>", "followed-you": "segui você", "mark-all-as-read": "Marcar todos como lidos", "no-notification": "Sem notificação", "new-notification": "Nova notificação", "notification": "Notificação", "backup-success": "Sucesso do Backup🎉", "system-notification": "Notificação do sistema", "embedding-api-endpoint": "Incorporando o Ponto de Extremidade da API", "embedding-api-key": "Incorporar chave da API", "recommand": "Recomendar", "has-todo": "Tem PARA FAZER", "reference-by": "Referência por", "hide-notification": "Ocultar notificação", "search-settings": "Configurações de pesquisa...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "Esta operação irá apagar os arquivos selecionados e não poderá ser restaurada, por favor confirme.", "plugin-settings": "Configuração do Plugin", "installed-plugins": "Instalado", "marketplace": "<PERSON><PERSON><PERSON>", "local-development": "Desenvolvimento Local", "add-local-plugin": "Adicionar <PERSON>in Local", "local-plugin": "Plugin Local", "uninstall": "<PERSON><PERSON><PERSON><PERSON>", "install": "Instalar", "downloads": "Baixar", "plugin-updated": "<PERSON><PERSON><PERSON>", "plugin-update-failed": "Falha na Atualização do Plugin", "plugin-connection-failed": "Falha na conexão do plugin", "disconnect": "Desconectar", "local-development-description": "Adicione um plug-in de desenvolvimento local e depure-o.", "ai": "IA", "ai-chat-box-notes": "Abaixo estão as notas relevantes recuperadas para você.", "copy": "Cópia", "add-to-note": "Adicionar <PERSON>", "add-to-blinko": "Adicionar ao Blinko", "no-title": "<PERSON><PERSON>", "search-blinko-content-or-help-create": "Pesquise con<PERSON><PERSON><PERSON> ou ajude a criar...", "conversation-history": "Histórico de conversa", "new-conversation": "Nova Conversa", "knowledge-base-search": "Pesquisa em base de conhecimento", "add-tools-to-model": "Pesquise online ou permita que a IA chame a API blinko.", "clear-current-content": "<PERSON><PERSON> con<PERSON> atual", "welcome-to-blinko": "<PERSON><PERSON>-vind<PERSON>(a), {{name}}", "coding": "Programação", "ai-prompt-writing": "Você é um escritor profissional, por favor, escreva um artigo profissional sobre o tema fornecido pelo usuário.", "writing": "Escrita", "ai-prompt-translation": "Você é um tradutor profissional, por favor traduza o texto fornecido pelo usuário para {{lang}}", "ai-prompt-coding": "Você é um programador profissional, por favor escreva um programa Python simples com base no tópico fornecido pelo usuário.", "translation": "Tradução", "first-char-delay": "Atraso do primeiro caractere", "total-tokens": "Total de tokens", "check-connect": "Conferir", "check-connect-error": "A falha de conexão pode ser adicionada ao final de /v1.", "check-connect-success": "Verificar Sucesso da Conexão", "loading": "Carregando", "embedding-dimensions": "Dimensões de Incorporação", "embedding-dimensions-description": "Você precisa garantir que as dimensões do modelo estão corretas e precisa forçar a reconstrução dos registros de índice após as alterações.", "model": "<PERSON><PERSON>", "ai-tools": "Ferramentas de IA", "tavily-api-key": "<PERSON><PERSON> <PERSON>", "tavily-max-results": "Resultados Máximos de Tavily", "ai-prompt-writing-content": "Escreva um artigo de 200 palavras e salve-o em suas anotações", "ai-prompt-coding-content": "Extraindo o conteúdo da web https://github.com/blinko-space/blinko", "stop-task": "Interromper tarefa", "processing": "Processamento", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "Há uma tarefa de reconstrução em andamento, deseja reiniciar?", "hide-blog-images": "Ocultar imagens do blog", "ai-prompt-translation-content": "Verifique as notas NO Tags nos últimos dois dias e marque -as.", "ai-prompt-delete-content": "Encontre 2 notas arquivadas, resuma e salve -as como novas notas e exclua essas duas notas arquivadas", "older": "<PERSON><PERSON> ve<PERSON><PERSON>", "newer": "<PERSON><PERSON>e", "restore-this-version": "Restaurar esta versão", "Note History": "NOTA HISTÓRIA", "View History Versions": "<PERSON><PERSON><PERSON> as vers<PERSON><PERSON> da história", "history-note-only": "Atenção: esta história contém apenas conteúdo de texto, não histórico de arquivos", "referenceResource": "Recurso de referência", "to-ask-ai": "Para perguntar ai", "press-enter-to-select-first-result": "Pressione Enter para selecionar o primeiro resultado", "ask-ai": "Pergunte ai", "ask-blinko-ai-about-this-query": "Pergunte a Blinko ai sobre esta consulta", "search-or-ask-ai": "Nota de pesquisa, configurações ou pergunte ai ...", "plugin": "Plugin", "editor-preview": "editor", "both": "Ambos", "auto-add-tags": "Adicionar tags automáticas", "add-as-comment": "Adicione como comentário", "choose-what-to-do-with-ai-results": "Escolha o que fazer com os resultados da IA", "ai-post-processing-mode": "Modo de pós -processamento da IA", "ai-post-processing-prompt": "Comentário de pós-processamento de IA", "2fa-setup-successful": "2FA Configuração bem -sucedida", "ai-generate-emoji": "", "ai-generating-emoji": "", "align-center": "Centro", "align-left": "E<PERSON>rda", "align-right": "Certo", "alternate-text": "Texto alternativo", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "Analise o conteúdo da nota a seguir e sugira tags apropriadas e forneça um breve resumo", "api-key": "", "camera": "Câmera", "check": "Lista de tarefas", "close": "<PERSON><PERSON><PERSON>", "code": "Bloco de código", "code-theme": "Visualização do tema do bloco de código", "column": "Coluna", "content-generated-by-ai": "Conteúdo gerado pela IA", "content-theme": "Visualização do tema do conteúdo", "copied": "Copiado", "custom-path": "<PERSON><PERSON><PERSON> personalizado", "dark-mode": "<PERSON><PERSON> es<PERSON>ro", "date-range": "", "days-ago": "", "define-custom-prompt-for-ai-to-process-notes": "Opere o AI para comentar sobre a nota atual. Por exemplo: Por favor, resuma o conteúdo da nota. Se o conteúdo da nota for menor do que 10 palavras, por favor, refine para mim.", "delete-column": "<PERSON><PERSON>lu<PERSON> linha", "delete-row": "Excluir coluna", "devtools": "Devtools", "down": "Abaixo", "download-tip": "O navegador não suporta a função de download", "edit-mode": "Alternar o modo de edição", "embedding-score-description": "O limiar de similaridade para consultas é geralmente a distância da soma euclidiana", "emoji": "<PERSON><PERSON><PERSON>", "enter-custom-prompt-for-post-processing": "Insira o prompt personalizado para o processamento pós", "enter-spotify-consumer-secret": "Entre no Spotify Consumer Secret", "enter-your-api-key": "", "exclude-tag-from-embedding": "Exclua conteúdo marcado", "exclude-tag-from-embedding-desc": "Selecione uma tag para excluir suas notas associadas da geração de vetores de incorporação de IA", "exclude-tag-from-embedding-tip": "Notas com esta tag serão excluídas do processamento de incorporação de IA", "file-type-error": "Tipo de arquivo é erro", "follow-system": "Siga o sistema", "footnote-ref": "<PERSON><PERSON> <PERSON> rod<PERSON>", "fullscreen": "Alternar a tela cheia", "generate": "<PERSON><PERSON><PERSON>", "heading1": "Cabeçalho 1", "heading2": "Titular 2", "heading3": "Título 3", "heading4": "Cabeçalho 4", "heading5": "Titular 5", "heading6": "CIDADE 6", "headings": "<PERSON><PERSON><PERSON><PERSON>", "help": "<PERSON><PERSON><PERSON>", "hours-ago": "", "image-url": "Imagem URL", "impoort-from-bko": "", "import-done": "Importação feita", "indent": "<PERSON><PERSON><PERSON>", "info": "Informações", "inline-code": "Código embutido", "insert-column-left": "Insira 1 à esquerda", "insert-column-right": "Insira 1 à direita", "insert-row-above": "Insira 1 acima", "insert-row-below": "Insira 1 abaixo", "instant-rendering": "Renderização instantânea", "light-mode": "<PERSON><PERSON> de luz", "line": "<PERSON><PERSON>", "link": "Link", "link-ref": "<PERSON>", "list": "Lista", "minutes-ago": "", "months-ago": "", "more": "<PERSON><PERSON>", "music-settings": "Configurações musicais", "name-empty": "O nome está vazio", "order-by-create-time": "Ordem por Criar tempo", "ordered-list": "Lista de pedidos", "outdent": "Overting", "outline": "Contorno", "over": "sobre", "performance-tip": "A visualização em tempo real requer $ {x} ms, você pode fechá-lo", "preview": "Visualização", "prompt-used-for-post-processing-notes": "Prompt usado para notas de pós -processamento", "rebuild": "Reconstruir", "rebuild-in-progress": "Reconstruir em andamento", "rebuilding-embedding-progress": "Reconstruindo o progresso da incorporação", "record": "Inicie o registro/final do registro", "record-tip": "O dispositivo não suporta gravação", "redo": "<PERSON><PERSON><PERSON>", "remove": "Remover", "rest-user-info": "REST Informações do usuário", "row": "<PERSON><PERSON>", "setting": "", "spin": "<PERSON><PERSON>", "split-view": "Visualização dividida", "spotify-consumer-key": "Chave do Spotify API", "spotify-consumer-secret": "Spotify API Secret", "strike": "Batida", "superadmin": "", "table": "Mesa", "text-is-not-empty": "texto (sem vazio)", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "Esta operação remove o rótulo associado e não pode ser restaurado, confirme", "title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip-text": "Texto da dica de ferramenta", "undo": "<PERSON><PERSON><PERSON>", "up": "Acima", "update": "<PERSON><PERSON><PERSON><PERSON>", "updated-at": "Atualização em", "upload-error": "Erro de upload", "uploading": "Enviando ...", "user": "", "weeks-ago": "", "wysiwyg": "Wysiwyg", "years-ago": "", "to-search-tags": "Para pesquisar tags", "app-upgrade-required": "Atualização do aplicativo necessária", "current-app-version": "Versão atual do APP", "required-app-version": "Versão do APP necessária", "upgrade": "Atualização", "online-search": "Pesquisa Online", "smart-edit": "Edição Inteligente", "function-call-required": "Chamada de Função Necessária", "smart-edit-prompt": "Prompt de Edição Inteligente", "define-instructions-for-ai-to-edit-your-notes": "Você pode usar prompts para manipular notas, por exemplo: Se uma nota contiver um link, resuma o conteúdo do link abaixo da nota original e gere um rótulo.", "rebuild-started": "Reconstrução Iniciada", "rebuild-stopped-by-user": "Reconstrução interrompida pelo usuário", "random-mode": "Passeio <PERSON>", "related-notes": "Notas relacionadas", "no-related-notes-found": "Nenhuma nota relacionada encontrada", "advanced": "Avançado", "rerank-model-description": "Especificar um modelo para reorganizar os resultados do vetor a fim de melhorar a precisão da pesquisa", "rerank-model": "Modelo de reclassificação", "rerank": "Reclassificar", "use-custom-rerank-endpoint-description": "Quando ativado, os endpoints e as chaves de API do modelo incorporado serão reordenados.", "use-embedding-endpoint": "Usar ponto de extremidade de incorporação", "rerank-score-description": "Defina um limiar de pontuação para o modelo de reorganização, abaixo do qual os resultados serão filtrados.", "public-share": "Compartilhamento público", "internal-share": "Compartilhamento Interno", "no-team-members-found": "Nenhum membro da equipe encontrado", "selected-users": "Usuários selecionados", "tags-prompt": "Tags de Solicitação", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "Texto utilizado para geração automática de etiquetas. Se deixado em branco, o texto padrão será utilizado.", "generate-low-permission-token": "<PERSON><PERSON><PERSON> Permissão Baixa", "low-permission-token-desc": "Os tokens de baixa permissão só podem acessar o endpoint upsertNote e o endpoint do chat AI. Eles não podem acessar suas informações de conta ou outras notas. Isso é ideal para casos de uso, como bots do Telegram ou bots do WeChat, onde você quer garantir que eles não possam acessar quaisquer outras notas.", "this-token-is-only-displayed-once-please-save-it-properly": "Este token é exibido apenas uma vez, por favor, salve-o corretamente.", "refresh-model-list": "Obter lista de modelos", "please-set-the-embedding-model": "Por favor, configure o modelo embutido.", "blinko-endpoint": "Ponto final do Blinko", "enter-blinko-endpoint": "O URL do seu Blinko implantado", "login-failed": "Falha no login", "verification-failed": "Falha na autenticação", "download-success": "Download bem sucedido", "download-failed": "Falha no download", "downloading": "<PERSON><PERSON><PERSON>", "hide-pc-editor": "Ocultar o editor da versão para PC.", "import-from-markdown": "Importar de um arquivo Markdown", "import-from-markdown-tip": "Importar de um único arquivo .md ou de um arquivo compactado .zip contendo arquivos .md.", "not-a-markdown-or-zip-file": "Não é um arquivo Markdown ou zip. Por favor, escolha um arquivo .md ou .zip.", "todo": "Agente ou intermediário", "restore": "Recuperar", "complete": "concluir", "today": "hoje", "yesterday": "ontem", "common.refreshing": "Atualizando", "common.releaseToRefresh": "Solte para atualizar", "common.pullToRefresh": "<PERSON><PERSON><PERSON> para atualizar", "edit-message-warning": "Editar esta mensagem apagará todos os registros de conversação subsequentes e gerará novamente as respostas da IA.", "enter-your-message": "Insira a sua mensagem", "set-deadline": "Definir o prazo.", "expired": "<PERSON><PERSON><PERSON>", "expired-days": "Vencido por {{count}} dias", "expired-hours": "<PERSON><PERSON><PERSON> há {{count}} horas.", "expired-minutes": "<PERSON><PERSON><PERSON> há {{count}} minutos.", "days-left": "{{count}} dias depois", "hours-left": "{{count}} horas depois", "minutes-left": "{{count}} minutos depois", "about-to-expire": "Prestes a expirar", "1-day": "1 dia", "1-week": "<PERSON><PERSON> semana", "1-month": "um mês", "quick-select": "<PERSON><PERSON><PERSON> rápid<PERSON>", "import-ai-configuration": "Importar configurações de IA", "would-you-like-to-import-this-configuration": "Você deseja importar essa configuração de IA?", "detected-ai-configuration-to-import": "Detecção de configuração AI para importar", "importing": "Importando", "cache-cleared-successfully": "O cache foi limpo com sucesso! A página será recarregada automaticamente.", "failed-to-clear-cache": "Falha ao limpar o cache do navegador. Por favor, tente atualizar manualmente (Ctrl+Shift+R).", "select-deployment": "Selecionar implantação", "deployment-name": "Nome da implantação", "please-set-the-api-endpoint": "Por favor, defina o endpoint da <PERSON>", "please-set-the-api-key": "Por favor, defina a chave da API"}