{"spotify-consumer-key-tip": "<PERSON><PERSON><PERSON><PERSON>, um MP3-<PERSON><PERSON><PERSON> zu erhalten.", "spotify-consumer-key-tip-2": "<PERSON><PERSON> sich den API-<PERSON><PERSON><PERSON><PERSON> https://developer.spotify.com/", "blinko": "Blinko", "notes": "Notizen", "resources": "Ressourcen", "archived": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Einstellungen", "total-tags": "ALLE TAGS", "search": "Suche...", "i-have-a-new-idea": "Ich habe eine neue Idee...", "add-tag": "Tag hinzufügen", "all-notes-have-been-loaded": "Alle {{items}} Notizen wurden geladen", "already-have-an-account-direct-login": "Konto bereits vorhanden? Direkt anmelden", "api-endpoint": "API-Endpunkt", "archive": "Archiv", "ask-about-your-notes": "Fragen Sie nach Ihren Notizen", "backup-file": "SICHERUNGSDATEI", "basic-information": "Grundlegende Informationen", "bulleted-list": "Aufzählungsliste", "cancel": "Abbrechen", "cancel-top": "Abbrechen oben", "change-type": "<PERSON><PERSON>", "change-user-info": "Benutzerinformationen ändern", "check-list": "Checkliste", "confirm": "Bestätigen", "confirm-password": "Passwort bestätigen", "convert-to": "Konvertieren zu", "convert-to-blinko": "<PERSON><PERSON> konvert<PERSON>en", "convert-to-note": "Zu Notiz konvertieren", "create-successfully": "Erfolgreich erstellt", "create-successfully-is-about-to-jump-to-the-login": "Erfolgreich erstellt, Umleitung zum Login", "daily-review": "Tägliche Überprüfung", "delete": "Löschen", "delete-confirm": "Löschen bestätigen", "delete-success": "Erfolg<PERSON><PERSON>", "edit": "bearbeiten", "enter-your-name": "<PERSON>n e<PERSON>ben", "enter-your-password": "Passwort eingeben", "enter-your-username": "Benutzernamen eingeben", "every-half-year": "<PERSON><PERSON> ha<PERSON>", "every-month": "<PERSON><PERSON>", "hello": "Hall<PERSON>", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "Hallo {{name}}, ich kann in Notizen suchen, wie kann ich heute helfen?", "import-from-bko": "Importieren von .bko", "import": "Importieren", "insert-hashtag": "Tag einfügen", "items": "Artikel", "language": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiple-select": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Name", "name-db": "NAME", "need-to-create-an-account": "Konto erstellen?", "new-version-detected-click-to-get-the-latest-version": "🎉Neue Version entdeckt, hier klicken, um die neueste Version zu erhalten", "nickname": "Spitzname", "no-data-here-well-then-time-to-write-a-note": "Keine Daten hier? Dann wird es <PERSON>eit, eine Notiz zu schreiben!", "no-tag-found": "Kein Tag gefunden", "not-a-bko-file": "keine b<PERSON>-<PERSON><PERSON>", "note": "Notiz", "numbered-list": "Nummerierte Liste", "operation-failed": "Operation fehlgeschlagen.", "password": "Passwort", "preference": "Präferenz", "recording": "<PERSON><PERSON><PERSON><PERSON>", "required-items-cannot-be-empty": "Erforderliche Elemente können nicht leer sein", "rest-user-password": "Restliches Benutzerpasswort", "running": "Läuft", "save": "Speichern", "schedule-archive-blinko": "Blinko archivieren", "schedule-back-up": "Zeitplan für Backup", "schedule-task": "Aufgaben Zeitplan", "show-less": "<PERSON><PERSON> anzeigen", "show-more": "<PERSON><PERSON> anzeigen", "show-navigation-bar-on-mobile": "Navigationsleiste auf dem Handy ausblenden", "sign-in": "Anmelden", "sign-up": "Registrieren", "status": "STATUS", "stopped": "Gestoppt", "the-two-passwords-are-inconsistent": "Die beiden Kennwörter sind unterschiedlich", "theme": "Modus", "there-are-no-resources-yet-go-upload-them-now": "Es sind noch keine Ressourcen vorhanden, jetzt hochladen", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "Bei diesem Vorgang wird der zugehörige Tag entfernt und kann nicht wiederhergestellt werden.", "this-operation-will-be-delete-resource-are-you-sure": "Bei diesem Vorgang wird die Ressource gelöscht und kann nicht wiederhergestellt werden.", "top": "Anheften", "total": "Insgesamt", "update-successfully": "Erfolgreich aktualisiert", "upload-file": "<PERSON><PERSON> ho<PERSON>n", "use-ai": "KI verwenden", "user-custom-openai-api-key": "Benutzerdefinierter OpenAI Api-Schlüssel", "user-custom-azureopenai-api-instance": "Azure OpenAI Instanzname", "user-custom-azureopenai-api-deployment": "Azure OpenAI Bereitstellungsname", "user-custom-azureopenai-api-version": "API version", "user-or-password-error": "Benutzer- oder Passwortfehler", "username": "<PERSON><PERSON><PERSON><PERSON>", "your-changes-have-been-saved": "Änderungen wurden gespeichert!", "confirm-your-password": "Passwort bestätigen", "confrim": "Bestätigen", "every-day": "Jeden Tag", "every-three-month": "Alle drei <PERSON>", "every-week": "<PERSON><PERSON>", "insert-sandpack": "Code-Sandbox einfügen", "insert-table": "<PERSON>bell<PERSON> e<PERSON>fügen", "schedule": "SCHEDULE", "ai-model": "KI-Modell", "confirm-to-delete": "Löschen bestätigen!", "congratulations-youve-reviewed-everything-today": "Sie haben heute alles durchgesehen.", "detail": "Details", "enter-send-shift-enter-for-new-line": "Enter zum Senden, <PERSON><PERSON>+Enter für neue Zeile", "in-progress": "In Arbeit...", "insert-codeblock": "Code-Block einfügen", "keep-sign-in": "Ang<PERSON><PERSON><PERSON> bleiben", "last-run": "ZULETZT GESTARTET", "model-provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "must-start-with-http-s-or-use-api-openai-as-default": "Muss mit http(s):// beginnen oder /api/openai als Standard verwenden", "recovery": "Wiederherstellen", "reviewed": "Überprüft", "created-in": "<PERSON><PERSON><PERSON>t in", "set-as-public": "Veröffentlichen", "unset-as-public": "Nicht mehr veröffentlichen", "no-tag": "<PERSON><PERSON>", "with-link": "<PERSON> v<PERSON><PERSON>en", "has-file": "<PERSON><PERSON> vor<PERSON>en", "created-at": "Erstellen am", "role": "<PERSON><PERSON>", "create-user": "<PERSON><PERSON><PERSON> er<PERSON>", "action": "Aktion", "original-password": "Original-Passwort", "import-from-memos-memos_prod-db": "Import aus Memos(memos_prod.db)", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "<PERSON>n <PERSON>e memos_prod.db exportieren, beenden Sie bitte den Memos-Container, um einen teilweisen Datenverlust zu vermeiden.", "go-to-share-page": "Zur Freigabeseite gehen", "import-done": "Import abgeschlossen", "rebuilding-embedding-progress": "Fortschritt Neuaufbau der Einbettungsvektoren", "rebuild": "<PERSON><PERSON> au<PERSON>", "notes-imported-by-other-means-may-not-have-embedded-vectors": "<PERSON><PERSON> anderem Weg importierte Notizen haben möglicherweise keine eingebetteten Vektoren", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "Wenn viele Notizen vorhanden sind, kann eine große Anzahl von To<PERSON> verbrauchen werden.", "time-format": "Zeitformat", "version": "Version", "new-version-available": "Neue Version verfügbar", "storage": "<PERSON><PERSON><PERSON><PERSON>", "local-file-system": "Lokales Dateisystem", "object-storage": "Speicherung von O<PERSON>je<PERSON>en", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "Zusätzlich zum GPT-Modell muss sichergestellt werden, dass folgende Einbettung aktiv ist", "speech-recognition-requires-the-use-of": "Die Spracherkennung erfordert die Verwendung von", "ai-expand": "KI Text erweitern", "ai-polish": "KI Text verbessern", "accept": "Akzeptieren", "reject": "<PERSON><PERSON><PERSON><PERSON>", "stop": "Stopp", "card-columns": "<PERSON><PERSON>", "select-a-columns": "Spalte auswählen", "width-less-than-1024px": "<PERSON><PERSON><PERSON> kleiner als 1024px", "width-less-than": "<PERSON><PERSON><PERSON> kleiner als", "small-device-card-columns": "Spalten für kleine Bildschirme", "medium-device-card-columns": "Spalten für mittlere Bildschirme", "large-device-card-columns": "Spalten für große Bildschirme", "device-card-columns": "Spaltenanzahl der Geräte", "columns-for-different-devices": "Anzahl der Spalten für verschiedene Geräte", "mobile": "Mobil", "tablet": "Tablet", "desktop": "Desktop", "chars": "<PERSON><PERSON><PERSON>", "text-fold-length": "Maximal ungekürzte Textlänge", "title-first-line-of-the-text": "Titel (erste Zeile des Textes)", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "Inhalt (Rest des Textes, wenn der Text länger als die maximale Länge ist)", "ai-tag": "KI-Tag", "article": "Artikel", "embedding-model": "Einbettungsmodell", "force-rebuild": "Neuaufbau erzwingen", "force-rebuild-embedding-index": "Ein erzwungener Neuaufbau stellt alle Daten wieder her, die vollständig indiziert wurden.", "embedding-model-description": "Der Index muss nach einem Wechsel des eingebetteten Modells neu aufgebaut werden", "top-k-description": "Maximale Anzahl von Dokumenten, die zurückgegeben werden", "embedding-score-description": "Die Ähnlichkeitsschwelle für Abfragen ist im Allgemeinen der euklidische Summenabstand", "embedding-lambda-description": "Abfrageergebnis Diversität Gewichtungsparameter", "update-tag-icon": "Tag-Symbol aktualisieren", "delete-only-tag": "Nur Tag löschen", "delete-tag-with-note": "Tag mit Notizen löschen", "update-tag-name": "Tag-Name aktualisieren", "thinking": "Denke...", "select-all": "Alle auswählen", "deselect-all": "Alle abwählen", "insert-before": "Einfügen vor", "insert-after": "Einfügen nach", "update-name": "Name aktualisieren", "ai-emoji": "KI-Emoji", "custom-icon": "Benutzerdefiniertes Icon", "ai-enhanced-search": "KI-erweiterte Suche", "preview-mode": "Vorschau-Modus", "source-code": "Quellcode", "camera": "<PERSON><PERSON><PERSON>", "reference": "<PERSON><PERSON><PERSON><PERSON>", "reference-note": "Referenz-Notiz", "source-code-mode": "Quellcode-Modus", "heading": "Überschrift", "paragraph": "Absatz", "quote": "Zitat", "bold": "<PERSON><PERSON>", "remove-italic": "Kursiv entfernen", "underline": "Unterstreichen", "italic": "<PERSON><PERSON><PERSON>", "remove-bold": "<PERSON><PERSON>", "remove-underline": "Unterstrich entfernen", "select-block-type": "Blocktyp auswählen", "block-type-select-placeholder": "Block Typ", "trash": "Papierkorb", "page-size": "Größe der Seite", "toolbar-visibility": "Sichtbarkeit der Symbolleiste", "always-hide-toolbar": "<PERSON><PERSON> verst<PERSON>", "always-show-toolbar": "Immer anzeigen", "hide-toolbar-on-mobile": "Auf dem <PERSON> ausblenden", "select-toolbar-visibility": "Sichtbarkeit der Symbolleiste auswählen", "select-a-time-format": "Zeitformat auswählen", "enter-code-shown-on-authenticator-app": "Code eingeben, der in der Authenticator-App angezeigt wird", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "Öffnen Sie die Authentifizierungs-App eines Drittanbieters und geben Sie die auf dem Bildschirm angezeigten Codes ein.", "two-factor-authentication": "Zwei-Faktoren-Authentifizierung", "scan-this-qr-code-with-your-authenticator-app": "<PERSON>annen Sie diesen QR-Code mit Ihrer Authenticator-App", "or-enter-this-code-manually": "Oder geben Sie diesen Code manuell ein:", "verify": "Überprüfen", "about": "<PERSON><PERSON>", "upload": "Hochladen", "days": "Tage", "select-model-provider": "Modellanbieter auswählen", "allow-register": "Benutzerregisterungen zulassen", "access-token": "Zugangs-Token", "bucket": "Bucket", "region": "Region", "access-key-secret": "Geheimer Zugangsschlüssel", "access-key-id": "ID Zugangsschlüssel", "share-and-copy-link": "Link teilen und kopieren", "copy-share-link": "Link zur Freigabe kopieren", "endpoint": "Endpunkt", "export-format": "Format exportieren", "export": "Exportieren", "time-range": "Zeitspanne", "all": "Alle", "exporting": "Exportieren...", "has-image": "Bild vorhanden", "has-link": "<PERSON> v<PERSON><PERSON>en", "filter-settings": "Filter-Einstellungen", "tag-status": "Tag Status", "all-notes": "Alle Notizen", "with-tags": "Mit Tags", "without-tags": "Ohne Tags", "select-tags": "Tags auswählen", "additional-conditions": "Zusätzliche Bedingungen", "apply-filter": "<PERSON><PERSON> anwenden", "to": "An", "start-date": "Startdatum", "end-date": "Enddatum", "reset": "Z<PERSON>ücksetzen", "no-condition": "<PERSON><PERSON>", "public": "<PERSON><PERSON><PERSON><PERSON>", "ai-model-tooltip": "<PERSON><PERSON><PERSON> Si<PERSON> den zu verwendenden Modellnamen ein, z. B. gpt-3.5-turbo, gpt-4, gpt-4o, gpt-4o-mini", "user-custom-azureopenai-api-deployment-tooltip": " <PERSON>eb<PERSON> Si<PERSON> den zu verwendenden Einsatznamen ein, z. B. gpt-4o", "ollama-ai-model-tooltip": "<PERSON><PERSON><PERSON> Si<PERSON> den zu verwendenden Modellnamen ein, z. B. llama3.2", "ollama-default-endpoint-is-http-localhost-11434": "Der Standard-Endpunkt von Ollama ist http://localhost:11434.", "your-azure-openai-instance-name": "Der Name Ihrer Azure OpenAI-Instanz", "ai-generate-emoji": "", "ai-generating-emoji": "", "api-key": "", "date-range": "", "days-ago": "", "enter-your-api-key": "", "hours-ago": "", "impoort-from-bko": "", "minutes-ago": "", "months-ago": "", "superadmin": "", "user": "", "weeks-ago": "", "years-ago": "", "both": "<PERSON><PERSON>", "code": "Code-Block", "column": "<PERSON>lt<PERSON>", "content-theme": "Vorschau des Inhaltsdesigns", "dark-mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete-column": "Spalte löschen", "delete-row": "Zeile löschen", "down": "Nach unten", "download-tip": "Der Browser unterstützt die Download-Funktion nicht.", "edit-mode": "Umschalten in den Bearbeitungsmodus", "edit-user": "<PERSON><PERSON><PERSON> bearbeiten", "emoji": "<PERSON><PERSON><PERSON>", "exclude-tag-from-embedding": "Markierten Inhalt ausschließen", "exclude-tag-from-embedding-desc": "Tag auswählen, um die zugehörigen Notizen von der Generierung des KI-Einbettungsvektors auszuschließen.", "exclude-tag-from-embedding-tip": "Notizen mit diesem Tag werden von der KI-Einbettungsverarbeitung ausgeschlossen.", "file-type-error": "Dateityp ist fehlerhaft", "follow-system": "Folgen Sie dem System.", "footnote-ref": "Fußnotenverweis", "fullscreen": "Vollbild anzeigen", "generate": "<PERSON><PERSON><PERSON>", "heading1": "Überschrift 1", "heading2": "Überschrift 2", "heading3": "Überschrift 3", "heading4": "Überschrift 4", "heading5": "Überschrift 5", "heading6": "Überschrift 6", "headings": "Überschriften", "help": "<PERSON><PERSON><PERSON>", "image-url": "Bild-URL", "indent": "Einzug", "info": "Informationen", "inline-code": "Inline-Code", "insert-column-left": "Spalte links einfügen", "insert-column-right": "Spalte rechts einfügen", "insert-row-above": "<PERSON><PERSON><PERSON> oben einfügen", "insert-row-below": "<PERSON><PERSON><PERSON> unten einfügen", "instant-rendering": "Echtzeit-Rendering", "light-mode": "Hellmodus", "line": "<PERSON><PERSON>", "link": "Verknüpfung", "link-ref": "<PERSON><PERSON><PERSON><PERSON>", "list": "Liste", "more": "<PERSON><PERSON>", "name-empty": "Der Name ist leer", "order-by-create-time": "Sortieren nach Erstellungszeit", "ordered-list": "Numerierte Liste", "outdent": "Ausrücken", "outline": "Gliederung", "over": "über", "performance-tip": "Echtzeitvorschau benötigt ${x}ms, du kannst sie schließen.", "preview": "Vorschau", "rebuild-embedding-index": "Einbettungsindex neu erstellen", "record": "Start-/<PERSON>au<PERSON>nahme", "record-tip": "Das Gerät unterstützt keine Aufzeichnung.", "redo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remove": "Entfernen", "row": "<PERSON><PERSON><PERSON>", "spin": "<PERSON><PERSON><PERSON>", "split-view": "<PERSON><PERSON><PERSON> An<PERSON>", "strike": "Durchgestrichen", "table": "<PERSON><PERSON><PERSON>", "search-tags": "Suchbegriffe", "insert-attachment-or-note": "In Anhang oder Notiz einfügen?", "context": "Kontext", "paste-to-note-or-attachment": "<PERSON>d <PERSON>, dass Si<PERSON> zu Notiz oder Anhang einfügen möchten?", "attachment": "<PERSON><PERSON>", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "Nach dem Löschen werden alle Benutzerdaten gelöscht und können nicht wiederhergestellt werden.", "upload-completed": "Hochladen abgeschlossen", "upload-cancelled": "Hochladen abgebrochen", "upload-failed": "Hochladen fehlgeschlagen", "import-from-bko-tip": "Das Hochladen zur Wiederherstellung auf S3 wird derzeit nicht unterstützt. Deaktivieren Sie die S3-Option vorübergehend, wenn Sie eine Wiederherstellung durchführen möchten.", "edit-time": "Bearbeitungszeit", "ai-write": "KI Write\n\nAI schreiben", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rename": "Umbenennen", "move-up": "Nach oben verschieben", "cut": "Schneiden", "paste": "Einfügen", "confirm-delete": "Bestätigen Löschen", "confirm-delete-content": "<PERSON>d <PERSON> sicher, dass <PERSON> {{name}} löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "folder-name": "Ordnername", "file-name": "Dateiname", "operation-success": "Operation erfolgreich", "cloud-file": "Cloud-<PERSON><PERSON>", "move-to-parent": "Zum übergeordneten Element wechseln", "no-resources-found": "<PERSON><PERSON>n gefunden", "new-folder": "<PERSON><PERSON><PERSON> Ordner", "folder-name-exists": "Der Ordnername existiert.", "folder-name-required": "<PERSON><PERSON><PERSON><PERSON>", "collapse": "Zusammenbruch", "show-all": "Alle anzeigen", "sun": "<PERSON><PERSON>", "mon": "<PERSON><PERSON>", "wed": "Mittwoch", "thu": "Don<PERSON><PERSON>", "fri": "Freitag", "sat": "Samstag", "heatMapTitle": "Wärmekarte der Notizen aus dem letzten Jahr", "heatMapDescription": "Z<PERSON>gt die Anzahl der pro Tag erstellten Notizen.", "select-month": "Auswählen Monat", "note-count": "Not<PERSON><PERSON><PERSON><PERSON>", "max-daily-words": "Maximale tägliche Wörter", "active-days": "Aktive Tage", "total-words": "Gesamtwörter", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "tag-distribution": "Tagverteilung", "other-tags": "Andere Schlagwörter", "tue": "Dienstag", "offline-status": "Offline-Modus", "offline-title": "Du bist offline", "offline-description": "Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut.", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "back-to-home": "Zurück zur Startseite", "offline": "Offline", "close-background-animation": "Schließen der Hintergrundanimation", "custom-bg-tip": "<PERSON><PERSON><PERSON> zu https://www.shadergradient.co/, um deinen eigenen Verlaufshintergrund zu erstellen.", "custom-background-url": "Benutzerdefinierten Hintergrund", "share": "Teilen", "need-password-to-access": "Passwortzu<PERSON><PERSON>", "password-error": "Passwortfehler", "cancel-share": "Stornieren Freigeben", "create-share": "<PERSON><PERSON><PERSON><PERSON>", "share-link": "Freigabelink", "set-access-password": "Legen Sie das Zugriffspasswort fest", "protect-your-shared-content": "Schütze deine gemeinsamen Inhalte.", "access-password": "Zugriffspasswort", "select-date": "<PERSON><PERSON><PERSON>en Si<PERSON> ein Datum aus", "expiry-time": "Verfallszeit", "select-expiry-time": "Wählen Sie das Ablaufdatum aus.", "permanent-valid": "Dauerhaft gültig", "7days-expiry": "7 Tage Ablauf", "custom-expiry": "Benutzerdefiniertes Ablaufdatum", "30days-expiry": "30 Tage Ablauf", "share-link-expired": "Freigabelink abgelaufen", "share-link-expired-desc": "Dieser Link ist abgelaufen. Bitte wenden Sie sich an den Administrator, um ihn erneut freizugeben!", "shared": "Get<PERSON><PERSON>", "internal-shared": "<PERSON><PERSON>", "edited": "Bearbeitet", "move-down": "Nach unten bewegen", "provider-id": "Anbieter-ID", "provider-name": "<PERSON><PERSON><PERSON><PERSON>", "well-known-url": "Bekannte URL", "authorization-url": "Autorisierungs-URL", "token-url": "Token-URL", "userinfo-url": "Benutzerinfo-URL", "scope": "Umfang", "client-id": "Kunden-ID", "client-secret": "Client-Geheimnis", "sso-settings": "SSO-Einstellungen", "oauth2-providers": "OAuth2-An<PERSON><PERSON>", "add-oauth2-provider": "Füge einen Oauth2-<PERSON><PERSON><PERSON> hinzu", "add-provider": "An<PERSON><PERSON> hinzufügen", "edit-oauth2-provider": "OAuth2-<PERSON><PERSON><PERSON> bear<PERSON>ten", "confirm-delete-provider": "Bestätigen Löschen Anbieter", "please-select-icon-from-iconify": "Bitte wählen Sie ein Symbol aus Iconify aus.", "provider-icon": "Anbieter-Symbol", "select-provider-template": "Wählen Sie Anbietervorlage aus", "provider-template": "Anbie<PERSON>vor<PERSON>", "please-add-this-url-to-your-oauth-provider-settings": "Bitte fügen Sie diese URL zu Ihren OAuth-Anbieter-Einstellungen hinzu.", "redirect-url": "Weiterleitungs-URL", "sign-in-with-provider": "<PERSON>den Sie sich mit {{ <PERSON><PERSON><PERSON> }} an.", "community": "Gemeinschaft", "theme-color": "Hauptfarbe", "link-account": "Verknüpfen Konto", "select-account": "<PERSON><PERSON> auswählen", "link-account-warning": "<PERSON><PERSON>, dass wenn Sie Ihre Konten verknüpfen, werden keine Daten vom aktuellen Konto mit dem verbundenen Konto synchronisiert.", "unlink-account": "<PERSON><PERSON> trennen", "unlink-account-tips": "Bestätigen Sie den Zugriff auf alle Verbände mit diesem Konto?", "login-type": "Anmeldetyp", "close-daily-review": "Tägliche Schlussüberprüfung", "max-home-page-width": "Maximale Breite der Startseite", "max-home-page-width-tip": "Wenn es auf 0 gesetzt ist, ist es die maximale Breite.", "no-comments-yet": "Es liegen noch keine Kommentare vor.", "author": "Autor", "from": "<PERSON>", "reply-to": "Antworten an", "comment": "Kommentar", "hub": "<PERSON><PERSON>", "home-site": "Startseite", "use-blinko-hub": "Verwenden Sie Blinko Hub.", "full-screen": "Vollbild", "exit-fullscreen": "<PERSON><PERSON><PERSON><PERSON> beenden", "no-note-associated": "<PERSON><PERSON> Notiz <PERSON>", "insert-context": "Einfügen in den Kontext", "follow": "Folgen", "follower": "<PERSON><PERSON><PERSON><PERSON>", "following": "Folgend", "admin": "Webmaster", "site-url": "Blinko-Website-URL", "unfollow": "Entfolgen", "join-hub": "Treten Sie dem Hub bei.", "refresh": "Aktualisieren", "comment-notification": "Kommentarbenachrichtigung", "follow-notification": "Folgen Benachrichtigung", "followed-you": "folgte dir", "mark-all-as-read": "Alles als gelesen markieren", "no-notification": "<PERSON><PERSON>", "new-notification": "Neue Benachrichtigung", "notification": "Benachrichtigung", "backup-success": "Sicherung erfolgreich🎉", "system-notification": "Systembenachrichtigung", "embedding-api-endpoint": "<PERSON><PERSON><PERSON> von API-Endpunkt", "embedding-api-key": "API-Schlüssel einbetten", "recommand": "Empfehlung", "has-todo": "Hat ZU TUN", "reference-by": "<PERSON><PERSON><PERSON><PERSON>", "hide-notification": "Benachrichtigung ausblenden", "search-settings": "Sucheinstellungen...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "Dieser Vorgang wird die ausgewählten Dateien löschen und kann nicht wiederhergestellt werden. Bitte bestätigen Sie.", "plugin-settings": "Plugin-Einstellung", "installed-plugins": "Installiert", "marketplace": "Marktplatz", "local-development": "Lokale Entwicklung", "add-local-plugin": "Füge lokales Plugin hinzu", "local-plugin": "Lokales Plugin", "uninstall": "Deinstallieren", "install": "Install\n\nInstallation", "downloads": "Downloads\n\n<PERSON><PERSON><PERSON><PERSON><PERSON>", "plugin-updated": "<PERSON><PERSON><PERSON> a<PERSON>", "plugin-update-failed": "Plugin-Update fehlgeschlagen.", "plugin-connection-failed": "Plugin-Verbindung fehlgeschlagen", "disconnect": "<PERSON><PERSON><PERSON>", "local-development-description": "Fügen Sie ein lokales Entwicklungs-Plugin hinzu und debuggen Sie es.", "ai": "KI", "ai-chat-box-notes": "Unten sind die relevanten Notizen für Sie abgerufen worden", "copy": "<PERSON><PERSON><PERSON>", "add-to-note": "<PERSON><PERSON>", "add-to-blinko": "<PERSON><PERSON>", "no-title": "<PERSON><PERSON>", "search-blinko-content-or-help-create": "<PERSON><PERSON> blinko-Inhalte oder hilf beim <PERSON>...", "conversation-history": "Konversationsverlauf", "new-conversation": "<PERSON><PERSON><PERSON>", "knowledge-base-search": "Wissensdatenbanksuche", "add-tools-to-model": "Suche online oder erlaube der KI, die blinko-API aufzurufen.", "clear-current-content": "Aktuellen Inhalt löschen", "welcome-to-blinko": "<PERSON><PERSON><PERSON><PERSON>, {{name}}", "ai-prompt-writing": "Sie sind ein professioneller Schriftsteller, bitte verfassen Sie einen professionellen Artikel über das vom Benutzer bereitgestellte Thema.", "coding": "Programmierung", "writing": "Schreiben", "ai-prompt-translation": "Sie sind ein professioneller Übersetzer, bitte übersetzen Sie den vom Benutzer bereitgestellten Text ins {{lang}}", "ai-prompt-coding": "Sie sind ein professioneller Programmierer, bitte schreiben Sie ein einfaches Python-Programm basierend auf dem vom Benutzer bereitgestellten Thema.", "translation": "Übersetzung", "first-char-delay": "First-Zeichenverzögerung", "total-tokens": "Gesamttokens", "check-connect": "Überprüfen", "check-connect-error": "Der Verbindungsfehler kann am Ende von /v1 hinzugefügt werden.", "check-connect-success": "Prüfung der Verbindung erfolgreich", "loading": "Laden", "embedding-dimensions": "Einbettungsdimensionen", "embedding-dimensions-description": "<PERSON><PERSON> müssen sicherstellen, dass die Modellabmessungen korrekt sind, und <PERSON>e müssen erzwingen, dass Indexdatensätze nach Änderungen neu aufgebaut werden.", "model": "<PERSON><PERSON>", "ai-tools": "KI-Werkzeuge", "tavily-api-key": "Tavily Search API-Schlüssel", "tavily-max-results": "<PERSON><PERSON> <PERSON>", "ai-prompt-writing-content": "Schreiben Sie einen 200-Wörter-Artikel und speichern Si<PERSON> ihn in Ihren Notizen.", "ai-prompt-coding-content": "<PERSON><PERSON><PERSON><PERSON> von Webinhalten aus https://github.com/blinko-space/blinko", "stop-task": "Aufgabe beenden", "processing": "Verarbeitung", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "<PERSON>äuft gerade eine Wiederherstellungsaufgabe, möchten Si<PERSON> neu starten?", "hide-blog-images": "Blogbilder ausblenden", "ai-prompt-translation-content": "Überprüfen Sie die No -Tags -Notizen in den letzten zwei Tagen und markieren Sie sie.", "ai-prompt-delete-content": "Finden Sie 2 archivierte Notizen, fassen Sie sie zusammen und speichern Sie sie als neue Notizen und löschen Sie diese beiden archivierten Notizen", "older": "<PERSON><PERSON>", "newer": "<PERSON><PERSON><PERSON>", "restore-this-version": "<PERSON><PERSON><PERSON> diese Version wieder her", "Note History": "Beachten Sie die Geschichte", "View History Versions": "<PERSON><PERSON><PERSON><PERSON>", "history-note-only": "ACHTUNG: <PERSON><PERSON>lau<PERSON> enthält nur Textinhalte, nicht <PERSON><PERSON><PERSON>", "referenceResource": "Referenzressource", "to-ask-ai": "<PERSON> zu fragen", "press-enter-to-select-first-result": "Drücken Sie die Eingabetaste, um das erste Ergebnis auszuwählen", "ask-ai": "Fragen Sie Ai", "ask-blinko-ai-about-this-query": "Fragen Sie Blinko AI nach dieser Frage", "search-or-ask-ai": "Suchen Sie Note, Einstellungen oder fragen Sie AI ...", "plugin": "Plugin", "editor-preview": "Editor", "auto-add-tags": "Automatische Tags hinzufügen", "add-as-comment": "Fügen Sie als Kommentar hinzu", "choose-what-to-do-with-ai-results": "<PERSON><PERSON><PERSON><PERSON>, was mit KI -Ergebnissen zu tun ist", "ai-post-processing-mode": "KI -Postverarbeitungsmodus", "ai-post-processing-prompt": "KI-Nachbearbeitungskommentaraufforderung", "2fa-setup-successful": "2FA Setup erfolgreich", "align-center": "Center", "align-left": "Links", "align-right": "<PERSON><PERSON><PERSON>", "alternate-text": "Alternativer Text", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "Analysieren Sie den folgenden Inhalt der Anmerkung und schlagen Sie geeignete Tags vor und geben Sie eine kurze Zusammenfassung an", "check": "Aufgabenliste", "close": "Schließen", "code-theme": "Codeblock -<PERSON><PERSON>", "content-generated-by-ai": "Inhalt von KI erzeugt", "copied": "<PERSON><PERSON><PERSON>", "custom-path": "Benutzerdefinierte Pfad", "define-custom-prompt-for-ai-to-process-notes": "Bedienen Sie die KI, um den aktuellen Hinweis zu kommentieren. Zum Beispiel: Bitte fassen Sie den Inhalt des Hinweises zusammen. Wenn der Inhalt des Hinweises weniger als 10 Wörter enthält, bitte polieren Sie ihn für mich auf.", "devtools": "Devtools", "enter-custom-prompt-for-post-processing": "Geben Sie die benutzerdefinierte Eingabeaufforderung für die Nachbearbeitung ein", "enter-spotify-consumer-key": "Geben Sie den Spotify -API -Schlüssel ein", "enter-spotify-consumer-secret": "<PERSON><PERSON><PERSON> Sie Spotify Consumer Secret ein", "music-settings": "Musikeinstellungen", "operation-in-progress": "Opearation in Arbeit", "prompt-used-for-post-processing-notes": "Eingabeaufforderung für Notizen nach der Verarbeitung", "rebuild-in-progress": "In Arbeit wieder aufbauen", "rest-user-info": "REST -Benutzerinformationen", "select-model": "<PERSON><PERSON> auswählen", "setting": "", "spotify-consumer-key": "Spotify -API -Schlüssel", "spotify-consumer-secret": "Spotify API Secret", "text-is-not-empty": "Text (kein leer)", "title": "Titel", "tooltip-text": "Tooltip -Text", "undo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "up": "Hoch", "update": "Aktualisieren", "updated-at": "Update at", "upload-error": "<PERSON><PERSON> hochladen", "uploading": "Hochladen ...", "user-list": "Benutzerliste", "wysiwyg": "Wysiwyg", "enable-ai-post-processing": "Aktivieren Sie die KI -Postverarbeitung", "automatically-process-notes-after-creation-or-update": "Verarbeiten Sie die Notizen nach der Erstellung automatisch", "can-generate-summaries-tags-or-perform-analysis": "Können Zusammenfassungs -Tags für die Ausführungsanalyse generieren", "ai-post-processing": "KI -Postverarbeitung", "model-list-updated": "<PERSON><PERSON><PERSON> aktualisiert", "to-search-tags": "Tags suchen", "app-upgrade-required": "App-Upgrade er<PERSON><PERSON><PERSON>", "current-app-version": "Aktuelle APP-Version", "required-app-version": "Benötigte APP-Version", "upgrade": "Aktualisierung", "online-search": "Online-Suche", "smart-edit": "Intelligente <PERSON>", "function-call-required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smart-edit-prompt": "Intelligente Bearbeitungsanweisung", "define-instructions-for-ai-to-edit-your-notes": "Sie können Aufforderungen verwenden, um Notizen zu manipulieren. Zum Beispiel: Wenn eine Notiz einen Link enthält, fassen Sie den Inhalt des Links unter der ursprünglichen Notiz zusammen und erzeugen Sie ein Etikett.", "rebuild-started": "Wiederaufbau Gestartet", "rebuild-stopped-by-user": "Wiederaufbau vom Benutzer gestoppt", "random-mode": "Zufallsweg", "related-notes": "Verwandte Notizen", "no-related-notes-found": "<PERSON><PERSON> zugehörigen Notizen gefunden", "advanced": "Fortgeschritten", "rerank-model-description": "Geben Sie ein Modell zur Neuordnung von Vektorenergebnissen an, um die Suchgenauigkeit zu verbessern.", "rerank-model": "Neubewertungsmodell", "rerank": "Neu ordnen", "use-custom-rerank-endpoint-description": "<PERSON><PERSON> aktiv<PERSON>, werden die Endpunkte und API-Schlüssel des eingebetteten Modells neu geordnet", "use-embedding-endpoint": "Verwenden Sie den Einbettungs-Endpunkt", "rerank-score-description": "Legen Sie eine Punktschwelle für das Neuordnungsmodell fest, unterhalb derer Ergebnisse gefiltert werden.", "public-share": "Öffentlicher Anteil", "internal-share": "Interner Anteil", "no-team-members-found": "<PERSON><PERSON>mitglieder gefunden", "selected-users": "Ausgewählte Benutzer", "tags-prompt": "Markierungsaufforderung", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "Aufforderung, die für die automatische Generierung von Tags verwendet wird. <PERSON><PERSON> leer e<PERSON>ste<PERSON>, wird der Standardaufforderung verwendet.", "generate-low-permission-token": "Generieren Si<PERSON> ein Token mit niedrigen Berechtigungen", "low-permission-token-desc": "Tokens mit niedrigen Berechtigungen können nur auf den UpsertNote-Endpunkt und den AI-Chat-Endpunkt zugreifen. Sie können nicht auf Ihre Kontoinformationen oder andere Notizen zugreifen. Dies ist ideal für Anwendungsfälle wie Telegram-Bots oder WeChat-Bots, bei denen Sie sicherstellen möchten, dass sie keinen Zugriff auf andere Notizen haben.", "this-token-is-only-displayed-once-please-save-it-properly": "Dieses <PERSON> wird nur einmal angeze<PERSON>t, bitte speichern Sie es ordnungsgemäß.", "refresh-model-list": "Model<PERSON>te a<PERSON>", "please-set-the-embedding-model": "Bitte das eingebettete Modell einstellen", "enter-blinko-endpoint": "Deine URL für die Bereitstellung von Blinko", "login-failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verification-failed": "Authentifizierung fehlgeschlagen", "download-success": "Download erfolgreich", "download-failed": "Download fehlgeschlagen", "downloading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hide-pc-editor": "Versteckter PC-Editor", "import-from-markdown": "Aus Markdown-<PERSON><PERSON> importieren", "import-from-markdown-tip": "Importieren Sie aus einer einzelnen .md-Datei oder einem .zip-Archiv, das .md-Date<PERSON> enthält.", "not-a-markdown-or-zip-file": "<PERSON><PERSON>- oder Zip-Datei. Bitte wählen Sie eine .md- oder .zip-Datei aus.", "todo": "Vertretung", "restore": "Wiederherstellung", "complete": "Fertigstellung", "today": "<PERSON><PERSON>", "yesterday": "Gestern", "common.refreshing": "Aktualisierung läuft", "common.releaseToRefresh": "Zum Aktualisieren loslassen", "common.pullToRefresh": "Nach unten ziehen zum Aktualisieren", "edit-message-warning": "Das Bearbeiten dieser Nachricht löscht alle nachfolgenden Dialogverläufe und erzeugt neue KI-Antworten.", "enter-your-message": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre Nachricht ein", "set-deadline": "Fälligkeitsdatum festlegen", "expired": "Abgelaufen", "expired-days": "Abgelaufen seit {{count}} Tagen", "expired-hours": "Abgelaufen {{count}} Stunden", "expired-minutes": "Abgelaufen seit {{count}} Minuten", "days-left": "{{count}} <PERSON><PERSON>", "hours-left": "{{count}} <PERSON><PERSON><PERSON> später", "minutes-left": "{{count}} <PERSON><PERSON><PERSON> später", "about-to-expire": "<PERSON>ld ablaufen", "1-day": "1 Tag", "1-week": "eine W<PERSON>e", "1-month": "ein <PERSON>", "quick-select": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "import-ai-configuration": "AI-Konfiguration importieren", "would-you-like-to-import-this-configuration": "Möchten Sie diese AI-Konfiguration importieren?", "detected-ai-configuration-to-import": "AI-Konfiguration zur Importierung erkannt", "importing": "Importieren", "cache-cleared-successfully": "Der Cache wurde erfolgreich gelöscht! Die Seite wird automatisch neu geladen.", "failed-to-clear-cache": "Fehler beim Löschen des Browser-Cache. Bitte versuchen Sie, manuell zu aktualisieren (Strg+Shift+R).", "select-deployment": "Bereitstellung auswählen", "deployment-name": "Bereitstellungsname", "please-set-the-api-endpoint": "Bitte API-Endpunkt festlegen", "please-set-the-api-key": "Bitte API-Schlüssel festlegen"}