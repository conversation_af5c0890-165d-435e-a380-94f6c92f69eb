{"hello": "hello", "blinko": "Blinko", "notes": "Notes", "resources": "Resources", "archived": "Archived", "settings": "Settings", "total-tags": "TOTAL TAGS", "search": "Search...", "i-have-a-new-idea": "I have a new idea...", "note": "Note", "multiple-select": "Multiple select", "convert-to": "Convert to", "delete": "Delete", "recovery": "Recovery", "archive": "Archive", "items": "items", "your-changes-have-been-saved": "Your changes have been saved!", "operation-failed": "Operation failed.", "in-progress": "In progress...", "confirm-to-delete": "Confirm to delete!", "this-operation-removes-the-associated-label-and-cannot-be-restored-please-confirm": "This operation removes the associated label and cannot be restored Please confirm", "add-tag": "Add Tag", "cancel": "Cancel", "no-data-here-well-then-time-to-write-a-note": "No data here~", "basic-information": "Basic Information", "name": "Name", "preference": "Preference", "theme": "Theme", "change-type": "Change type", "insert-hashtag": "Insert hashtag", "bulleted-list": "Bulleted list", "numbered-list": "Numbered list", "check-list": "Check list", "insert-table": "Insert table", "insert-codeblock": "Insert codeBlock", "insert-sandpack": "Insert sandpack", "upload-file": "Upload file", "delete-confirm": "Delete Confirm", "this-operation-will-be-delete-resource-are-you-sure": "This operation will be delete resource are you sure?", "delete-success": "Delete successfully", "update-successfully": "Update successfully", "create-successfully": "Create successfully", "total": "Total", "all-notes-have-been-loaded": "All {{items}} notes have been loaded", "hi-user-name-i-can-search-for-the-notes-for-you-how-can-i-help-you-today": "Hi {{name}}!, I can search for your notes. How can I help you today?", "ask-about-your-notes": "Ask about your notes", "use-ai": "Use AI", "model-provider": "Model Provider", "api-endpoint": "API Endpoint", "must-start-with-http-s-or-use-api-openai-as-default": "Must start with http(s):// or use /api/openai as default", "user-custom-openai-api-key": "User custom OpenAI API Key", "user-custom-azureopenai-api-instance": "Azure OpenAI instance name", "user-custom-azureopenai-api-deployment": "Azure OpenAI deployment name", "user-custom-azureopenai-api-version": "API version", "ai-model": "AI Model", "logout": "Logout", "user-or-password-error": "User or password error", "username": "Username", "enter-your-name": "Enter your name", "password": "Password", "enter-your-password": "Enter your password", "need-to-create-an-account": "Need to create an account?", "sign-up": "Sign Up", "sign-in": "Sign in", "nickname": "Nickname", "change-user-info": "Change User Info", "rest-user-password": "Rest User Password", "confirm-password": "Confirm Password", "confirm-your-password": "Confirm your password", "enter-your-username": "Enter your username", "save": "Save", "keep-sign-in": "Keep sign in", "required-items-cannot-be-empty": "Required items cannot be empty", "the-two-passwords-are-inconsistent": "The two passwords are inconsistent", "create-successfully-is-about-to-jump-to-the-login": "Create successfully, is about to jump to the login", "already-have-an-account-direct-login": "Already have an account? Direct login", "no-tag-found": "No tag found", "new-version-detected-click-to-get-the-latest-version": "New version detected, click to get the latest version", "schedule-task": "Schedule Task", "schedule-back-up": "Schedule Back Up", "every-day": "Every day", "every-week": "Every week", "every-month": "Every month", "every-three-month": "Every three month", "every-half-year": "Every half year", "import": "Import", "import-from-bko": "Import from .bko", "import-from-markdown": "Import from Markdown", "import-from-markdown-tip": "Import from single .md file or .zip archive containing .md files", "not-a-markdown-or-zip-file": "Not a markdown or zip file. Please select a .md or .zip file.", "not-a-bko-file": "not a bko file", "convert-to-note": "Convert to Note", "convert-to-blinko": "Convert to Blinko", "reviewed": "Reviewed", "congratulations-youve-reviewed-everything-today": "you've reviewed everything today.", "name-db": "NAME", "schedule": "SCHEDULE", "last-run": "LAST RUN", "backup-file": "BACKUP FILE", "status": "STATUS", "running": "Running", "stopped": "Stopped", "show-navigation-bar-on-mobile": "Hidden navigation bar on mobile", "schedule-archive-blinko": "Schedule Archive Blinko", "there-are-no-resources-yet-go-upload-them-now": "There are no resources yet go upload them now", "confrim": "Confirm", "daily-review": "Daily Review", "detail": "Detail", "enter-send-shift-enter-for-new-line": "Enter send, Shift+Enter for new line", "show-less": "Show Less", "show-more": "Show More", "top": "Top", "cancel-top": "Cancel Top", "created-in": "Created in", "set-as-public": "Set as Public", "unset-as-public": "Unset as Public", "with-link": "With Link", "no-tag": "No Tag", "has-file": "Has File", "created-at": "Create At", "updated-at": "Update At", "role": "Role", "user-list": "User List", "create-user": "Create User", "action": "Action", "original-password": "Original Password", "edit-user": "Edit User", "import-from-memos-memos_prod-db": "Import from Memos(memos_prod.db)", "when-exporting-memos_prod-db-please-close-the-memos-container-to-avoid-partial-loss-of-data": "When exporting memos_prod.db, please close the memos container to avoid partial loss of data.", "go-to-share-page": "Go to share page", "import-done": "Import done", "rebuilding-embedding-progress": "Rebuilding Embedding Progress", "rebuild-embedding-index": "Rebuild Embedding Index", "rebuild": "Rebuild", "notes-imported-by-other-means-may-not-have-embedded-vectors": "Notes imported by other means may not have embedded vectors", "if-you-have-a-lot-of-notes-you-may-consume-a-certain-number-of-tokens": "If you have a lot of notes you may consume a certain number of tokens", "order-by-create-time": "Order by create time", "time-format": "Time Format", "version": "Version", "new-version-available": "New version available", "server": "Server", "client": "Client", "new-server-version-available": "New server version available", "new-client-version-available": "New client version available", "storage": "Storage", "local-file-system": "Local File System", "object-storage": "Object storage", "in-addition-to-the-gpt-model-there-is-a-need-to-ensure-that-it-is-possible-to-invoke-the": "In addition to the GPT model, there is a need to ensure that it is possible to invoke the", "speech-recognition-requires-the-use-of": "Speech recognition requires the use of", "ai-expand": "AI Expand", "ai-polish": "AI Polish", "accept": "Accept", "reject": "Reject", "stop": "Stop", "card-columns": "Card Columns", "select-a-columns": "Select a columns", "width-less-than-1024px": "Width less than 1024px", "width-less-than": "Width less than", "small-device-card-columns": "Small Device Card Columns", "medium-device-card-columns": "Medium Device Card Columns", "large-device-card-columns": "Large Device Card Columns", "device-card-columns": "<PERSON>ce Card Columns", "columns-for-different-devices": "Columns for different devices", "mobile": "Mobile", "tablet": "Tablet", "desktop": "Desktop", "chars": "Chars", "text-fold-length": "Text Fold Length", "title-first-line-of-the-text": "Title(first line of the text)", "content-rest-of-the-text-if-the-text-is-longer-than-the-length": "Content(rest of the text,if the text is longer than the length)", "ai-tag": "AI Tag", "article": "Article", "embedding-model": "Embedding Model", "force-rebuild": "Force Rebuild", "force-rebuild-embedding-index": "Forced rebuild will rebuild all data that has been indexed completely", "embedding-model-description": "The index must be rebuilt after switching embedded models", "top-k-description": "Maximum number of documents eventually returned", "embedding-score-description": "The similarity threshold for queries is generally the Euclidean sum distance", "embedding-lambda-description": "Query Result Diversity Weighting Parameter", "update-tag-icon": "Update tag icon", "delete-only-tag": "Delete Only Tag", "delete-tag-with-note": "Delete tag with note", "update-name": "Update Name", "update-tag-name": "Update Tag Name", "thinking": "Thinking...", "select-all": "Select All", "deselect-all": "Deselect All", "ai-emoji": "AI Emoji", "custom-icon": "Custom Icon", "ai-enhanced-search": "AI Enhanced Search", "preview-mode": "Preview Mode", "source-code": "Source Code", "camera": "Camera", "reference": "Reference", "reference-note": "Reference Note", "source-code-mode": "Source Code Mode", "heading": "Heading", "paragraph": "Paragraph", "remove-bold": "Remove Bold", "remove-italic": "Remove Italic", "underline": "Underline", "remove-underline": "Remove Underline", "select-block-type": "Select Block Type", "block-type-select-placeholder": "Block Type", "trash": "Recycle Bin", "light-mode": "Light Mode", "dark-mode": "Dark Mode", "follow-system": "Follow System", "custom-path": "Custom Path", "page-size": "<PERSON>", "toolbar-visibility": "Toolbar Visibility", "select-toolbar-visibility": "Select toolbar visibility", "always-show-toolbar": "Always Show", "hide-toolbar-on-mobile": "Hide on Mobile", "always-hide-toolbar": "Always Hide", "select-a-time-format": "Select a time format", "enter-code-shown-on-authenticator-app": "Enter code shown on authenticator app", "open-your-third-party-authentication-app-and-enter-the-codeshown-on-screen": "Open your third-party authentication app, and enter the code shown on screen", "two-factor-authentication": "Two-Factor Authentication", "scan-this-qr-code-with-your-authenticator-app": "Scan this QR code with your authenticator app", "or-enter-this-code-manually": "Or enter this code manually:", "verify": "Verify", "2fa-setup-successful": "2FA setup successful", "about": "About", "days": "Days", "select-model": "Select Model", "select-deployment": "Select Deployment", "select-model-provider": "Select Model Provider", "allow-register": "Allow Register", "access-token": "Access Token", "bucket": "Bucket", "region": "Region", "access-key-secret": "Access key secret", "access-key-id": "Access key id", "copy-share-link": "Copy share link", "share-and-copy-link": "Share and copy link", "endpoint": "Endpoint", "export-format": "Export Format", "time-range": "Time Range", "all": "All", "exporting": "Exporting...", "tag-status": "Tag Status", "all-notes": "All Notes", "with-tags": "With Tags", "without-tags": "Without Tags", "select-tags": "Select Tags", "additional-conditions": "Additional Conditions", "apply-filter": "Apply Filter", "has-image": "Has Image", "has-link": "<PERSON>", "filter-settings": "Filter <PERSON>s", "to": "To", "reset": "Reset", "start-date": "Start Date", "end-date": "End Date", "no-condition": "No Condition", "public": "Public", "exclude-tag-from-embedding": "Exclude Tagged Content", "exclude-tag-from-embedding-tip": "Notes with this tag will be excluded from AI embedding processing", "exclude-tag-from-embedding-desc": "Select a tag to exclude its associated notes from AI embedding vector generation", "ai-model-tooltip": "Enter the model name to use, such as gpt-3.5-turbo, gpt-4, gpt-4o, gpt-4o-mini", "user-custom-azureopenai-api-deployment-tooltip": " Enter the deployment name to use, such as gpt-4o", "ollama-ai-model-tooltip": "Enter the model name to use, such as llama3.2", "ollama-default-endpoint-is-http-localhost-11434": "Ollama default endpoint is http://localhost:11434", "your-azure-openai-instance-name": "Your Azure OpenAI instance name", "align-center": "Center", "align-left": "Left", "align-right": "Right", "alternate-text": "Alternate text", "bold": "Bold", "both": "Both", "check": "Task List", "close": "Close", "code": "Code Block", "code-theme": "Code Block Theme Preview", "column": "Column", "comment": "Comment", "confirm": "Confirm", "content-theme": "Content Theme Preview", "copied": "<PERSON>pied", "copy": "Copy", "delete-column": "Delete Row", "delete-row": "Delete Column", "devtools": "DevTools", "down": "Down", "download-tip": "The browser does not support the download function", "edit": "Edit", "edit-mode": "Toggle Edit Mode", "emoji": "<PERSON><PERSON><PERSON>", "export": "Export", "file-type-error": "file type is error", "footnote-ref": "Footnote Ref", "fullscreen": "Toggle Fullscreen", "generate": "Generating", "headings": "Headings", "heading1": "Heading 1", "heading2": "Heading 2", "heading3": "Heading 3", "heading4": "Heading 4", "heading5": "Heading 5", "heading6": "Heading 6", "help": "Help", "image-url": "image URL", "indent": "Indent", "info": "Info", "inline-code": "Inline Code", "insert-after": "Insert line after", "insert-before": "Insert line before", "insert-column-left": "Insert 1 left", "insert-column-right": "Insert 1 right", "insert-row-above": "Insert 1 above", "insert-row-below": "Insert 1 below", "instant-rendering": "Instant Rendering", "italic": "Italic", "language": "Language", "line": "Line", "link": "Link", "link-ref": "<PERSON>", "list": "List", "more": "More", "name-empty": "Name is empty", "ordered-list": "Order List", "outdent": "Outdent", "outline": "Outline", "over": "over", "performance-tip": "Real-time preview requires ${x}ms, you can close it", "preview": "Preview", "quote": "Quote", "record": "Start Record/End Record", "record-tip": "The device does not support recording", "recording": "Recording...", "redo": "Redo", "remove": "Remove", "row": "Row", "spin": "Spin", "split-view": "Split View", "strike": "Strike", "table": "Table", "text-is-not-empty": "text(no empty)", "title": "Title", "tooltip-text": "Tooltip text", "undo": "Undo", "up": "Up", "update": "Update", "upload": "Upload", "upload-error": "Upload error", "uploading": "Uploading...", "wysiwyg": "WYSIWYG", "search-tags": "Search Tags", "insert-attachment-or-note": "Insert to attachment or note?", "paste-to-note-or-attachment": "Are you sure to paste to context or attachment? ", "context": "Context", "attachment": "Attachment", "after-deletion-all-user-data-will-be-cleared-and-unrecoverable": "After deletion, all user data will be cleared and unrecoverable.", "upload-completed": "Upload completed", "upload-cancelled": "Upload cancelled", "upload-failed": "Upload Failed", "import-from-bko-tip": "Uploading to s3 for recovery is not supported at this time. Please disable the s3 option temporarily when you want to recover.", "music-settings": "Music Settings", "spotify-consumer-key": "Spotify API Key", "spotify-consumer-secret": "Spotify API Secret", "enter-spotify-consumer-key": "Enter Spotify API Key", "enter-spotify-consumer-secret": "Enter Spotify Consumer Secret", "spotify-consumer-key-tip": "Used to get mp3 music cover", "spotify-consumer-key-tip-2": "Get API Key from https://developer.spotify.com/", "edit-time": "Edit Time", "ai-write": "AI Write", "download": "Download", "rename": "<PERSON><PERSON>", "move-up": "Move Up", "cut": "Cut", "paste": "Paste", "confirm-delete-content": "Are you sure you want to delete {{name}}? This action cannot be undone.", "confirm-delete": "Confirm Delete", "folder-name": "Folder Name", "file-name": "File Name", "operation-success": "Operation Successful", "cloud-file": "Cloud File", "move-to-parent": "Move To Parent", "no-resources-found": "No Resources Found", "operation-in-progress": "Opearation in progress", "new-folder": "New Folder", "folder-name-required": "Folder Name Required", "folder-name-exists": "Folder Name Exists", "show-all": "Show all", "collapse": "Collapse", "sun": "Sun", "mon": "Mon", "tue": "<PERSON><PERSON>", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "wed": "Wed", "heatMapTitle": "Heat map of notes from the past year", "heatMapDescription": "Shows the number of notes created per day", "select-month": "Select Month", "note-count": "Note Count", "total-words": "Total Words", "max-daily-words": "Max Daily Words", "active-days": "Active Days", "analytics": "Analytics", "tag-distribution": "Tag Distribution", "other-tags": "Other Tags", "offline-status": "Offline Mode", "offline-title": "You are offline", "offline-description": "Please check your internet connection and try again", "retry": "Retry", "back-to-home": "Back to Home", "offline": "Offline", "close-background-animation": "Close Background Animation", "custom-background-url": "Custom Background", "custom-bg-tip": "Go to https://www.shadergradient.co/ to create your own gradient background", "share": "Share", "need-password-to-access": "Password access required", "password-error": "Password error", "create-share": "Create Share", "cancel-share": "Cancel Share", "share-link": "Share Link", "set-access-password": "Set access password", "protect-your-shared-content": "Protect your shared content", "access-password": "Access password", "select-date": "Select date", "expiry-time": "Expiry Time", "select-expiry-time": "Select Expiry Time", "permanent-valid": "Permanent Valid", "7days-expiry": "7 Days Expiry", "30days-expiry": "30 Days Expiry", "custom-expiry": "Custom Expiry", "share-link-expired": "Share Link Expired", "share-link-expired-desc": "This share has expired please contact the administrator to re-share!", "shared": "Shared", "internal-shared": "Internal Shared", "edited": "Edited", "move-down": "Move Down", "provider-id": "Provider ID", "provider-name": "Provider Name", "well-known-url": "WellKnown URL", "authorization-url": "Authorization URL", "token-url": "Token URL", "userinfo-url": "User Info URL", "scope": "<PERSON><PERSON>", "client-id": "Client ID", "client-secret": "Client Secret", "sso-settings": "SSO Settings", "oauth2-providers": "Oauth2 Providers", "add-oauth2-provider": "Add Oauth2 Provider", "add-provider": "Add Provider", "edit-oauth2-provider": "Edit Oauth2 Provider", "confirm-delete-provider": "Confirm Delete Provider", "provider-icon": "Provider Icon", "please-select-icon-from-iconify": "Please select icon from iconify", "provider-template": "Provider Template", "select-provider-template": "Select Provider Template", "please-add-this-url-to-your-oauth-provider-settings": "Please add this url to your Oauth provider settings", "redirect-url": "Redirect url", "sign-in-with-provider": "Sign in with {{ provider }}", "community": "Community", "theme-color": "Theme Color", "rest-user-info": "Rest User Info", "link-account": "Link Account", "select-account": "Select Account", "link-account-warning": "Please note that if you link your accounts, any data from the current account will not be synced to the linked account.", "unlink-account": "Unlink Account", "unlink-account-tips": "Do you confirm access to all associations with this account?", "login-type": "Login Type", "close-daily-review": "Close Daily Review", "max-home-page-width": "Max Home Page Width", "max-home-page-width-tip": "If set to 0 it is the maximum width", "reply-to": "Reply To", "author": "Author", "from": "From", "no-comments-yet": "No comments yet", "hub": "<PERSON><PERSON>", "home-site": "Home Site", "use-blinko-hub": "Use Blinko Hub", "full-screen": "Full Screen", "exit-fullscreen": "Exit Fullscreen", "no-note-associated": "No note associated", "insert-context": "Insert to context", "follow": "Follow", "follower": "Follower", "following": "Following", "admin": "Webmaster", "site-url": "Blinko Site URL", "unfollow": "Unfollow", "join-hub": "Join <PERSON>", "refresh": "Refresh", "comment-notification": "Comment Notification", "follow-notification": "Follow Notification", "followed-you": "followed you", "mark-all-as-read": "Mark all as read", "no-notification": "No notification", "new-notification": "New Notification", "notification": "Notification", "system-notification": "System Notification", "backup-success": "Backup Success", "embedding-api-endpoint": "Embedding API Endpoint", "embedding-api-key": "Embedding API Key", "recommand": "Recommand", "has-todo": "Has TO-DO", "reference-by": "Reference By", "hide-notification": "Hide Notification", "search-settings": "Search Settings...", "this-operation-will-delete-the-selected-files-and-cannot-be-restored-please-confirm": "This operation will delete the selected files and cannot be restored. Please confirm", "plugin-settings": "Plugin Setting", "installed-plugins": "Installed", "marketplace": "Marketplace", "local-development": "Local Development", "local-plugin": "Local Plugin", "add-local-plugin": "Add Local Plugin", "uninstall": "Uninstall", "install": "Install", "downloads": "Downloads", "plugin-updated": "Plugin Updated", "plugin-update-failed": "Plugin Update Failed", "plugin-connection-failed": "Plugin Connection Failed", "disconnect": "Disconnect", "local-development-description": "Add a local development plug-in and debug it.", "ai": "AI", "embedding-dimensions": "Embedding Dimensions", "embedding-dimensions-description": "You need to ensure that the model dimensions are correct, and you need to force index records to be rebuilt after changes", "ai-chat-box-notes": "Below are the relevant notes retrieved for you", "add-to-blinko": "Add to Blinko", "add-to-note": "Add to Note", "no-title": "No Titile", "search-blinko-content-or-help-create": "Search Blinko content or help create...", "conversation-history": "Conversation history", "new-conversation": "New Chat", "knowledge-base-search": "Knowledge base search", "add-tools-to-model": "Search online or allow AI to call the Blinko API", "clear-current-content": "Clear current content", "welcome-to-blinko": "Welcome, {{name}}", "content-generated-by-ai": "CONTENT GENERATED BY AI", "ai-prompt-writing": "You are a professional writer, please write a professional article about the topic provided by the user.", "ai-prompt-coding": "You are a professional coder, please write a simple Python program based on the topic provided by the user.", "ai-prompt-translation": "You are a professional translator, please translate the text provided by the user into {{lang}}", "writing": "Writing", "coding": "Coding", "translation": "Translation", "total-tokens": "Total tokens", "first-char-delay": "First char delay", "check-connect": "Check", "check-connect-success": "Check Connect Success", "check-connect-error": "The connection failure may be added to the end of /v1", "loading": "Loading", "model": "Model", "deployment-name": "Deployment Name", "ai-tools": "AI Tools", "tavily-max-results": "<PERSON><PERSON>", "tavily-api-key": "Tavily Search API Key", "ai-prompt-writing-content": "Write a 200-word article and save it in your notes", "ai-prompt-coding-content": "Extracting https://github.com/blinko-space/blinko web content", "rebuild-in-progress": "Rebuild in progress", "processing": "Processing", "stop-task": "Stop task", "there-is-a-rebuild-task-in-progress-do-you-want-to-restart": "There is a rebuild task in progress, do you want to restart?", "hide-blog-images": "Hide blog images", "ai-prompt-translation-content": "Check the no tags notes in the past two days and tag them.", "ai-prompt-delete-content": "Find 2 archived notes, summarize and save them as new notes, and delete these two archived notes", "newer": "Newer", "older": "Older", "restore-this-version": "Restore this version", "Note History": "Note History", "View History Versions": "View History Versions", "history-note-only": "Attention: This history contains only text content, not file history", "referenceResource": "Reference Resource", "to-ask-ai": "To ask AI", "press-enter-to-select-first-result": "Press enter to select first result", "ask-ai": "Ask AI", "ask-blinko-ai-about-this-query": "Ask Blinko AI about this query", "search-or-ask-ai": "Search note, settings or ask AI ...", "plugin": "Plugin", "editor-preview": "Editor & Preview", "add-as-comment": "Add as comment", "auto-add-tags": "Auto add tags", "ai-post-processing-mode": "AI Post Processing Mode", "choose-what-to-do-with-ai-results": "Choose what to do with AI results", "ai-post-processing-prompt": "AI Post Processing Comment Prompt", "define-custom-prompt-for-ai-to-process-notes": "Operate the AI to comment on the current note. For example: Please summarize the content of the note. If the note content is less than 10 words, please polish it for me.", "prompt-used-for-post-processing-notes": "Prompt used for post processing notes", "analyze-the-following-note-content-and-suggest-appropriate-tags-and-provide-a-brief-summary": "Analyze the following note content and suggest appropriate tags and provide a brief summary", "enter-custom-prompt-for-post-processing": "Enter custom prompt for post processing", "enable-ai-post-processing": "Enable AI Post Processing", "automatically-process-notes-after-creation-or-update": "Automatically process notes after creation", "can-generate-summaries-tags-or-perform-analysis": "Can generate summaries tags on perform analysis", "ai-post-processing": "AI Post Processing", "model-list-updated": "Model list updated", "http-proxy": "HTTP Proxy", "use-http-proxy": "Use HTTP Proxy", "enable-http-proxy-for-accessing-github-or-ai-api-endpoints": "Enable HTTP proxy for accessing GitHub or AI API endpoints", "proxy-host": "Proxy Host", "proxy-port": "Proxy Port", "proxy-username": "Proxy Username", "proxy-password": "Proxy Password", "ip-address-or-hostname-for-proxy-server": "IP address or hostname for proxy server", "port-number-for-proxy-server": "Port number for proxy server", "optional-username-for-authenticated-proxy": "Optional username for authenticated proxy", "optional-password-for-authenticated-proxy": "Optional password for authenticated proxy", "optional": "Optional", "test-proxy-connection": "Test Proxy Connection", "test-if-the-proxy-is-working-correctly": "Test if the proxy is working correctly with the current settings", "test": "Test", "response-time": "Response Time", "status-code": "Status Code", "error": "Error", "error-code": "Error Code", "error-details": "<PERSON><PERSON><PERSON>", "check-proxy-settings-and-ensure-server-is-accessible": "Check your proxy settings and ensure the server is accessible", "do-not-include-http-or-https-prefix": "Do not include http:// or https:// prefix", "to-search-tags": "To search tags", "app-upgrade-required": "App upgrade required", "current-app-version": "Current APP version", "required-app-version": "Required APP version", "upgrade": "Upgrade", "online-search": "Online Search", "smart-edit": "Smart Edit", "function-call-required": "Function Call Required", "smart-edit-prompt": "Smart Edit Prompt", "define-instructions-for-ai-to-edit-your-notes": "You can use prompts to manipulate notes, for example: If a note contains a link, summarize the link content below the original note and generate a label.", "rebuild-started": "Rebuild Started", "rebuild-stopped-by-user": "Rebuild stopped by user", "random-mode": "Random Walk", "related-notes": "Related notes", "no-related-notes-found": "No related notes found", "advanced": "Advanced", "rerank-model-description": "Specify a model for reordering vector results to improve search accuracy", "rerank-model": "Rerank model", "rerank": "<PERSON><PERSON>", "use-custom-rerank-endpoint-description": "When enabled, the endpoints and API keys of the embedded model will be reordered", "use-embedding-endpoint": "Use embedding endpoint", "rerank-score-description": "Set a score threshold for the reordering model, below which results will be filtered.", "public-share": "Public Share", "internal-share": "Internal Share", "no-team-members-found": "No team members found", "selected-users": "Selected users", "tags-prompt": "Tags Prompt", "ai-tag-prompt-default": "", "greater-than-prompt-used-for-auto-generating-tags-if-set-empty-the-default-prompt-will-be-used": "Prompt used for auto-generating tags.If set empty, the default prompt will be used.", "generate-low-permission-token": "Generate Low Permission Token", "low-permission-token-desc": "Low-permission tokens​ can only access the ​upsertNote endpoint​ and the ​AI chat endpoint. They ​cannot​ access your account information or other notes. This is ideal for use cases such as ​Telegram bots​ or ​WeChat bots, where you want to ensure they ​cannot access any other notes.", "this-token-is-only-displayed-once-please-save-it-properly": "This token is only displayed once, please save it properly", "refresh-model-list": "Obtain model list", "please-set-the-embedding-model": "Please set up the embedded model", "please-set-the-api-endpoint": "Please set up the API Endpoint", "please-set-the-api-key": "Please set the API key", "blinko-endpoint": "Blinko Endpoint", "enter-blinko-endpoint": "Your Blinko deployment URL", "login-failed": "<PERSON><PERSON> failed", "verification-failed": "Authentication failed", "download-success": "Download successful", "download-failed": "Download failed", "downloading": "Downloading", "hide-pc-editor": "Hide PC-side editor", "todo": "TODO", "restore": "Recover", "complete": "Complete", "today": "Today", "yesterday": "Yesterday", "common.refreshing": "Refreshing", "common.releaseToRefresh": "Release to refresh", "common.pullToRefresh": "Pull down to refresh", "edit-message": "Edit Message", "edit-message-warning": "Editing this message will clear all subsequent conversation records and regenerate AI responses.", "enter-your-message": "Enter your message", "confirm-and-regenerate": "Confirm and Regenerate", "share-conversation": "Share Conversation", "share-link-copied": "Share link copied to clipboard", "ai-conversation-share": "Blinko AI Conversation Share", "shared-at": "Shared at", "loading-shared-conversation": "Loading shared conversation...", "invalid-share-link": "Invalid share link", "conversation-not-found": "Shared conversation not found or not public", "load-conversation-failed": "Failed to load shared conversation", "conversation-not-exist": "Shared conversation does not exist", "load-failed": "Load failed", "powered-by-blinko": "Powered by Blinko", "pull-to-refresh": "Pull to refresh", "conversation-id-missing": "Conversation ID missing", "shared-by": "Shared by", "set-deadline": "Set a deadline", "expired": "Expired", "days-left": "{{count}} days left", "hours-left": "{{count}} hours left", "minutes-left": "{{count}} minutes left", "about-to-expire": "About to expire", "just-expired": "Just expired", "expired-days": "Expired {{count}} days", "expired-hours": "Expired {{count}} hours", "expired-minutes": "Expired {{count}} minutes", "1-day": "1 day", "1-week": "One week", "1-month": "One month", "quick-select": "Quick Selection", "import-ai-configuration": "Import AI configuration", "would-you-like-to-import-this-configuration": "Do you want to import this AI configuration?", "detected-ai-configuration-to-import": "Detected that AI configuration needs to be imported.", "importing": "Importing", "maintenance": "Maintenance", "clear-browser-cache": "<PERSON> Browser Cache", "clear-cache": "<PERSON>ache", "cache-cleared-successfully": "Cache cleared successfully! Page will reload automatically.", "failed-to-clear-cache": "Failed to clear browser cache. Please try manual refresh (Ctrl+Shift+R)."}