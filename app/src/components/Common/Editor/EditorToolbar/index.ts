export const Emoji = {
  name: 'emoji',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8"><path d="M3 12a9 9 0 1 0 18 0a9 9 0 1 0-18 0m6-2h.01M15 10h.01"/><path d="M9.5 15a3.5 3.5 0 0 0 5 0"/></g></svg>',
}
//https://icon-sets.iconify.design/tabler/?icon-filter=italic

export const Check = {
  name: 'check',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M3.5 5.5L5 7l2.5-2.5m-4 7L5 13l2.5-2.5m-4 7L5 19l2.5-2.5M11 6h9m-9 6h9m-9 6h9"/></svg>',
}

export const Italic = {
  name: 'italic',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M11 5h6M7 19h6m1-14l-4 14"/></svg>',
}

export const Table = {
  name: 'table',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M3 5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2zm0 5h18M10 3v18"/></svg>',
}

export const Headings = {
  name: 'headings',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M7 12h10M7 5v14M17 5v14m-2 0h4M15 5h4M5 19h4M5 5h4"/></svg>',
}

export const Bold = {
  name: 'bold',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M7 5h6a3.5 3.5 0 0 1 0 7H7zm6 7h1a3.5 3.5 0 0 1 0 7H7v-7"/></svg>'
}

export const Strike = {
  name: 'strike',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M5 12h14m-3-5.5A4 2 0 0 0 12 5h-1a3.5 3.5 0 0 0 0 7h2a3.5 3.5 0 0 1 0 7h-1.5a4 2 0 0 1-4-1.5"/></svg>',
}

export const Indent = {
  name: 'indent',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M20 6H9m11 6h-7m7 6H9M4 8l4 4l-4 4"/></svg>',
}

export const Outdent = {
  name: 'outdent',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M20 6h-7m7 6h-9m9 6h-7M8 8l-4 4l4 4"/></svg>',
}

export const Link = {
  name: 'link',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="m9 15l6-6m-4-3l.463-.536a5 5 0 0 1 7.071 7.072L18 13m-5 5l-.397.534a5.07 5.07 0 0 1-7.127 0a4.97 4.97 0 0 1 0-7.071L6 11"/></svg>',
}

export const List = {
  name: 'list',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M9 6h11M9 12h11M9 18h11M5 6v.01M5 12v.01M5 18v.01"/></svg>',
}

export const OrderedList = {
  name: 'ordered-list',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M11 6h9m-9 6h9m-8 6h8M4 16a2 2 0 1 1 4 0c0 .591-.5 1-1 1.5L4 20h4M6 10V4L4 6"/></svg>',
}

export const Code = {
  name: 'code',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M15 12h.01M12 12h.01M9 12h.01M6 19a2 2 0 0 1-2-2v-4l-1-1l1-1V7a2 2 0 0 1 2-2m12 14a2 2 0 0 0 2-2v-4l1-1l-1-1V7a2 2 0 0 0-2-2"/></svg>',
}

export const InlineCode = {
  name: 'inline-code',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M9 12h6m-9 7a2 2 0 0 1-2-2v-4l-1-1l1-1V7a2 2 0 0 1 2-2m12 14a2 2 0 0 0 2-2v-4l1-1l-1-1V7a2 2 0 0 0-2-2"/></svg>',
}

export const Fullscreen = {
  name: 'fullscreen',
  tipPosition: 'e',
  className: 'right',
  icon: '',
}

export const Outline = {
  name: 'outline',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8"><path d="M3 7a4 4 0 0 1 4-4h10a4 4 0 0 1 4 4v10a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4z"/><path d="M12 7c1.956 0 3.724.802 5 2.095l-2.956 2.904a3 3 0 0 0-2.038-.799a3 3 0 0 0-2.038.798L7.012 9.095a6.98 6.98 0 0 1 5-2.095z"/></g></svg>',
}

export const Export = {
  name: 'export',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8"><path d="M14 3v4a1 1 0 0 0 1 1h4"/><path d="M11.5 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v5m-5 6h7m-3-3l3 3l-3 3"/></g></svg>',
}

export const Preview = {
  name: 'preview',
  tipPosition: 'e',
  className: 'right',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M4 12V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v6m-10 6H3m18 0h-7m-8-3l-3 3l3 3m12-6l3 3l-3 3"/></svg>',
}

export const MorePC = {
  name: 'more',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><g fill="currentColor"><circle cx="6" cy="12" r="1.75"/><circle cx="12" cy="12" r="1.75"/><circle cx="18" cy="12" r="1.75"/></g></svg>',
  toolbar: [
    Outline,
    Export,
    Preview,
  ],
}

export const MoreMobile = {
  name: 'more',
  icon: '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"><g fill="currentColor"><circle cx="6" cy="12" r="1.75"/><circle cx="12" cy="12" r="1.75"/><circle cx="18" cy="12" r="1.75"/></g></svg>',
  toolbar: [
    Table,
    Headings,
    Code,
    InlineCode,
    Outline,
    Export,
    Preview,
  ],
}

export const ToolbarPC = [
  Emoji,
  Headings,
  Bold,
  Italic,
  Strike,
  Link,
  List,
  OrderedList,
  Check,
  Table,
  Code,
  InlineCode,
  MorePC
]

export const ToolbarMobile = [
  Emoji,
  Bold,
  Italic,
  Strike,
  // Indent,
  // Outdent,
  Link,
  List,
  OrderedList,
  Check,
  MoreMobile
]
// emoji，headings，bold，italic，strike，|，line，quote，list，ordered-list，check ,outdent ,indent，code，inline-code，insert-after，insert-before ,undo，redo，upload，link，table，record，edit-mode，both，preview，fullscreen，outline，code-theme，content-theme，export, devtools，info，help，br