export const i18nEditor = (t) => {
  return {
    alignCenter: t('align-center'),
    alignLeft: t('align-left'),
    alignRight: t('align-right'),
    alternateText: t('alternate-text'),
    bold: t('bold'),
    both: t('editor-preview'),
    check: t('check'),
    close: t('close'),
    code: t('code'),
    "code-theme": t('code-theme'),
    column: t('column'),
    comment: t('comment'),
    confirm: t('confirm'),
    "content-theme": t('content-theme'),
    copied: t('copied'),
    copy: t('copy'),
    "delete-column": t('delete-column'),
    "delete-row": t('delete-row'),
    devtools: t('devtools'),
    down: t('down'),
    downloadTip: t('download-tip'),
    edit: t('edit'),
    "edit-mode": t('edit-mode'),
    emoji: t('emoji'),
    export: t('export'),
    fileTypeError: t('file-type-error'),
    footnoteRef: t('footnote-ref'),
    fullscreen: t('fullscreen'),
    generate: t('generate'),
    headings: t('headings'),
    'heading1': t('heading1'),
    'heading2': t('heading2'),
    'heading3': t('heading3'),
    'heading4': t('heading4'),
    'heading5': t('heading5'),
    'heading6': t('heading6'),
    help: t('help'),
    imageURL: t('image-url'),
    indent: t('indent'),
    info: t('info'),
    "inline-code": t('inline-code'),
    "insert-after": t('insert-after'),
    "insert-before": t('insert-before'),
    insertColumnLeft: t('insert-column-left'),
    insertColumnRight: t('insert-column-right'),
    insertRowAbove: t('insert-row-above'),
    insertRowBelow: t('insert-row-below'),
    instantRendering: t('instant-rendering'),
    italic: t('italic'),
    language: t('language'),
    line: t('line'),
    link: t('link'),
    linkRef: t('link-ref'),
    list: t('list'),
    more: t('more'),
    nameEmpty: t('name-empty'),
    "ordered-list": t('ordered-list'),
    outdent: t('outdent'),
    outline: t('outline'),
    over: t('over'),
    performanceTip: t('performance-tip'),
    preview: t('preview'),
    quote: t('quote'),
    record: t('record'),
    "record-tip": t('record-tip'),
    recording: t('recording'),
    redo: t('redo'),
    remove: t('remove'),
    row: t('row'),
    spin: t('spin'),
    splitView: t('split-view'),
    strike: t('strike'),
    table: t('table'),
    textIsNotEmpty: t('text-is-not-empty'),
    title: t('title'),
    tooltipText: t('tooltip-text'),
    undo: t('undo'),
    up: t('up'),
    update: t('update'),
    upload: t('upload'),
    uploadError: t('upload-error'),
    uploading: t('uploading'),
    wysiwyg: t('wysiwyg'),
  }

}