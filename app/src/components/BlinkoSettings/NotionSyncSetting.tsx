import { observer } from "mobx-react-lite";
import { In<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Card, CardBody, CardHeader, Divider } from "@heroui/react";
import { RootStore } from "@/store/root";
import { BlinkoStore } from "@/store/blinkoStore";
import { api } from "@/lib/trpc";
import { PromiseCall } from "@/store/standard/PromiseState";
import { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { ToastPlugin } from "@/store/module/Toast/Toast";
import { useTranslation } from "react-i18next";

const Item = observer(({ leftContent, rightContent }: { leftContent: any, rightContent: any }) => {
  return (
    <div className="flex flex-col md:flex-row md:items-center justify-between py-2 gap-2">
      <div className="text-sm font-medium">{leftContent}</div>
      <div className="flex items-center gap-2">{rightContent}</div>
    </div>
  );
});

export const NotionSyncSetting = observer(() => {
  const { t } = useTranslation();
  const blinko = RootStore.Get(BlinkoStore);
  const [notionApiToken, setNotionApiToken] = useState('');
  const [notionDatabaseId, setNotionDatabaseId] = useState('');
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncStats, setSyncStats] = useState<any>(null);
  const [syncStatus, setSyncStatus] = useState<any>(null);

  // 初始化配置值
  useEffect(() => {
    if (blinko.config.value) {
      setNotionApiToken(blinko.config.value.notionApiToken || '');
      setNotionDatabaseId(blinko.config.value.notionDatabaseId || '');
    }
  }, [blinko.config.value]);

  // 获取同步状态和统计
  useEffect(() => {
    const fetchSyncInfo = async () => {
      try {
        const [stats, status] = await Promise.all([
          api.task.getNotionSyncStats.query(),
          api.task.getNotionSyncStatus.query()
        ]);
        setSyncStats(stats);
        setSyncStatus(status);
      } catch (error) {
        console.error('Failed to fetch sync info:', error);
      }
    };

    fetchSyncInfo();
    // 每30秒刷新一次状态
    const interval = setInterval(fetchSyncInfo, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleSaveConfig = async (key: string, value: any) => {
    try {
      await PromiseCall(api.config.update.mutate({ key, value }));
      await blinko.config.call();
      ToastPlugin.success('配置已保存');
    } catch (error) {
      ToastPlugin.error('保存配置失败');
      console.error('Failed to save config:', error);
    }
  };

  const handleTestConnection = async () => {
    if (!notionApiToken) {
      ToastPlugin.error('请先输入Notion API Token');
      return;
    }

    setIsTestingConnection(true);
    try {
      // 先保存token
      await handleSaveConfig('notionApiToken', notionApiToken);

      // 这里可以添加连接测试的API调用
      // const result = await api.notion.testConnection.query();

      ToastPlugin.success('Notion连接测试成功！');
    } catch (error) {
      ToastPlugin.error('Notion连接测试失败');
      console.error('Connection test failed:', error);
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleManualSync = async () => {
    if (!blinko.config.value?.notionSyncEnabled) {
      ToastPlugin.error('请先启用Notion同步');
      return;
    }

    setIsSyncing(true);
    try {
      const result = await api.task.manualNotionSync.mutate();
      if (result.success) {
        ToastPlugin.success(`同步完成！已同步 ${result.syncedCount} 条笔记`);
      } else {
        ToastPlugin.error(`同步失败：${result.errors.join(', ')}`);
      }

      // 刷新状态
      const [stats, status] = await Promise.all([
        api.task.getNotionSyncStats.query(),
        api.task.getNotionSyncStatus.query()
      ]);
      setSyncStats(stats);
      setSyncStatus(status);
    } catch (error) {
      ToastPlugin.error('手动同步失败');
      console.error('Manual sync failed:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex gap-3">
        <Icon icon="simple-icons:notion" className="text-2xl" />
        <div className="flex flex-col">
          <p className="text-md font-semibold">Notion 同步设置</p>
          <p className="text-small text-default-500">将Blinko笔记备份到Notion</p>
        </div>
      </CardHeader>
      <Divider />
      <CardBody className="gap-4">
        
        {/* API Token 配置 */}
        <Item
          leftContent={<>Notion API Token</>}
          rightContent={
            <div className="flex gap-2 items-center w-full md:w-auto">
              <Input
                type="password"
                placeholder="输入Notion API Token"
                value={notionApiToken}
                onChange={(e) => setNotionApiToken(e.target.value)}
                onBlur={() => handleSaveConfig('notionApiToken', notionApiToken)}
                className="w-[200px] md:w-[300px]"
              />
              <Button
                size="sm"
                color="primary"
                variant="flat"
                isLoading={isTestingConnection}
                onClick={handleTestConnection}
              >
                测试连接
              </Button>
            </div>
          }
        />

        {/* Database ID 配置 */}
        <Item
          leftContent={<>Notion Database ID</>}
          rightContent={
            <Input
              placeholder="自动创建或手动输入Database ID"
              value={notionDatabaseId}
              onChange={(e) => setNotionDatabaseId(e.target.value)}
              onBlur={() => handleSaveConfig('notionDatabaseId', notionDatabaseId)}
              className="w-[200px] md:w-[300px]"
            />
          }
        />

        {/* 同步开关 */}
        <Item
          leftContent={<>启用Notion同步</>}
          rightContent={
            <Switch
              isSelected={blinko.config.value?.notionSyncEnabled ?? false}
              onChange={async (e) => {
                await handleSaveConfig('notionSyncEnabled', e.target.checked);
              }}
            />
          }
        />

        <Divider />

        {/* 同步状态信息 */}
        {syncStats && (
          <div className="space-y-3">
            <h4 className="text-md font-semibold">同步统计</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-lg font-bold text-success">{syncStats.totalSynced}</div>
                <div className="text-default-500">已同步</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-danger">{syncStats.totalErrors}</div>
                <div className="text-default-500">错误数</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-primary">{syncStats.successRate}%</div>
                <div className="text-default-500">成功率</div>
              </div>
              <div className="text-center">
                <div className="text-sm font-medium">
                  {syncStats.lastSyncTime ? formatDate(syncStats.lastSyncTime) : '未同步'}
                </div>
                <div className="text-default-500">上次同步</div>
              </div>
            </div>
          </div>
        )}

        {/* 当前状态 */}
        {syncStatus && (
          <div className="space-y-2">
            <h4 className="text-md font-semibold">当前状态</h4>
            <div className="flex items-center gap-2">
              <Icon 
                icon={syncStatus.isRunning ? "eos-icons:loading" : "material-symbols:check-circle"} 
                className={`text-lg ${syncStatus.isRunning ? 'text-warning' : 'text-success'}`}
              />
              <span className="text-sm">
                {syncStatus.isRunning ? '正在同步...' : '空闲'}
              </span>
            </div>
            {syncStatus.lastError && (
              <div className="text-sm text-danger">
                最后错误: {syncStatus.lastError}
              </div>
            )}
          </div>
        )}

        <Divider />

        {/* 操作按钮 */}
        <div className="flex gap-2">
          <Button
            color="primary"
            variant="flat"
            startContent={<Icon icon="material-symbols:sync" />}
            isLoading={isSyncing}
            onClick={handleManualSync}
            isDisabled={!blinko.config.value?.notionSyncEnabled}
          >
            手动同步
          </Button>
          
          <Button
            color="default"
            variant="flat"
            startContent={<Icon icon="material-symbols:refresh" />}
            onClick={async () => {
              const [stats, status] = await Promise.all([
                api.task.getNotionSyncStats.query(),
                api.task.getNotionSyncStatus.query()
              ]);
              setSyncStats(stats);
              setSyncStatus(status);
              ToastPlugin.success('状态已刷新');
            }}
          >
            刷新状态
          </Button>
        </div>

        {/* 帮助信息 */}
        <div className="text-xs text-default-500 space-y-1">
          <p>• 获取Notion API Token: 访问 https://www.notion.so/my-integrations</p>
          <p>• 如果未设置Database ID，系统将自动创建新的数据库</p>
          <p>• 同步任务默认每天凌晨执行，只同步新增和修改的笔记</p>
        </div>

      </CardBody>
    </Card>
  );
});
