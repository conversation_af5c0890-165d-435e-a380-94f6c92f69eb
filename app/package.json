{"name": "@blinko/frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cd ../server && bun run dev", "build": "bun run build:web", "icon": "bun run tauri icon", "build:iconify:icons": "bun run src/components/Common/Iconify/buildIcons.js", "build:web": "vite build", "tauri:dev": "RUST_BACKTRACE=1 tauri dev", "tauri:android:dev": "tauri android dev --host", "tauri:android:build": "tauri android build", "tauri:desktop:build": "tauri build"}, "dependencies": {"@heroui/react": "^2.8.0-beta.1", "@hirohe/react-watermark": "^0.5.0", "@iconify/react": "^5.2.1", "@iconify/utils": "^2.3.0", "@react-spring/three": "^9.7.5", "@react-three/fiber": "^8.17.10", "@shadergradient/react": "^2.0.19", "@tailwindcss/vite": "^4.1.4", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.1", "@tauri-apps/plugin-fs": "^2.2.1", "@tauri-apps/plugin-http": "^2.4.3", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-os": "^2.2.1", "@tauri-apps/plugin-process": "~2", "@tauri-apps/plugin-updater": "~2", "@tauri-apps/plugin-upload": "^2.2.1", "@trpc/client": "^11.1.0", "@types/nprogress": "^0.2.3", "boring-avatars": "^1.11.2", "canvas-confetti": "^1.9.3", "copy-to-clipboard": "^3.3.3", "echarts": "^5.6.0", "emoji-picker-react": "^4.12.2", "filesize": "^8.0.7", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "markmap-lib": "^0.18.11", "markmap-view": "^0.18.10", "mermaid": "^11.6.0", "mobx-react-lite": "^4.1.0", "motion": "^12.7.3", "next-themes": "^0.4.6", "nprogress": "^0.2.0", "qrcode.react": "^4.2.0", "rctx-contextmenu": "^1.4.1", "react": "^18.3.1", "react-accessible-treeview": "^2.11.1", "react-beautiful-dnd-next": "^11.0.5", "react-burger-menu": "^3.1.0", "react-dev-inspector": "^2.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-file-icon": "^1.6.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-masonry-css": "^1.0.16", "react-photo-view": "^1.2.7", "react-router-dom": "^7.5.0", "react-swipeable": "^7.0.2", "react-syntax-highlighter": "^15.6.1", "react-webcam": "^7.2.0", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-task-list": "^0.0.3", "swiper": "^11.2.6", "systemjs": "^6.15.1", "tauri-plugin-blinko-api": "file:./tauri-plugin-blinko", "three": "^0.174.0", "usehooks-ts": "^3.1.1", "vanilla-tilt": "^1.8.1", "vditor": "3.11.1"}, "devDependencies": {"@headlessui/tailwindcss": "^0.2.2", "@iconify/json": "^2.2.331", "@tailwindcss/typography": "^0.5.16", "@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/systemjs": "^6.15.3", "@types/three": "^0.174.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "tailwindcss": "^4.1.4", "tailwindcss-animate": "^1.0.7", "typescript": "~5.6.2", "vite": "^6.0.3"}}