{"$schema": "https://schema.tauri.app/config/2", "productName": "Blinko", "version": "1.1.2", "identifier": "com.blinko.app", "build": {"beforeDevCommand": "bun run dev", "devUrl": "http://localhost:1111", "beforeBuildCommand": "bun run build", "frontendDist": "../../dist/public"}, "app": {"withGlobalTauri": true, "security": {"csp": null}, "windows": [{"title": "Blinko", "width": 1024, "height": 768, "fullscreen": false, "resizable": true, "focus": true, "titleBarStyle": "Overlay", "hiddenTitle": true, "decorations": false}]}, "bundle": {"createUpdaterArtifacts": true, "active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"updater": {"pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IENBNzZBMzZDRTUxQUM4RjcKUldUM3lCcmxiS04yeXYyOGZ0RVVBbE42WDMxUXFiQTI0R3RqT0ZBbkZEcFFRNlZTWVhwZzlwRmkK", "endpoints": ["https://github.com/blinkospace/blinko/releases/latest/download/latest.json"]}}}