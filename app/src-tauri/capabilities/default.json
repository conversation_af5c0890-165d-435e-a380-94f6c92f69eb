{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "opener:default", "core:window:default", "core:window:allow-start-dragging", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-unmaximize", "core:window:allow-close", "core:window:allow-set-size", "core:window:allow-set-focus", "core:window:allow-is-maximized", "core:window:allow-toggle-maximize", "core:app:default", "fs:default", "dialog:default", "fs:allow-app-write", "blinko:default", "core:window:allow-center", "decorum:allow-show-snap-overlay", {"identifier": "fs:scope", "allow": [{"path": "$DOWNLOAD"}, {"path": "$DOWNLOAD/**"}]}, {"identifier": "fs:allow-write", "allow": [{"path": "$DOWNLOAD"}]}, "upload:default", "process:allow-restart", "process:default"]}